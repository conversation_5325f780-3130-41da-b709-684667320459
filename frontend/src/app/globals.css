@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Professional Color Palette */
  --primary-50: 248, 250, 252;
  --primary-100: 241, 245, 249;
  --primary-200: 226, 232, 240;
  --primary-300: 203, 213, 225;
  --primary-400: 148, 163, 184;
  --primary-500: 100, 116, 139;
  --primary-600: 71, 85, 105;
  --primary-700: 51, 65, 85;
  --primary-800: 30, 41, 59;
  --primary-900: 15, 23, 42;

  /* Elegant Accent Colors */
  --accent-50: 254, 252, 232;
  --accent-100: 254, 249, 195;
  --accent-200: 254, 240, 138;
  --accent-300: 253, 224, 71;
  --accent-400: 250, 204, 21;
  --accent-500: 234, 179, 8;
  --accent-600: 202, 138, 4;
  --accent-700: 161, 98, 7;
  --accent-800: 133, 77, 14;
  --accent-900: 113, 63, 18;

  /* Neutral Grays */
  --gray-50: 249, 250, 251;
  --gray-100: 243, 244, 246;
  --gray-200: 229, 231, 235;
  --gray-300: 209, 213, 219;
  --gray-400: 156, 163, 175;
  --gray-500: 107, 114, 128;
  --gray-600: 75, 85, 99;
  --gray-700: 55, 65, 81;
  --gray-800: 31, 41, 55;
  --gray-900: 17, 24, 39;

  /* Success and Status Colors */
  --success-500: 34, 197, 94;
  --success-600: 22, 163, 74;
  --warning-500: 245, 158, 11;
  --error-500: 239, 68, 68;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--primary-800));
  background: rgb(var(--gray-50));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

.smooth-scroll {
  scroll-behavior: smooth;
}

.glass-effect {
  backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.luxury-gradient {
  background: linear-gradient(135deg,
    rgb(var(--primary-900)) 0%,
    rgb(var(--primary-800)) 35%,
    rgb(var(--primary-700)) 100%);
}

.accent-gradient {
  background: linear-gradient(135deg,
    rgb(var(--accent-600)) 0%,
    rgb(var(--accent-500)) 50%,
    rgb(var(--accent-400)) 100%);
  box-shadow: 0 4px 20px rgba(var(--accent-500), 0.3);
}

.hero-overlay {
  background: linear-gradient(
    135deg,
    rgba(var(--primary-900), 0.95) 0%,
    rgba(var(--primary-800), 0.9) 35%,
    rgba(var(--primary-700), 0.85) 100%
  );
}

.elegant-shadow {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.elegant-shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.elegant-shadow-xl {
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Professional animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}
