@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Base Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #fafafa;
  color: #171717;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Professional Component Styles */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-accent-600 to-accent-500 text-white font-semibold px-8 py-4 rounded-xl shadow-elegant hover:shadow-elegant-lg transform hover:-translate-y-1 transition-all duration-300 ease-out;
  }

  .btn-secondary {
    @apply bg-white text-primary-800 font-semibold px-8 py-4 rounded-xl shadow-elegant hover:shadow-elegant-lg border border-neutral-200 hover:border-accent-300 transform hover:-translate-y-1 transition-all duration-300 ease-out;
  }

  .card-elegant {
    @apply bg-white rounded-2xl shadow-elegant hover:shadow-elegant-xl border border-neutral-100 transition-all duration-300 ease-out;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-accent-600 to-accent-500 bg-clip-text text-transparent;
  }
}

/* Professional Utility Classes */
@layer utilities {
  .glass-effect {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }

  .gradient-primary {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, #c05621 0%, #dd6b20 50%, #ed8936 100%);
  }

  .hero-overlay {
    background: linear-gradient(
      135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 50%,
      rgba(51, 65, 85, 0.85) 100%
    );
  }

  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* Professional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out forwards;
}

/* Stagger animations for lists */
.animate-stagger > * {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-stagger > *:nth-child(1) { animation-delay: 0.1s; }
.animate-stagger > *:nth-child(2) { animation-delay: 0.2s; }
.animate-stagger > *:nth-child(3) { animation-delay: 0.3s; }
.animate-stagger > *:nth-child(4) { animation-delay: 0.4s; }
.animate-stagger > *:nth-child(5) { animation-delay: 0.5s; }
.animate-stagger > *:nth-child(6) { animation-delay: 0.6s; }
