'use client';

import Link from 'next/link';

const HeroSection = () => {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`,
        }}
      />
      
      {/* Overlay */}
      <div className="absolute inset-0 hero-overlay" />

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-serif font-bold text-white mb-8 leading-tight tracking-tight animate-fade-in-up">
            Experience London's
            <span className="block text-accent-400 mt-2">Elite Transportation</span>
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed font-light animate-fade-in-up">
            From luxury helicopters soaring above the Thames to private jets connecting you globally,
            GoGeo Travels London delivers unparalleled comfort and sophistication in every journey.
          </p>

          {/* Service Types */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 max-w-5xl mx-auto animate-fade-in-up">
            {[
              { icon: '🚁', name: 'Helicopters', desc: 'Sky-high luxury' },
              { icon: '✈️', name: 'Private Jets', desc: 'Global connections' },
              { icon: '🚌', name: 'Executive Buses', desc: 'Group comfort' },
              { icon: '🚗', name: 'Private Cars', desc: 'Personal elegance' },
            ].map((service, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 text-center hover:bg-white/20 smooth-transition cursor-pointer hover-lift"
              >
                <div className="text-4xl mb-3">{service.icon}</div>
                <h3 className="text-white font-semibold text-base md:text-lg tracking-wide">
                  {service.name}
                </h3>
                <p className="text-white/70 text-sm md:text-base font-light">
                  {service.desc}
                </p>
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-fade-in-up">
            <Link
              href="#booking"
              className="bg-accent-gradient text-white px-12 py-5 rounded-2xl font-semibold text-xl hover-lift smooth-transition elegant-shadow-xl w-full sm:w-auto tracking-wide"
            >
              Book Your Journey
            </Link>
            <Link
              href="#fleet"
              className="glass-effect text-white px-12 py-5 rounded-2xl font-semibold text-xl hover:bg-white/20 smooth-transition w-full sm:w-auto tracking-wide"
            >
              Explore Fleet
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="mt-20 pt-10 border-t border-white/20 animate-fade-in-up">
            <p className="text-white/80 text-lg mb-6 font-light tracking-wide">Trusted by London's Elite</p>
            <div className="flex flex-wrap justify-center items-center gap-12 opacity-80">
              <div className="text-white font-medium">★★★★★ 5.0 Rating</div>
              <div className="text-white font-medium">500+ Happy Clients</div>
              <div className="text-white font-medium">24/7 Concierge</div>
              <div className="text-white font-medium">Fully Licensed & Insured</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <Link href="#fleet">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </Link>
      </div>
    </section>
  );
};

export default HeroSection;
