'use client';

import { useState, useEffect } from 'react';
import { apiService, Vehicle } from '@/services/api';
import { useCityContext } from '@/contexts/CityContext';

interface BookingFormData {
  transportMode: string; // HELICOPTER, PRIVATE_JET, BUS, PRIVATE_CAR
  vehicleId?: string;
  pickupLocation: string;
  dropoffLocation: string;
  pickupDate: string;
  pickupTime: string;
  passengers: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specialRequests?: string;
}

const BookingForm = () => {
  const { selectedCity } = useCityContext();
  const [currentStep, setCurrentStep] = useState(1);
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const [formData, setFormData] = useState<BookingFormData>({
    transportMode: '',
    vehicleId: '',
    pickupLocation: '',
    dropoffLocation: '',
    pickupDate: '',
    pickupTime: '',
    passengers: 1,
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    specialRequests: '',
  });

  useEffect(() => {
    const fetchVehicles = async () => {
      if (!selectedCity) return;

      try {
        setLoading(true);
        const vehiclesData = await apiService.getVehicles(selectedCity.slug);
        setVehicles(vehiclesData);
        // Reset vehicle selection when city changes
        setFormData(prev => ({ ...prev, vehicleId: '' }));
      } catch (error) {
        console.error('Error fetching vehicles for city:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVehicles();
  }, [selectedCity]);

  // Get available transport modes based on selected city
  const getAvailableTransportModes = () => {
    const allModes = [
      { id: 'PRIVATE_JET', name: 'Private Jet', icon: '✈️' },
      { id: 'HELICOPTER', name: 'Private Helicopter', icon: '🚁' },
      { id: 'PRIVATE_CAR', name: 'Private Car', icon: '🚗' },
      { id: 'BUS', name: 'Private Bus', icon: '🚌' },
    ];

    if (!selectedCity) return [];

    // For London, show all transport modes
    if (selectedCity.slug === 'london') {
      return allModes;
    }

    // For other cities (Manchester, Budapest, Madrid), only show cars and buses
    return allModes.filter(mode =>
      mode.id === 'PRIVATE_CAR' || mode.id === 'BUS'
    );
  };

  // Get vehicles filtered by selected transport mode
  const getVehiclesByTransportMode = () => {
    if (!formData.transportMode) return [];
    return vehicles.filter(vehicle => vehicle.category === formData.transportMode);
  };

  const handleInputChange = (field: keyof BookingFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Reset vehicle selection when transport mode changes
      if (field === 'transportMode') {
        newData.vehicleId = '';
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCity) {
      alert('Please select a city first.');
      return;
    }

    try {
      setSubmitting(true);

      const bookingData = {
        ...formData,
        city: selectedCity.slug
      };

      const response = await apiService.submitBooking(bookingData);

      alert('Booking request submitted successfully! We will contact you within 2 hours with a detailed quote.');

      // Reset form
      setFormData({
        transportMode: '',
        vehicleId: '',
        pickupLocation: '',
        dropoffLocation: '',
        pickupDate: '',
        pickupTime: '',
        passengers: 1,
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        specialRequests: '',
      });
      setCurrentStep(1);

    } catch (error) {
      console.error('Error:', error);
      alert('Error submitting booking. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const nextStep = () => {
    // Validate current step before proceeding
    if (currentStep === 1) {
      if (!formData.transportMode) {
        alert('Please select a mode of transport before proceeding.');
        return;
      }
      if (!formData.pickupLocation || !formData.dropoffLocation || !formData.pickupDate || !formData.pickupTime) {
        alert('Please fill in all required fields before proceeding.');
        return;
      }
    }

    if (currentStep < 3) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  return (
    <section id="booking" className="py-20 bg-gradient-to-br from-neutral-50 to-neutral-100">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-primary-900 mb-6">
            Book Your Journey
          </h2>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
            Experience luxury transportation tailored to your needs. Complete the form below to request your booking.
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold ${
                    step <= currentStep
                      ? 'gradient-accent text-white shadow-elegant'
                      : 'bg-neutral-200 text-neutral-600'
                  }`}
                >
                  {step}
                </div>
                {step < 3 && (
                  <div
                    className={`w-16 h-1 mx-2 ${
                      step < currentStep ? 'bg-accent-500' : 'bg-neutral-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-2xl shadow-elegant-xl p-8 border border-neutral-100">
          <form onSubmit={handleSubmit}>
            {/* Step 1: Service & Journey Details */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h3 className="text-2xl font-serif font-bold text-primary-900 mb-6">
                  Select Your Service
                </h3>

                {/* Selected City Display */}
                <div className="mb-6 p-4 bg-neutral-50 rounded-xl border border-neutral-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-primary-900">Selected City</h4>
                      <p className="text-neutral-600">{selectedCity?.name}, {selectedCity?.country}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => window.location.reload()}
                      className="text-accent-600 hover:text-accent-700 text-sm font-medium"
                    >
                      Change City
                    </button>
                  </div>
                </div>

                {/* Transport Mode Selection */}
                {!loading && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-primary-800 mb-2">
                      Select Mode of Transport *
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {getAvailableTransportModes().map((mode) => (
                        <button
                          key={mode.id}
                          type="button"
                          onClick={() => handleInputChange('transportMode', mode.id)}
                          className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                            formData.transportMode === mode.id
                              ? 'border-accent-500 bg-accent-50 text-accent-700'
                              : 'border-neutral-300 bg-white text-neutral-700 hover:border-accent-300 hover:bg-accent-50'
                          }`}
                        >
                          <div className="text-center">
                            <div className="text-2xl mb-2">{mode.icon}</div>
                            <div className="font-semibold text-sm">{mode.name}</div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Specific Vehicle Selection */}
                {formData.transportMode && getVehiclesByTransportMode().length > 0 && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-primary-800 mb-2">
                      Select Specific Vehicle (Optional)
                    </label>
                    <select
                      value={formData.vehicleId || ''}
                      onChange={(e) => handleInputChange('vehicleId', e.target.value)}
                      className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900"
                    >
                      <option value="">Any available vehicle</option>
                      {getVehiclesByTransportMode().map((vehicle) => (
                        <option key={vehicle.id} value={vehicle.id}>
                          {vehicle.name} - {vehicle.capacity} - {vehicle.priceRange}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Locations */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Pickup Location *
                    </label>
                    <input
                      type="text"
                      value={formData.pickupLocation}
                      onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      placeholder="Enter pickup address"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Drop-off Location *
                    </label>
                    <input
                      type="text"
                      value={formData.dropoffLocation}
                      onChange={(e) => handleInputChange('dropoffLocation', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      placeholder="Enter destination address"
                      required
                    />
                  </div>
                </div>

                {/* Date and Time */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Pickup Date *
                    </label>
                    <input
                      type="date"
                      value={formData.pickupDate}
                      onChange={(e) => handleInputChange('pickupDate', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Pickup Time *
                    </label>
                    <input
                      type="time"
                      value={formData.pickupTime}
                      onChange={(e) => handleInputChange('pickupTime', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Passengers *
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="50"
                      value={formData.passengers}
                      onChange={(e) => handleInputChange('passengers', parseInt(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>
                </div>



                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={nextStep}
                    className="bg-accent-gradient text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"
                  >
                    Next Step
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Personal Information */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h3 className="text-2xl font-serif font-bold text-primary-900 mb-6">
                  Personal Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-primary-700 mb-2">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-primary-700 mb-2">
                    Special Requests (Optional)
                  </label>
                  <textarea
                    value={formData.specialRequests}
                    onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500"
                    placeholder="Any special requirements, dietary restrictions, or additional services..."
                  />
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-gray-200 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all duration-300"
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    onClick={nextStep}
                    className="bg-accent-gradient text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"
                  >
                    Review Booking
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Review and Submit */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h3 className="text-2xl font-serif font-bold text-primary-900 mb-6">
                  Review Your Booking
                </h3>

                <div className="bg-primary-50 rounded-xl p-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-primary-800">Service Type</h4>
                      <p className="text-primary-600 capitalize">{formData.serviceType.replace('-', ' ')}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-primary-800">Trip Type</h4>
                      <p className="text-primary-600">{formData.isRoundTrip ? 'Round Trip' : 'One Way'}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-primary-800">Pickup Location</h4>
                      <p className="text-primary-600">
                        {londonLocations.find(l => l.id === formData.pickupLocation)?.name || formData.pickupLocation}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-primary-800">Drop-off Location</h4>
                      <p className="text-primary-600">
                        {londonLocations.find(l => l.id === formData.dropoffLocation)?.name || formData.dropoffLocation}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-primary-800">Pickup Date & Time</h4>
                      <p className="text-primary-600">{formData.pickupDate} at {formData.pickupTime}</p>
                    </div>
                    {formData.isRoundTrip && (
                      <div>
                        <h4 className="font-semibold text-primary-800">Return Date & Time</h4>
                        <p className="text-primary-600">{formData.returnDate} at {formData.returnTime}</p>
                      </div>
                    )}
                    <div>
                      <h4 className="font-semibold text-primary-800">Passengers</h4>
                      <p className="text-primary-600">{formData.passengers}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-primary-800">Contact</h4>
                      <p className="text-primary-600">{formData.firstName} {formData.lastName}</p>
                      <p className="text-primary-600">{formData.email}</p>
                      <p className="text-primary-600">{formData.phone}</p>
                    </div>
                  </div>
                  {formData.specialRequests && (
                    <div>
                      <h4 className="font-semibold text-primary-800">Special Requests</h4>
                      <p className="text-primary-600">{formData.specialRequests}</p>
                    </div>
                  )}
                </div>

                <div className="bg-accent-50 rounded-xl p-6">
                  <h4 className="font-semibold text-accent-800 mb-2">Next Steps</h4>
                  <ul className="text-accent-700 space-y-1 text-sm">
                    <li>• We'll review your booking request within 2 hours</li>
                    <li>• You'll receive a detailed quote via email</li>
                    <li>• Our team will contact you to confirm details</li>
                    <li>• Payment can be made via card, bank transfer, or cash</li>
                  </ul>
                </div>

                <div className="text-center text-sm text-primary-600">
                  By submitting this booking, you agree to our{' '}
                  <a href="#" className="text-accent-600 hover:underline">Terms of Service</a> and{' '}
                  <a href="#" className="text-accent-600 hover:underline">Privacy Policy</a>
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="bg-gray-200 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all duration-300"
                  >
                    Previous
                  </button>
                  <button
                    type="submit"
                    className="bg-accent-gradient text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"
                  >
                    Submit Booking
                  </button>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </section>
  );
};

export default BookingForm;
