'use client';

import { useState } from 'react';
import Image from 'next/image';
import { vehicleFleet } from '@/lib/vehicles';
import { VehicleType } from '@/types';

const FleetSection = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'All Vehicles', icon: '🚀' },
    { id: 'helicopter', name: 'Helicopters', icon: '🚁' },
    { id: 'private-jet', name: 'Private Jets', icon: '✈️' },
    { id: 'bus', name: 'Executive Buses', icon: '🚌' },
    { id: 'private-car', name: 'Private Cars', icon: '🚗' },
  ];

  const filteredVehicles = selectedCategory === 'all' 
    ? vehicleFleet 
    : vehicleFleet.filter(vehicle => vehicle.category === selectedCategory);

  return (
    <section id="fleet" className="py-24 bg-gradient-to-br from-gray-50 via-primary-50 to-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-20 animate-fade-in-up">
          <h2 className="text-5xl md:text-6xl font-serif font-bold text-primary-900 mb-8 tracking-tight">
            Our Premium Fleet
          </h2>
          <p className="text-xl text-primary-600 max-w-4xl mx-auto leading-relaxed font-light">
            Discover our meticulously curated collection of luxury vehicles, each designed to provide
            an unparalleled transportation experience across London and beyond.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-6 mb-16 animate-fade-in-up">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center space-x-3 px-8 py-4 rounded-2xl font-semibold smooth-transition elegant-shadow ${
                selectedCategory === category.id
                  ? 'bg-accent-gradient text-white elegant-shadow-lg hover-lift'
                  : 'bg-white text-primary-700 hover:bg-primary-50 hover:text-primary-800 hover-lift'
              }`}
            >
              <span className="text-xl">{category.icon}</span>
              <span className="tracking-wide">{category.name}</span>
            </button>
          ))}
        </div>

        {/* Vehicle Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredVehicles.map((vehicle) => (
            <VehicleCard key={vehicle.id} vehicle={vehicle} />
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-20 animate-fade-in-up">
          <div className="bg-white rounded-3xl elegant-shadow-xl p-12 max-w-5xl mx-auto border border-gray-100">
            <h3 className="text-3xl md:text-4xl font-serif font-bold text-primary-900 mb-6 tracking-tight">
              Ready to Experience Luxury?
            </h3>
            <p className="text-xl text-primary-600 mb-8 font-light leading-relaxed">
              Our fleet is available 24/7 with professional chauffeurs and pilots ready to serve you.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <a
                href="#booking"
                className="bg-accent-gradient text-white px-10 py-4 rounded-2xl font-semibold smooth-transition hover-lift elegant-shadow-lg text-lg tracking-wide"
              >
                Book Your Journey
              </a>
              <a
                href="tel:+442084326418"
                className="bg-primary-100 text-primary-700 px-10 py-4 rounded-2xl font-semibold hover:bg-primary-200 smooth-transition hover-lift text-lg tracking-wide"
              >
                Call +44 ************
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

const VehicleCard = ({ vehicle }: { vehicle: VehicleType }) => {
  return (
    <div className="bg-white rounded-3xl elegant-shadow-lg hover:elegant-shadow-xl smooth-transition overflow-hidden group hover-scale border border-gray-100">
      {/* Image */}
      <div className="relative h-72 overflow-hidden">
        <Image
          src={vehicle.image}
          alt={vehicle.name}
          fill
          className="object-cover group-hover:scale-110 smooth-transition"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        <div className="absolute top-6 right-6">
          <span className="bg-accent-gradient text-white px-4 py-2 rounded-full text-sm font-semibold elegant-shadow">
            {vehicle.capacity}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-8">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-2xl font-serif font-bold text-primary-900 tracking-tight">
            {vehicle.name}
          </h3>
          <span className="text-3xl opacity-80">
            {vehicle.category === 'helicopter' && '🚁'}
            {vehicle.category === 'private-jet' && '✈️'}
            {vehicle.category === 'bus' && '🚌'}
            {vehicle.category === 'private-car' && '🚗'}
          </span>
        </div>

        <p className="text-primary-600 mb-6 leading-relaxed font-light">
          {vehicle.description}
        </p>

        {/* Features */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-primary-800 mb-3 uppercase tracking-wider">Features</h4>
          <div className="flex flex-wrap gap-2">
            {vehicle.features.slice(0, 3).map((feature, index) => (
              <span
                key={index}
                className="bg-primary-50 text-primary-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-primary-100"
              >
                {feature}
              </span>
            ))}
            {vehicle.features.length > 3 && (
              <span className="bg-accent-50 text-accent-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-accent-100">
                +{vehicle.features.length - 3} more
              </span>
            )}
          </div>
        </div>

        {/* Price and CTA */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div>
            <p className="text-accent-600 font-bold text-xl tracking-tight">{vehicle.priceRange}</p>
          </div>
          <a
            href="#booking"
            className="bg-primary-900 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-800 smooth-transition hover-lift elegant-shadow"
          >
            Book Now
          </a>
        </div>
      </div>
    </div>
  );
};

export default FleetSection;
