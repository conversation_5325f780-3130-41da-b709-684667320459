"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FleetSection.tsx":
/*!*****************************************!*\
  !*** ./src/components/FleetSection.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CityContext */ \"(app-pages-browser)/./src/contexts/CityContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FleetSection = ()=>{\n    _s();\n    const { selectedCity } = (0,_contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__.useCityContext)();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get available categories based on selected city\n    const getAvailableCategories = ()=>{\n        const allCategories = [\n            {\n                id: 'all',\n                name: 'All Vehicles',\n                icon: '🚀'\n            },\n            {\n                id: 'HELICOPTER',\n                name: 'Helicopters',\n                icon: '🚁'\n            },\n            {\n                id: 'PRIVATE_JET',\n                name: 'Private Jets',\n                icon: '✈️'\n            },\n            {\n                id: 'BUS',\n                name: 'Executive Buses',\n                icon: '🚌'\n            },\n            {\n                id: 'PRIVATE_CAR',\n                name: 'Private Cars',\n                icon: '🚗'\n            }\n        ];\n        if (!selectedCity) return allCategories;\n        // For London, show all categories\n        if (selectedCity.slug === 'london') {\n            return allCategories;\n        }\n        // For other cities (Manchester, Budapest, Madrid), only show cars and buses\n        return allCategories.filter((cat)=>cat.id === 'all' || cat.id === 'BUS' || cat.id === 'PRIVATE_CAR');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FleetSection.useEffect\": ()=>{\n            const fetchVehicles = {\n                \"FleetSection.useEffect.fetchVehicles\": async ()=>{\n                    if (!selectedCity) return;\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const vehicleData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getVehicles(selectedCity.slug);\n                        setVehicles(vehicleData);\n                    } catch (err) {\n                        console.error('Error fetching vehicles:', err);\n                        setError('Failed to load vehicles. Please try again later.');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FleetSection.useEffect.fetchVehicles\"];\n            fetchVehicles();\n        }\n    }[\"FleetSection.useEffect\"], [\n        selectedCity\n    ]);\n    const filteredVehicles = selectedCategory === 'all' ? vehicles : vehicles.filter((vehicle)=>vehicle.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"fleet\",\n        className: \"py-24 bg-gradient-to-br from-neutral-50 via-white to-neutral-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-20 animate-fade-in-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl md:text-6xl font-serif font-bold text-primary-900 mb-8 tracking-tight\",\n                            children: [\n                                \"Our Premium Fleet\",\n                                selectedCity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block text-3xl md:text-4xl text-accent-600 mt-2\",\n                                    children: [\n                                        \"in \",\n                                        selectedCity.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed font-light\",\n                            children: [\n                                \"Discover our meticulously curated collection of luxury vehicles, each designed to provide an unparalleled transportation experience in \",\n                                (selectedCity === null || selectedCity === void 0 ? void 0 : selectedCity.name) || 'your selected city',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-6 mb-16 animate-stagger\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"flex items-center space-x-3 px-8 py-4 rounded-2xl font-semibold smooth-transition shadow-elegant \".concat(selectedCategory === category.id ? 'gradient-accent text-white shadow-elegant-lg hover-lift' : 'bg-white text-primary-800 hover:bg-neutral-50 hover:text-accent-600 hover-lift border border-neutral-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: category.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"tracking-wide\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-3xl shadow-elegant-lg overflow-hidden animate-pulse border border-neutral-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-72 bg-neutral-200\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-neutral-200 rounded mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-neutral-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-neutral-200 rounded mb-6 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-18\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-24\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 bg-neutral-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE14\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary px-8 py-3\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined) : filteredVehicles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE97\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"No vehicles found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600\",\n                            children: \"No vehicles match your current filter. Try selecting a different category.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: filteredVehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VehicleCard, {\n                            vehicle: vehicle\n                        }, vehicle.id, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-20 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-elegant shadow-elegant-xl p-12 max-w-5xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl md:text-4xl font-serif font-bold text-primary-900 mb-6 tracking-tight\",\n                                children: \"Ready to Experience Luxury?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-neutral-600 mb-8 font-light leading-relaxed\",\n                                children: \"Our fleet is available 24/7 with professional chauffeurs and pilots ready to serve you.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#booking\",\n                                        className: \"btn-primary text-lg px-10 py-4\",\n                                        children: \"Book Your Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:+442084326418\",\n                                        className: \"btn-secondary text-lg px-10 py-4\",\n                                        children: \"Call +44 ************\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FleetSection, \"ok8/PAEcdXk76+8Zy2qFPTjPwvI=\", false, function() {\n    return [\n        _contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__.useCityContext\n    ];\n});\n_c = FleetSection;\nconst VehicleCard = (param)=>{\n    let { vehicle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card-elegant hover:shadow-elegant-xl smooth-transition overflow-hidden group hover-scale\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-72 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: vehicle.image,\n                        alt: vehicle.name,\n                        fill: true,\n                        className: \"object-cover group-hover:scale-110 smooth-transition\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-primary-900/30 via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-6 right-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"gradient-accent text-white px-4 py-2 rounded-full text-sm font-semibold shadow-elegant\",\n                            children: vehicle.capacity\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 tracking-tight\",\n                                children: vehicle.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl opacity-80\",\n                                children: [\n                                    vehicle.category === 'HELICOPTER' && '🚁',\n                                    vehicle.category === 'PRIVATE_JET' && '✈️',\n                                    vehicle.category === 'BUS' && '🚌',\n                                    vehicle.category === 'PRIVATE_CAR' && '🚗'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 mb-6 leading-relaxed font-light\",\n                        children: vehicle.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-primary-800 mb-3 uppercase tracking-wider\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    vehicle.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-neutral-50 text-neutral-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-neutral-200\",\n                                            children: feature\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)),\n                                    vehicle.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-accent-50 text-accent-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-accent-200\",\n                                        children: [\n                                            \"+\",\n                                            vehicle.features.length - 3,\n                                            \" more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between pt-4 border-t border-neutral-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-accent-600 font-bold text-xl tracking-tight\",\n                                    children: vehicle.priceRange\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#booking\",\n                                className: \"btn-primary px-6 py-3 text-sm\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = VehicleCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FleetSection);\nvar _c, _c1;\n$RefreshReg$(_c, \"FleetSection\");\n$RefreshReg$(_c1, \"VehicleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FleetSection.tsx\n"));

/***/ })

});