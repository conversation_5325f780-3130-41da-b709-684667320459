"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FleetSection.tsx":
/*!*****************************************!*\
  !*** ./src/components/FleetSection.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CityContext */ \"(app-pages-browser)/./src/contexts/CityContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FleetSection = ()=>{\n    _s();\n    const { selectedCity } = (0,_contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__.useCityContext)();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: 'all',\n            name: 'All Vehicles',\n            icon: '🚀'\n        },\n        {\n            id: 'HELICOPTER',\n            name: 'Helicopters',\n            icon: '🚁'\n        },\n        {\n            id: 'PRIVATE_JET',\n            name: 'Private Jets',\n            icon: '✈️'\n        },\n        {\n            id: 'BUS',\n            name: 'Executive Buses',\n            icon: '🚌'\n        },\n        {\n            id: 'PRIVATE_CAR',\n            name: 'Private Cars',\n            icon: '🚗'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FleetSection.useEffect\": ()=>{\n            const fetchVehicles = {\n                \"FleetSection.useEffect.fetchVehicles\": async ()=>{\n                    if (!selectedCity) return;\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const vehicleData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getVehicles(selectedCity.slug);\n                        setVehicles(vehicleData);\n                    } catch (err) {\n                        console.error('Error fetching vehicles:', err);\n                        setError('Failed to load vehicles. Please try again later.');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FleetSection.useEffect.fetchVehicles\"];\n            fetchVehicles();\n        }\n    }[\"FleetSection.useEffect\"], [\n        selectedCity\n    ]);\n    const filteredVehicles = selectedCategory === 'all' ? vehicles : vehicles.filter((vehicle)=>vehicle.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"fleet\",\n        className: \"py-24 bg-gradient-to-br from-neutral-50 via-white to-neutral-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-20 animate-fade-in-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl md:text-6xl font-serif font-bold text-primary-900 mb-8 tracking-tight\",\n                            children: \"Our Premium Fleet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed font-light\",\n                            children: \"Discover our meticulously curated collection of luxury vehicles, each designed to provide an unparalleled transportation experience across London and beyond.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-6 mb-16 animate-stagger\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"flex items-center space-x-3 px-8 py-4 rounded-2xl font-semibold smooth-transition shadow-elegant \".concat(selectedCategory === category.id ? 'gradient-accent text-white shadow-elegant-lg hover-lift' : 'bg-white text-primary-800 hover:bg-neutral-50 hover:text-accent-600 hover-lift border border-neutral-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: category.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"tracking-wide\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, undefined),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-3xl shadow-elegant-lg overflow-hidden animate-pulse border border-neutral-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-72 bg-neutral-200\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-neutral-200 rounded mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-neutral-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-neutral-200 rounded mb-6 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-18\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-24\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 bg-neutral-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE14\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary px-8 py-3\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, undefined) : filteredVehicles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE97\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"No vehicles found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600\",\n                            children: \"No vehicles match your current filter. Try selecting a different category.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: filteredVehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VehicleCard, {\n                            vehicle: vehicle\n                        }, vehicle.id, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-20 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-elegant shadow-elegant-xl p-12 max-w-5xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl md:text-4xl font-serif font-bold text-primary-900 mb-6 tracking-tight\",\n                                children: \"Ready to Experience Luxury?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-neutral-600 mb-8 font-light leading-relaxed\",\n                                children: \"Our fleet is available 24/7 with professional chauffeurs and pilots ready to serve you.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#booking\",\n                                        className: \"btn-primary text-lg px-10 py-4\",\n                                        children: \"Book Your Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:+442084326418\",\n                                        className: \"btn-secondary text-lg px-10 py-4\",\n                                        children: \"Call +44 ************\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FleetSection, \"ok8/PAEcdXk76+8Zy2qFPTjPwvI=\", false, function() {\n    return [\n        _contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__.useCityContext\n    ];\n});\n_c = FleetSection;\nconst VehicleCard = (param)=>{\n    let { vehicle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card-elegant hover:shadow-elegant-xl smooth-transition overflow-hidden group hover-scale\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-72 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: vehicle.image,\n                        alt: vehicle.name,\n                        fill: true,\n                        className: \"object-cover group-hover:scale-110 smooth-transition\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-primary-900/30 via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-6 right-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"gradient-accent text-white px-4 py-2 rounded-full text-sm font-semibold shadow-elegant\",\n                            children: vehicle.capacity\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 tracking-tight\",\n                                children: vehicle.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl opacity-80\",\n                                children: [\n                                    vehicle.category === 'HELICOPTER' && '🚁',\n                                    vehicle.category === 'PRIVATE_JET' && '✈️',\n                                    vehicle.category === 'BUS' && '🚌',\n                                    vehicle.category === 'PRIVATE_CAR' && '🚗'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 mb-6 leading-relaxed font-light\",\n                        children: vehicle.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-primary-800 mb-3 uppercase tracking-wider\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    vehicle.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-neutral-50 text-neutral-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-neutral-200\",\n                                            children: feature\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)),\n                                    vehicle.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-accent-50 text-accent-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-accent-200\",\n                                        children: [\n                                            \"+\",\n                                            vehicle.features.length - 3,\n                                            \" more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between pt-4 border-t border-neutral-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-accent-600 font-bold text-xl tracking-tight\",\n                                    children: vehicle.priceRange\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#booking\",\n                                className: \"btn-primary px-6 py-3 text-sm\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = VehicleCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FleetSection);\nvar _c, _c1;\n$RefreshReg$(_c, \"FleetSection\");\n$RefreshReg$(_c1, \"VehicleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FleetSection.tsx\n"));

/***/ })

});