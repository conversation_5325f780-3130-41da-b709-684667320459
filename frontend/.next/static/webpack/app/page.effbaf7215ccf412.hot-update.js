"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/BookingForm.tsx":
/*!****************************************!*\
  !*** ./src/components/BookingForm.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _contexts_CityContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/CityContext */ \"(app-pages-browser)/./src/contexts/CityContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst BookingForm = ()=>{\n    _s();\n    const { selectedCity } = (0,_contexts_CityContext__WEBPACK_IMPORTED_MODULE_3__.useCityContext)();\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        transportMode: '',\n        vehicleId: '',\n        pickupLocation: '',\n        dropoffLocation: '',\n        pickupDate: '',\n        pickupTime: '',\n        passengers: '1',\n        firstName: '',\n        lastName: '',\n        email: '',\n        phone: '',\n        specialRequests: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BookingForm.useEffect\": ()=>{\n            const fetchVehicles = {\n                \"BookingForm.useEffect.fetchVehicles\": async ()=>{\n                    if (!selectedCity) return;\n                    try {\n                        setLoading(true);\n                        const vehiclesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getVehicles(selectedCity.slug);\n                        setVehicles(vehiclesData);\n                        // Reset vehicle selection when city changes\n                        setFormData({\n                            \"BookingForm.useEffect.fetchVehicles\": (prev)=>({\n                                    ...prev,\n                                    vehicleId: ''\n                                })\n                        }[\"BookingForm.useEffect.fetchVehicles\"]);\n                    } catch (error) {\n                        console.error('Error fetching vehicles for city:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"BookingForm.useEffect.fetchVehicles\"];\n            fetchVehicles();\n        }\n    }[\"BookingForm.useEffect\"], [\n        selectedCity\n    ]);\n    // Get available transport modes based on selected city\n    const getAvailableTransportModes = ()=>{\n        const allModes = [\n            {\n                id: 'PRIVATE_JET',\n                name: 'Private Jet',\n                icon: '✈️'\n            },\n            {\n                id: 'HELICOPTER',\n                name: 'Private Helicopter',\n                icon: '🚁'\n            },\n            {\n                id: 'PRIVATE_CAR',\n                name: 'Private Car',\n                icon: '🚗'\n            },\n            {\n                id: 'BUS',\n                name: 'Private Bus',\n                icon: '🚌'\n            }\n        ];\n        if (!selectedCity) return [];\n        // For London, show all transport modes\n        if (selectedCity.slug === 'london') {\n            return allModes;\n        }\n        // For other cities (Manchester, Budapest, Madrid), only show cars and buses\n        return allModes.filter((mode)=>mode.id === 'PRIVATE_CAR' || mode.id === 'BUS');\n    };\n    // Get vehicles filtered by selected transport mode\n    const getVehiclesByTransportMode = ()=>{\n        if (!formData.transportMode) return [];\n        return vehicles.filter((vehicle)=>vehicle.category === formData.transportMode);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>{\n            const newData = {\n                ...prev,\n                [field]: value\n            };\n            // Reset vehicle selection when transport mode changes\n            if (field === 'transportMode') {\n                newData.vehicleId = '';\n            }\n            return newData;\n        });\n        // Clear error for this field when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required field validations\n        if (!formData.transportMode) newErrors.transportMode = 'Transport mode is required';\n        if (!formData.pickupLocation.trim()) newErrors.pickupLocation = 'Pickup location is required';\n        if (!formData.dropoffLocation.trim()) newErrors.dropoffLocation = 'Drop-off location is required';\n        if (!formData.pickupDate) newErrors.pickupDate = 'Pickup date is required';\n        if (!formData.pickupTime) newErrors.pickupTime = 'Pickup time is required';\n        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';\n        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';\n        if (!formData.email.trim()) newErrors.email = 'Email is required';\n        if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';\n        // Email validation\n        if (formData.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        // Phone validation (basic)\n        if (formData.phone && !/^[\\+]?[1-9][\\d]{0,15}$/.test(formData.phone.replace(/[\\s\\-\\(\\)]/g, ''))) {\n            newErrors.phone = 'Please enter a valid phone number';\n        }\n        // Date validation (must be today or future)\n        if (formData.pickupDate) {\n            const selectedDate = new Date(formData.pickupDate);\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            if (selectedDate < today) {\n                newErrors.pickupDate = 'Pickup date cannot be in the past';\n            }\n        }\n        // Passengers validation\n        if (!formData.passengers.trim()) {\n            newErrors.passengers = 'Number of passengers is required';\n        } else {\n            const passengersNum = parseInt(formData.passengers);\n            if (isNaN(passengersNum) || passengersNum < 1) {\n                newErrors.passengers = 'Please enter a valid number of passengers (minimum 1)';\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!selectedCity) {\n            alert('Please select a city first.');\n            return;\n        }\n        // Validate form\n        if (!validateForm()) {\n            alert('Please fill in all required fields correctly.');\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const bookingData = {\n                ...formData,\n                city: selectedCity.slug\n            };\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.submitBooking(bookingData);\n            alert('Booking request submitted successfully! We will contact you within 2 hours with a detailed quote.');\n            // Reset form\n            setFormData({\n                transportMode: '',\n                vehicleId: '',\n                pickupLocation: '',\n                dropoffLocation: '',\n                pickupDate: '',\n                pickupTime: '',\n                passengers: '1',\n                firstName: '',\n                lastName: '',\n                email: '',\n                phone: '',\n                specialRequests: ''\n            });\n            setErrors({});\n        } catch (error) {\n            console.error('Error:', error);\n            alert('Error submitting booking. Please try again.');\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"booking\",\n        className: \"py-20 bg-gradient-to-br from-neutral-50 to-neutral-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-serif font-bold text-primary-900 mb-6\",\n                            children: \"Book Your Journey\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-neutral-600 max-w-2xl mx-auto\",\n                            children: \"Experience luxury transportation tailored to your needs. Complete the form below to request your booking.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-elegant-xl p-8 border border-neutral-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-neutral-50 rounded-xl border border-neutral-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-primary-900\",\n                                                    children: \"Selected City\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-600\",\n                                                    children: [\n                                                        selectedCity === null || selectedCity === void 0 ? void 0 : selectedCity.name,\n                                                        \", \",\n                                                        selectedCity === null || selectedCity === void 0 ? void 0 : selectedCity.country\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>window.location.reload(),\n                                            className: \"text-accent-600 hover:text-accent-700 text-sm font-medium\",\n                                            children: \"Change City\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined),\n                            !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Select Mode of Transport *\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: getAvailableTransportModes().map((mode)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleInputChange('transportMode', mode.id),\n                                                className: \"p-4 rounded-xl border-2 transition-all duration-200 \".concat(formData.transportMode === mode.id ? 'border-accent-500 bg-accent-50 text-accent-700' : errors.transportMode ? 'border-red-300 bg-white text-neutral-700 hover:border-red-400' : 'border-neutral-300 bg-white text-neutral-700 hover:border-accent-300 hover:bg-accent-50'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl mb-2\",\n                                                            children: mode.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-sm\",\n                                                            children: mode.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, mode.id, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    errors.transportMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-sm mt-1\",\n                                        children: errors.transportMode\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, undefined),\n                            formData.transportMode && getVehiclesByTransportMode().length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Select Specific Vehicle (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: formData.vehicleId || '',\n                                        onChange: (e)=>handleInputChange('vehicleId', e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Any available vehicle\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            getVehiclesByTransportMode().map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: vehicle.id,\n                                                    children: [\n                                                        vehicle.name,\n                                                        \" - \",\n                                                        vehicle.capacity,\n                                                        \" - \",\n                                                        vehicle.priceRange\n                                                    ]\n                                                }, vehicle.id, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                children: \"Pickup Location *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.pickupLocation,\n                                                onChange: (e)=>handleInputChange('pickupLocation', e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.pickupLocation ? 'border-red-300' : 'border-gray-300'),\n                                                placeholder: \"Enter pickup address\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            errors.pickupLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.pickupLocation\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                children: \"Drop-off Location *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.dropoffLocation,\n                                                onChange: (e)=>handleInputChange('dropoffLocation', e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.dropoffLocation ? 'border-red-300' : 'border-gray-300'),\n                                                placeholder: \"Enter destination address\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            errors.dropoffLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.dropoffLocation\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                children: \"Pickup Date *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: formData.pickupDate,\n                                                onChange: (e)=>handleInputChange('pickupDate', e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.pickupDate ? 'border-red-300' : 'border-gray-300'),\n                                                min: new Date().toISOString().split('T')[0]\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            errors.pickupDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.pickupDate\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                children: \"Pickup Time *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"time\",\n                                                value: formData.pickupTime,\n                                                onChange: (e)=>handleInputChange('pickupTime', e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.pickupTime ? 'border-red-300' : 'border-gray-300')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            errors.pickupTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.pickupTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                children: \"Passengers *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.passengers,\n                                                onChange: (e)=>handleInputChange('passengers', e.target.value),\n                                                className: \"w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.passengers ? 'border-red-300' : 'border-gray-300'),\n                                                placeholder: \"Enter number of passengers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            errors.passengers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.passengers\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-serif font-bold text-primary-900 mb-6\",\n                                        children: \"Personal Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                        children: \"First Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.firstName,\n                                                        onChange: (e)=>handleInputChange('firstName', e.target.value),\n                                                        className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.firstName ? 'border-red-300' : 'border-gray-300'),\n                                                        placeholder: \"Enter your first name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.firstName\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                        children: \"Last Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.lastName,\n                                                        onChange: (e)=>handleInputChange('lastName', e.target.value),\n                                                        className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.lastName ? 'border-red-300' : 'border-gray-300'),\n                                                        placeholder: \"Enter your last name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.lastName\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                        children: \"Email Address *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: formData.email,\n                                                        onChange: (e)=>handleInputChange('email', e.target.value),\n                                                        className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.email ? 'border-red-300' : 'border-gray-300'),\n                                                        placeholder: \"Enter your email address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.email\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                        children: \"Phone Number *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        value: formData.phone,\n                                                        onChange: (e)=>handleInputChange('phone', e.target.value),\n                                                        className: \"w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 \".concat(errors.phone ? 'border-red-300' : 'border-gray-300'),\n                                                        placeholder: \"Enter your phone number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-primary-700 mb-2\",\n                                                children: \"Special Requests (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.specialRequests,\n                                                onChange: (e)=>handleInputChange('specialRequests', e.target.value),\n                                                rows: 4,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500\",\n                                                placeholder: \"Any special requirements, dietary restrictions, or additional services...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-neutral-200 pt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-accent-50 rounded-xl p-6 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-accent-800 mb-2\",\n                                                children: \"Next Steps\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-accent-700 space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• We'll review your booking request within 2 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• You'll receive a detailed quote via email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Our team will contact you to confirm details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Payment can be made via card, bank transfer, or cash\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: submitting,\n                                            className: \"bg-accent-500 text-white px-12 py-4 rounded-lg font-semibold text-lg transition-all duration-300 shadow-lg \".concat(submitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent-600 hover:shadow-xl hover:scale-105'),\n                                            children: submitting ? 'Submitting...' : 'Submit Booking Request'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/BookingForm.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BookingForm, \"vH6Lng4KZQ+wbP/W2datq68vadc=\", false, function() {\n    return [\n        _contexts_CityContext__WEBPACK_IMPORTED_MODULE_3__.useCityContext\n    ];\n});\n_c = BookingForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BookingForm);\nvar _c;\n$RefreshReg$(_c, \"BookingForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/BookingForm.tsx\n"));

/***/ })

});