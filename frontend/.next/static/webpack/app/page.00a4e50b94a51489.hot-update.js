"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst Navigation = ()=>{\n    _s();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    const navItems = [\n        {\n            href: '#home',\n            label: 'Home'\n        },\n        {\n            href: '#fleet',\n            label: 'Our Fleet'\n        },\n        {\n            href: '#booking',\n            label: 'Book Now'\n        },\n        {\n            href: '#about',\n            label: 'About'\n        },\n        {\n            href: '#contact',\n            label: 'Contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 smooth-transition \".concat(isScrolled ? 'bg-white/95 backdrop-blur-xl shadow-elegant-lg border-b border-neutral-200' : 'bg-transparent'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-18 lg:h-22\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#home\",\n                                className: \"flex items-center space-x-3 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 gradient-accent rounded-xl flex items-center justify-center shadow-elegant group-hover:hover-lift smooth-transition\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-2xl\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-serif text-2xl font-bold tracking-tight \".concat(isScrolled ? 'text-primary-900' : 'text-white'),\n                                                children: \"GoGeo Travels\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold tracking-wider \".concat(isScrolled ? 'text-accent-600' : 'text-accent-400'),\n                                                children: \"LONDON\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-10 flex items-baseline space-x-10\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"px-4 py-3 font-semibold smooth-transition hover:hover-lift relative group \".concat(isScrolled ? 'text-primary-800 hover:text-accent-600' : 'text-white hover:text-accent-400'),\n                                        children: [\n                                            item.label,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute bottom-0 left-0 w-0 h-0.5 bg-accent-500 smooth-transition group-hover:w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#booking\",\n                                className: \"btn-primary px-8 py-3\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                className: \"p-2 rounded-lg smooth-transition \".concat(isScrolled ? 'text-primary-800 hover:bg-neutral-100' : 'text-white hover:bg-white/10'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    stroke: \"currentColor\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pt-4 pb-6 space-y-2 bg-white/98 backdrop-blur-md rounded-2xl mt-4 shadow-elegant-lg border border-neutral-200\",\n                        children: [\n                            navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"block px-4 py-3 text-primary-800 font-semibold hover:text-accent-600 smooth-transition hover:bg-neutral-50 rounded-xl\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#booking\",\n                                className: \"block w-full text-center btn-primary px-4 py-3 mt-6\",\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/Navigation.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"0+zEKVBL95ILuBb5rHE6ViYOHu8=\");\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navigation.tsx\n"));

/***/ })

});