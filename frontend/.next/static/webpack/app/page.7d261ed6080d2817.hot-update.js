"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FleetSection.tsx":
/*!*****************************************!*\
  !*** ./src/components/FleetSection.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CityContext */ \"(app-pages-browser)/./src/contexts/CityContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FleetSection = ()=>{\n    _s();\n    const { selectedCity } = (0,_contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__.useCityContext)();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get available categories based on selected city\n    const getAvailableCategories = ()=>{\n        const allCategories = [\n            {\n                id: 'all',\n                name: 'All Vehicles',\n                icon: '🚀'\n            },\n            {\n                id: 'HELICOPTER',\n                name: 'Helicopters',\n                icon: '🚁'\n            },\n            {\n                id: 'PRIVATE_JET',\n                name: 'Private Jets',\n                icon: '✈️'\n            },\n            {\n                id: 'BUS',\n                name: 'Executive Buses',\n                icon: '🚌'\n            },\n            {\n                id: 'PRIVATE_CAR',\n                name: 'Private Cars',\n                icon: '🚗'\n            }\n        ];\n        if (!selectedCity) return allCategories;\n        // For London, show all categories\n        if (selectedCity.slug === 'london') {\n            return allCategories;\n        }\n        // For other cities (Manchester, Budapest, Madrid), only show cars and buses\n        return allCategories.filter((cat)=>cat.id === 'all' || cat.id === 'BUS' || cat.id === 'PRIVATE_CAR');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FleetSection.useEffect\": ()=>{\n            const fetchVehicles = {\n                \"FleetSection.useEffect.fetchVehicles\": async ()=>{\n                    if (!selectedCity) return;\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const vehicleData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getVehicles(selectedCity.slug);\n                        setVehicles(vehicleData);\n                    } catch (err) {\n                        console.error('Error fetching vehicles:', err);\n                        setError('Failed to load vehicles. Please try again later.');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FleetSection.useEffect.fetchVehicles\"];\n            fetchVehicles();\n        }\n    }[\"FleetSection.useEffect\"], [\n        selectedCity\n    ]);\n    const filteredVehicles = selectedCategory === 'all' ? vehicles : vehicles.filter((vehicle)=>vehicle.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"fleet\",\n        className: \"py-24 bg-gradient-to-br from-neutral-50 via-white to-neutral-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-20 animate-fade-in-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl md:text-6xl font-serif font-bold text-primary-900 mb-8 tracking-tight\",\n                            children: [\n                                \"Our Premium Fleet\",\n                                selectedCity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block text-3xl md:text-4xl text-accent-600 mt-2\",\n                                    children: [\n                                        \"in \",\n                                        selectedCity.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed font-light\",\n                            children: [\n                                \"Discover our meticulously curated collection of luxury vehicles, each designed to provide an unparalleled transportation experience in \",\n                                (selectedCity === null || selectedCity === void 0 ? void 0 : selectedCity.name) || 'your selected city',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-6 mb-16 animate-stagger\",\n                    children: getAvailableCategories().map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"flex items-center space-x-3 px-8 py-4 rounded-2xl font-semibold smooth-transition shadow-elegant \".concat(selectedCategory === category.id ? 'gradient-accent text-white shadow-elegant-lg hover-lift' : 'bg-white text-primary-800 hover:bg-neutral-50 hover:text-accent-600 hover-lift border border-neutral-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: category.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"tracking-wide\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-3xl shadow-elegant-lg overflow-hidden animate-pulse border border-neutral-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-72 bg-neutral-200\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-neutral-200 rounded mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-neutral-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-neutral-200 rounded mb-6 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-18\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-neutral-200 rounded w-24\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 bg-neutral-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE14\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"btn-primary px-8 py-3\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined) : filteredVehicles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE97\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"No vehicles found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600\",\n                            children: \"No vehicles match your current filter. Try selecting a different category.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: filteredVehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VehicleCard, {\n                            vehicle: vehicle\n                        }, vehicle.id, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-20 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-elegant shadow-elegant-xl p-12 max-w-5xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl md:text-4xl font-serif font-bold text-primary-900 mb-6 tracking-tight\",\n                                children: \"Ready to Experience Luxury?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-neutral-600 mb-8 font-light leading-relaxed\",\n                                children: \"Our fleet is available 24/7 with professional chauffeurs and pilots ready to serve you.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#booking\",\n                                        className: \"btn-primary text-lg px-10 py-4\",\n                                        children: \"Book Your Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:+442084326418\",\n                                        className: \"btn-secondary text-lg px-10 py-4\",\n                                        children: \"Call +44 ************\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FleetSection, \"ok8/PAEcdXk76+8Zy2qFPTjPwvI=\", false, function() {\n    return [\n        _contexts_CityContext__WEBPACK_IMPORTED_MODULE_4__.useCityContext\n    ];\n});\n_c = FleetSection;\nconst VehicleCard = (param)=>{\n    let { vehicle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card-elegant hover:shadow-elegant-xl smooth-transition overflow-hidden group hover-scale\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-72 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: vehicle.image,\n                        alt: vehicle.name,\n                        fill: true,\n                        className: \"object-cover group-hover:scale-110 smooth-transition\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-primary-900/30 via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-6 right-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"gradient-accent text-white px-4 py-2 rounded-full text-sm font-semibold shadow-elegant\",\n                            children: vehicle.capacity\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 tracking-tight\",\n                                children: vehicle.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl opacity-80\",\n                                children: [\n                                    vehicle.category === 'HELICOPTER' && '🚁',\n                                    vehicle.category === 'PRIVATE_JET' && '✈️',\n                                    vehicle.category === 'BUS' && '🚌',\n                                    vehicle.category === 'PRIVATE_CAR' && '🚗'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 mb-6 leading-relaxed font-light\",\n                        children: vehicle.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-primary-800 mb-3 uppercase tracking-wider\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    vehicle.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-neutral-50 text-neutral-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-neutral-200\",\n                                            children: feature\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined)),\n                                    vehicle.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-accent-50 text-accent-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-accent-200\",\n                                        children: [\n                                            \"+\",\n                                            vehicle.features.length - 3,\n                                            \" more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between pt-4 border-t border-neutral-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-accent-600 font-bold text-xl tracking-tight\",\n                                    children: vehicle.priceRange\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#booking\",\n                                className: \"btn-primary px-6 py-3 text-sm\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = VehicleCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FleetSection);\nvar _c, _c1;\n$RefreshReg$(_c, \"FleetSection\");\n$RefreshReg$(_c1, \"VehicleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FleetSection.tsx\n"));

/***/ })

});