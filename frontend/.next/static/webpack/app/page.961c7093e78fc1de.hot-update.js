"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navigation */ \"(app-pages-browser)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_HeroSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/HeroSection */ \"(app-pages-browser)/./src/components/HeroSection.tsx\");\n/* harmony import */ var _components_FleetSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FleetSection */ \"(app-pages-browser)/./src/components/FleetSection.tsx\");\n/* harmony import */ var _components_AboutSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AboutSection */ \"(app-pages-browser)/./src/components/AboutSection.tsx\");\n/* harmony import */ var _components_BookingForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BookingForm */ \"(app-pages-browser)/./src/components/BookingForm.tsx\");\n/* harmony import */ var _components_ContactSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ContactSection */ \"(app-pages-browser)/./src/components/ContactSection.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_CitySelectionModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/CitySelectionModal */ \"(app-pages-browser)/./src/components/CitySelectionModal.tsx\");\n/* harmony import */ var _contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/CityContext */ \"(app-pages-browser)/./src/contexts/CityContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomeContent() {\n    _s();\n    const { selectedCity, showCityModal, setSelectedCity, setShowCityModal } = (0,_contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__.useCityContext)();\n    console.log('HomeContent render - selectedCity:', selectedCity, 'showCityModal:', showCityModal);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeroSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FleetSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BookingForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CitySelectionModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showCityModal,\n                onCitySelect: setSelectedCity\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_s(HomeContent, \"wUXaPPHsgy2N0RiVtRySUWg8a0U=\", false, function() {\n    return [\n        _contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__.useCityContext\n    ];\n});\n_c = HomeContent;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__.CityProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HomeContent, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"HomeContent\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});