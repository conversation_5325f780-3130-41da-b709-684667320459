"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/FleetSection.tsx":
/*!*****************************************!*\
  !*** ./src/components/FleetSection.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst FleetSection = ()=>{\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: 'all',\n            name: 'All Vehicles',\n            icon: '🚀'\n        },\n        {\n            id: 'HELICOPTER',\n            name: 'Helicopters',\n            icon: '🚁'\n        },\n        {\n            id: 'PRIVATE_JET',\n            name: 'Private Jets',\n            icon: '✈️'\n        },\n        {\n            id: 'BUS',\n            name: 'Executive Buses',\n            icon: '🚌'\n        },\n        {\n            id: 'PRIVATE_CAR',\n            name: 'Private Cars',\n            icon: '🚗'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FleetSection.useEffect\": ()=>{\n            const fetchVehicles = {\n                \"FleetSection.useEffect.fetchVehicles\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        // For now, we'll fetch London vehicles. Later we'll make this dynamic based on city selection\n                        const vehicleData = await _services_api__WEBPACK_IMPORTED_MODULE_3__.apiService.getVehicles('london');\n                        setVehicles(vehicleData);\n                    } catch (err) {\n                        console.error('Error fetching vehicles:', err);\n                        setError('Failed to load vehicles. Please try again later.');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"FleetSection.useEffect.fetchVehicles\"];\n            fetchVehicles();\n        }\n    }[\"FleetSection.useEffect\"], []);\n    const filteredVehicles = selectedCategory === 'all' ? vehicles : vehicles.filter((vehicle)=>vehicle.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"fleet\",\n        className: \"py-24 bg-gradient-to-br from-neutral-50 via-white to-neutral-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-20 animate-fade-in-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl md:text-6xl font-serif font-bold text-primary-900 mb-8 tracking-tight\",\n                            children: \"Our Premium Fleet\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed font-light\",\n                            children: \"Discover our meticulously curated collection of luxury vehicles, each designed to provide an unparalleled transportation experience across London and beyond.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap justify-center gap-6 mb-16 animate-stagger\",\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"flex items-center space-x-3 px-8 py-4 rounded-2xl font-semibold smooth-transition shadow-elegant \".concat(selectedCategory === category.id ? 'gradient-accent text-white shadow-elegant-lg hover-lift' : 'bg-white text-primary-800 hover:bg-neutral-50 hover:text-accent-600 hover-lift border border-neutral-200'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: category.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"tracking-wide\",\n                                    children: category.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, category.id, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        ...Array(6)\n                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-3xl elegant-shadow-lg overflow-hidden animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-72 bg-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-gray-200 rounded mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded mb-6 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-gray-200 rounded w-16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-gray-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-gray-200 rounded w-18\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 bg-gray-200 rounded w-24\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 bg-gray-200 rounded w-20\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE14\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"Oops! Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-primary-600 mb-6\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"bg-accent-gradient text-white px-8 py-3 rounded-xl font-semibold hover-lift smooth-transition\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, undefined) : filteredVehicles.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83D\\uDE97\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-serif font-bold text-primary-900 mb-4\",\n                            children: \"No vehicles found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-primary-600\",\n                            children: \"No vehicles match your current filter. Try selecting a different category.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: filteredVehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VehicleCard, {\n                            vehicle: vehicle\n                        }, vehicle.id, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-20 animate-fade-in-up\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-3xl elegant-shadow-xl p-12 max-w-5xl mx-auto border border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-3xl md:text-4xl font-serif font-bold text-primary-900 mb-6 tracking-tight\",\n                                children: \"Ready to Experience Luxury?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-primary-600 mb-8 font-light leading-relaxed\",\n                                children: \"Our fleet is available 24/7 with professional chauffeurs and pilots ready to serve you.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#booking\",\n                                        className: \"bg-accent-gradient text-white px-10 py-4 rounded-2xl font-semibold smooth-transition hover-lift elegant-shadow-lg text-lg tracking-wide\",\n                                        children: \"Book Your Journey\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:+442084326418\",\n                                        className: \"bg-primary-100 text-primary-700 px-10 py-4 rounded-2xl font-semibold hover:bg-primary-200 smooth-transition hover-lift text-lg tracking-wide\",\n                                        children: \"Call +44 ************\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FleetSection, \"4olwN+5wI2Bn8gLj6UcwvZfMg+4=\");\n_c = FleetSection;\nconst VehicleCard = (param)=>{\n    let { vehicle } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-3xl elegant-shadow-lg hover:elegant-shadow-xl smooth-transition overflow-hidden group hover-scale border border-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-72 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: vehicle.image,\n                        alt: vehicle.name,\n                        fill: true,\n                        className: \"object-cover group-hover:scale-110 smooth-transition\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-6 right-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-accent-gradient text-white px-4 py-2 rounded-full text-sm font-semibold elegant-shadow\",\n                            children: vehicle.capacity\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 tracking-tight\",\n                                children: vehicle.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl opacity-80\",\n                                children: [\n                                    vehicle.category === 'HELICOPTER' && '🚁',\n                                    vehicle.category === 'PRIVATE_JET' && '✈️',\n                                    vehicle.category === 'BUS' && '🚌',\n                                    vehicle.category === 'PRIVATE_CAR' && '🚗'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-primary-600 mb-6 leading-relaxed font-light\",\n                        children: vehicle.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-primary-800 mb-3 uppercase tracking-wider\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    vehicle.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-primary-50 text-primary-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-primary-100\",\n                                            children: feature\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined)),\n                                    vehicle.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-accent-50 text-accent-700 px-3 py-1.5 rounded-lg text-xs font-medium border border-accent-100\",\n                                        children: [\n                                            \"+\",\n                                            vehicle.features.length - 3,\n                                            \" more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between pt-4 border-t border-gray-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-accent-600 font-bold text-xl tracking-tight\",\n                                    children: vehicle.priceRange\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#booking\",\n                                className: \"bg-primary-900 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-800 smooth-transition hover-lift elegant-shadow\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/FleetSection.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = VehicleCard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FleetSection);\nvar _c, _c1;\n$RefreshReg$(_c, \"FleetSection\");\n$RefreshReg$(_c1, \"VehicleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FleetSection.tsx\n"));

/***/ })

});