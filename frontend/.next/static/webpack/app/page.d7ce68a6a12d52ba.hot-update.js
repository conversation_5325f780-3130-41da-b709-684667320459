"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/contexts/CityContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/CityContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CityProvider: () => (/* binding */ CityProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useCityContext: () => (/* binding */ useCityContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useCityContext,CityProvider,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst CityContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useCityContext = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CityContext);\n    if (context === undefined) {\n        throw new Error('useCityContext must be used within a CityProvider');\n    }\n    return context;\n};\n_s(useCityContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst CityProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [selectedCity, setSelectedCityState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCityModal, setShowCityModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CityProvider.useEffect\": ()=>{\n            // Clear localStorage for testing - REMOVE THIS LATER\n            localStorage.removeItem('selectedCity');\n            // Check if user has previously selected a city\n            const savedCity = localStorage.getItem('selectedCity');\n            console.log('Saved city from localStorage:', savedCity);\n            if (savedCity) {\n                try {\n                    const city = JSON.parse(savedCity);\n                    console.log('Parsed saved city:', city);\n                    setSelectedCityState(city);\n                } catch (error) {\n                    console.error('Error parsing saved city:', error);\n                    console.log('Showing city modal due to parse error');\n                    setShowCityModal(true);\n                }\n            } else {\n                // Show city selection modal for new users\n                console.log('No saved city, showing modal');\n                setShowCityModal(true);\n            }\n        }\n    }[\"CityProvider.useEffect\"], []);\n    const setSelectedCity = (city)=>{\n        setSelectedCityState(city);\n        localStorage.setItem('selectedCity', JSON.stringify(city));\n        setShowCityModal(false);\n    };\n    const value = {\n        selectedCity,\n        setSelectedCity,\n        showCityModal,\n        setShowCityModal\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CityContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/contexts/CityContext.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CityProvider, \"mSrtgSn6StZ4QbCoDtF3Pseh0Pc=\");\n_c = CityProvider;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CityContext);\nvar _c;\n$RefreshReg$(_c, \"CityProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/CityContext.tsx\n"));

/***/ })

});