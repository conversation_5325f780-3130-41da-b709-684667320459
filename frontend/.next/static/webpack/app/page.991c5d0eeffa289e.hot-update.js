"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst HeroSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20\",\n                style: {\n                    backgroundImage: \"url('https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')\"\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-primary-900/95 via-primary-800/90 to-primary-700/85\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl lg:text-8xl font-serif font-bold text-white mb-8 leading-tight tracking-tight animate-fade-in-up text-shadow\",\n                            children: [\n                                \"Experience London's\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block text-gradient mt-2\",\n                                    children: \"Elite Transportation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-white/95 mb-12 max-w-4xl mx-auto leading-relaxed font-light animate-fade-in-up\",\n                            children: \"From luxury helicopters soaring above the Thames to private jets connecting you globally, GoGeo Travels London delivers unparalleled comfort and sophistication in every journey.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 max-w-6xl mx-auto animate-stagger\",\n                            children: [\n                                {\n                                    icon: '🚁',\n                                    name: 'Helicopters',\n                                    desc: 'Sky-high luxury'\n                                },\n                                {\n                                    icon: '✈️',\n                                    name: 'Private Jets',\n                                    desc: 'Global connections'\n                                },\n                                {\n                                    icon: '🚌',\n                                    name: 'Executive Buses',\n                                    desc: 'Group comfort'\n                                },\n                                {\n                                    icon: '🚗',\n                                    name: 'Private Cars',\n                                    desc: 'Personal elegance'\n                                }\n                            ].map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-effect rounded-3xl p-8 text-center hover:bg-white/15 smooth-transition cursor-pointer hover-lift border border-white/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-5xl mb-4\",\n                                            children: service.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-white font-bold text-lg md:text-xl tracking-wide mb-2\",\n                                            children: service.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm md:text-base font-light\",\n                                            children: service.desc\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-8 justify-center items-center animate-fade-in-up\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"#booking\",\n                                    className: \"btn-primary text-xl px-12 py-5 w-full sm:w-auto\",\n                                    children: \"Book Your Journey\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"#fleet\",\n                                    className: \"btn-secondary text-xl px-12 py-5 w-full sm:w-auto bg-white/10 text-white border-white/20 hover:bg-white/20\",\n                                    children: \"Explore Fleet\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-24 pt-12 border-t border-white/20 animate-fade-in-up\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/90 text-xl mb-8 font-light tracking-wide\",\n                                    children: \"Trusted by London's Elite\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-400 text-2xl font-bold mb-1\",\n                                                    children: \"★★★★★\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/90 font-semibold\",\n                                                    children: \"5.0 Rating\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-400 text-2xl font-bold mb-1\",\n                                                    children: \"500+\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/90 font-semibold\",\n                                                    children: \"Happy Clients\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-400 text-2xl font-bold mb-1\",\n                                                    children: \"24/7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/90 font-semibold\",\n                                                    children: \"Concierge\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-accent-400 text-2xl font-bold mb-1\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white/90 font-semibold\",\n                                                    children: \"Licensed & Insured\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"#fleet\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/components/HeroSection.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeroSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroSection);\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HeroSection.tsx\n"));

/***/ })

});