"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    var _cities_find, _cities_find1;\n    _s();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginEmail, setLoginEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginPassword, setLoginPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCity, setSelectedCity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('london');\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bookings, setBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bookings');\n    // Fleet management state\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fleetForm, setFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        category: 'PRIVATE_CAR',\n        capacity: '',\n        description: '',\n        features: '',\n        image: '',\n        priceRange: '',\n        isActive: true\n    });\n    const [submittingFleet, setSubmittingFleet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingVehicle, setEditingVehicle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFleetForm, setShowFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Status update functionality\n    const updateBookingStatus = async (bookingId, newStatus)=>{\n        try {\n            const response = await fetch(\"http://localhost:5000/api/booking/\".concat(bookingId, \"/status\"), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                // Update the booking in the local state\n                setBookings((prev)=>prev.map((booking)=>booking.id === bookingId ? {\n                            ...booking,\n                            status: newStatus\n                        } : booking));\n            } else {\n                alert('Failed to update booking status');\n            }\n        } catch (error) {\n            console.error('Error updating status:', error);\n            alert('Error updating booking status');\n        }\n    };\n    // Fetch vehicles for selected city\n    const fetchVehicles = async ()=>{\n        if (!isAuthenticated || !selectedCity) return;\n        try {\n            const response = await fetch(\"http://localhost:5000/api/vehicles?city=\".concat(selectedCity));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.data) {\n                    setVehicles(data.data);\n                }\n            } else {\n                console.error('Failed to fetch vehicles:', response.status);\n            }\n        } catch (error) {\n            console.error('Error fetching vehicles:', error);\n        }\n    };\n    // Fleet management functions\n    const handleFleetSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmittingFleet(true);\n        try {\n            const selectedCityData = cities.find((c)=>c.slug === selectedCity);\n            if (!selectedCityData) {\n                alert('Please select a city first');\n                return;\n            }\n            const fleetData = {\n                ...fleetForm,\n                features: fleetForm.features.split(',').map((f)=>f.trim()).filter((f)=>f),\n                cityId: selectedCityData.id\n            };\n            const url = editingVehicle ? \"http://localhost:5000/api/vehicles/\".concat(editingVehicle) : 'http://localhost:5000/api/vehicles';\n            const method = editingVehicle ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(fleetData)\n            });\n            if (response.ok) {\n                alert(\"Vehicle \".concat(editingVehicle ? 'updated' : 'added', \" successfully!\"));\n                resetFleetForm();\n                // Refresh data\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n                fetchVehicles();\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to \".concat(editingVehicle ? 'update' : 'add', \" vehicle: \").concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error adding vehicle:', error);\n            alert('Error adding vehicle. Please try again.');\n        } finally{\n            setSubmittingFleet(false);\n        }\n    };\n    const resetFleetForm = ()=>{\n        setFleetForm({\n            name: '',\n            category: 'PRIVATE_CAR',\n            capacity: '',\n            description: '',\n            features: '',\n            image: '',\n            priceRange: '',\n            isActive: true\n        });\n        setEditingVehicle(null);\n        setShowFleetForm(false);\n    };\n    const handleEditVehicle = (vehicle)=>{\n        setFleetForm({\n            name: vehicle.name,\n            category: vehicle.category,\n            capacity: vehicle.capacity,\n            description: vehicle.description,\n            features: vehicle.features.join(', '),\n            image: vehicle.image || '',\n            priceRange: vehicle.priceRange,\n            isActive: vehicle.isActive\n        });\n        setEditingVehicle(vehicle.id);\n        setShowFleetForm(true);\n    };\n    const handleDeleteVehicle = async (vehicleId)=>{\n        if (!confirm('Are you sure you want to delete this vehicle?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"http://localhost:5000/api/vehicles/\".concat(vehicleId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                alert('Vehicle deleted successfully!');\n                // Refresh data\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n                fetchVehicles();\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to delete vehicle: \".concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error deleting vehicle:', error);\n            alert('Error deleting vehicle. Please try again.');\n        }\n    };\n    // Check if user is already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const authStatus = localStorage.getItem('adminAuthenticated');\n            if (authStatus === 'true') {\n                setIsAuthenticated(true);\n            }\n        }\n    }[\"AdminPage.useEffect\"], []);\n    const handleLogin = (e)=>{\n        e.preventDefault();\n        setLoginError('');\n        if (loginEmail === '<EMAIL>' && loginPassword === 'admin') {\n            setIsAuthenticated(true);\n            localStorage.setItem('adminAuthenticated', 'true');\n            setLoginEmail('');\n            setLoginPassword('');\n        } else {\n            setLoginError('Invalid email or password');\n        }\n    };\n    const handleLogout = ()=>{\n        setIsAuthenticated(false);\n        localStorage.removeItem('adminAuthenticated');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const fetchCities = {\n                \"AdminPage.useEffect.fetchCities\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/api/cities');\n                        const data = await response.json();\n                        if (data.success && data.data) {\n                            setCities(data.data);\n                        } else {\n                            setError('Failed to load cities');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching cities:', error);\n                        setError('Failed to load cities');\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchCities\"];\n            fetchCities();\n        }\n    }[\"AdminPage.useEffect\"], [\n        isAuthenticated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const fetchBookings = {\n                \"AdminPage.useEffect.fetchBookings\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"\".concat(\"http://localhost:5000/api\", \"/booking?city=\").concat(selectedCity));\n                        const data = await response.json();\n                        if (data.success) {\n                            setBookings(data.data);\n                        } else {\n                            setError('Failed to load bookings');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching bookings:', error);\n                        setError('Failed to load bookings');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchBookings\"];\n            if (selectedCity) {\n                fetchBookings();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity\n    ]);\n    // Separate useEffect for vehicles\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (isAuthenticated && selectedCity) {\n                fetchVehicles();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity,\n        isAuthenticated\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'PENDING':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'CONFIRMED':\n                return 'bg-blue-100 text-blue-800';\n            case 'IN_PROGRESS':\n                return 'bg-purple-100 text-purple-800';\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'CANCELLED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-GB', {\n            day: '2-digit',\n            month: 'short',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // Show login form if not authenticated\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-elegant-xl p-8 w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-accent-gradient rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-2xl\",\n                                    children: \"G\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 mb-2\",\n                                children: \"Admin Portal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-600\",\n                                children: \"GoGeo Travels London\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleLogin,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Email Address\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: loginEmail,\n                                        onChange: (e)=>setLoginEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: loginPassword,\n                                        onChange: (e)=>setLoginPassword(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"Enter your password\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\",\n                                children: loginError\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"w-full bg-accent-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-accent-600 transition-colors duration-200\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-neutral-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-lg border-b border-neutral-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-accent-gradient rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-serif font-bold text-primary-900\",\n                                                children: \"GoGeo Travels\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-primary-600\",\n                                                children: \"Admin Portal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-primary-700\",\n                                        children: \"Welcome, Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"text-sm text-accent-600 hover:text-accent-700 font-medium\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"w-64 bg-white shadow-lg h-screen sticky top-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-neutral-100 rounded-lg p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('bookings'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'bookings' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('fleet'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'fleet' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Fleet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-primary-900 mb-6\",\n                                    children: \"Cities\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: cities.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-600 text-sm\",\n                                        children: \"Loading cities...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, this) : cities.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedCity(city.slug),\n                                            className: \"w-full text-left px-4 py-3 rounded-xl transition-colors \".concat(selectedCity === city.slug ? 'bg-accent-500 text-white shadow-md' : 'text-primary-700 hover:bg-primary-50 hover:text-primary-900'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: city.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm opacity-75\",\n                                                        children: [\n                                                            city._count.vehicles,\n                                                            \" vehicles\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, city.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto\",\n                            children: activeTab === 'bookings' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                                children: [\n                                                    \"Booking Requests - \",\n                                                    (_cities_find = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find === void 0 ? void 0 : _cities_find.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600\",\n                                                children: \"Manage and track all booking requests for this city\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600 mt-4\",\n                                                children: \"Loading bookings...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-500 text-xl mb-4\",\n                                                children: \"⚠️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this) : bookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-primary-900 mb-2\",\n                                                children: \"No bookings found\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600\",\n                                                children: \"No booking requests for this city yet.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-3xl elegant-shadow-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Customer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Journey\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Vehicle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Date & Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                        children: bookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"hover:bg-gray-50 smooth-transition\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold text-primary-900\",\n                                                                                    children: [\n                                                                                        booking.firstName,\n                                                                                        \" \",\n                                                                                        booking.lastName\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 524,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-primary-600\",\n                                                                                    children: booking.email\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-primary-600\",\n                                                                                    children: booking.phone\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"From: \",\n                                                                                        booking.pickupLocation\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"To: \",\n                                                                                        booking.dropoffLocation\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600 mt-1\",\n                                                                                    children: [\n                                                                                        booking.passengers,\n                                                                                        \" passenger\",\n                                                                                        booking.passengers > 1 ? 's' : ''\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: booking.vehicle ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: booking.vehicle.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 544,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-primary-600\",\n                                                                                        children: booking.vehicle.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-primary-500\",\n                                                                                children: \"Not specified\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: new Date(booking.pickupDate).toLocaleDateString('en-GB')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600\",\n                                                                                    children: booking.pickupTime\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: booking.status,\n                                                                            onChange: (e)=>updateBookingStatus(booking.id, e.target.value),\n                                                                            className: \"px-3 py-1 rounded-full text-xs font-semibold border-0 focus:ring-2 focus:ring-accent-500 \".concat(getStatusColor(booking.status)),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"PENDING\",\n                                                                                    children: \"PENDING\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 564,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"CONFIRMED\",\n                                                                                    children: \"CONFIRMED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"IN_PROGRESS\",\n                                                                                    children: \"IN_PROGRESS\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"COMPLETED\",\n                                                                                    children: \"COMPLETED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 567,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"CANCELLED\",\n                                                                                    children: \"CANCELLED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-600\",\n                                                                        children: formatDate(booking.createdAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, booking.id, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                                            children: [\n                                                                \"Fleet Management - \",\n                                                                (_cities_find1 = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find1 === void 0 ? void 0 : _cities_find1.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-primary-600\",\n                                                            children: \"Manage premium vehicles for this city\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFleetForm(!showFleetForm),\n                                                    className: \"bg-accent-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-accent-600 transition-colors\",\n                                                    children: showFleetForm ? 'Cancel' : 'Add New Vehicle'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-lg mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-neutral-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-primary-900\",\n                                                    children: \"Current Fleet\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full divide-y divide-neutral-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-neutral-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Vehicle\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Capacity\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Price Range\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-neutral-200\",\n                                                            children: [\n                                                                vehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-neutral-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        vehicle.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                            className: \"h-10 w-10 rounded-lg object-cover mr-3\",\n                                                                                            src: vehicle.image,\n                                                                                            alt: vehicle.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                            lineNumber: 638,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-medium text-primary-900\",\n                                                                                                    children: vehicle.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                                    lineNumber: 641,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm text-primary-600 truncate max-w-xs\",\n                                                                                                    children: vehicle.description\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                                    lineNumber: 642,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 636,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800\",\n                                                                                    children: vehicle.category.replace('_', ' ')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 647,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-900\",\n                                                                                children: vehicle.capacity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 651,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-900\",\n                                                                                children: vehicle.priceRange\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(vehicle.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                                    children: vehicle.isActive ? 'Active' : 'Inactive'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 658,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleEditVehicle(vehicle),\n                                                                                        className: \"text-accent-600 hover:text-accent-900 mr-3\",\n                                                                                        children: \"Edit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 667,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteVehicle(vehicle.id),\n                                                                                        className: \"text-red-600 hover:text-red-900\",\n                                                                                        children: \"Delete\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 673,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, vehicle.id, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 27\n                                                                    }, this)),\n                                                                vehicles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        colSpan: 6,\n                                                                        className: \"px-6 py-8 text-center text-primary-600\",\n                                                                        children: \"No vehicles found for this city. Add your first vehicle to get started.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, this),\n                                    (showFleetForm || true) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-lg p-8 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-primary-900\",\n                                                        children: editingVehicle ? 'Edit Vehicle' : 'Add New Vehicle'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-primary-600 mt-1\",\n                                                        children: editingVehicle ? 'Update vehicle information' : 'Add a new vehicle to the fleet'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleFleetSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Vehicle Name *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.name,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    name: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., Mercedes S-Class\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 711,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 707,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Vehicle Category *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 722,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: fleetForm.category,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    category: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        required: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"PRIVATE_CAR\",\n                                                                                children: \"Private Car\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 731,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"BUS\",\n                                                                                children: \"Private Bus\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 732,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            selectedCity === 'london' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"HELICOPTER\",\n                                                                                        children: \"Helicopter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 735,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"PRIVATE_JET\",\n                                                                                        children: \"Private Jet\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 736,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 721,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Capacity *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.capacity,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    capacity: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., 4 passengers\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 746,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Price Range *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.priceRange,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    priceRange: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., \\xa3200-300/hour\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Description *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: fleetForm.description,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            description: e.target.value\n                                                                        })),\n                                                                rows: 4,\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"Describe the vehicle and its luxury features...\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Features (comma-separated)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 786,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: fleetForm.features,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            features: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"e.g., Leather seats, WiFi, Champagne service, Professional chauffeur\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Image URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 799,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"url\",\n                                                                value: fleetForm.image,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            image: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"https://example.com/vehicle-image.jpg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 802,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                id: \"isActive\",\n                                                                checked: fleetForm.isActive,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            isActive: e.target.checked\n                                                                        })),\n                                                                className: \"h-4 w-4 text-accent-600 focus:ring-accent-500 border-neutral-300 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"isActive\",\n                                                                className: \"ml-2 block text-sm text-primary-800\",\n                                                                children: \"Vehicle is active and available for booking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: resetFleetForm,\n                                                                className: \"bg-neutral-200 text-neutral-700 px-6 py-3 rounded-lg font-semibold hover:bg-neutral-300 transition-colors\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"submit\",\n                                                                disabled: submittingFleet,\n                                                                className: \"bg-accent-500 text-white px-8 py-3 rounded-lg font-semibold transition-colors \".concat(submittingFleet ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent-600'),\n                                                                children: submittingFleet ? editingVehicle ? 'Updating...' : 'Adding...' : editingVehicle ? 'Update Vehicle' : 'Add Vehicle'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"OJE2clIpiDyqZBOo1Ow9VbcafA4=\");\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0QztBQUNNO0FBNEJuQyxTQUFTRztRQXNiZ0JDLGNBc0hJQTs7SUEzaUIxQyxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ08sWUFBWUMsY0FBYyxHQUFHUiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNTLGVBQWVDLGlCQUFpQixHQUFHViwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNXLFlBQVlDLGNBQWMsR0FBR1osK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYSxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDSSxRQUFRVyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFTLEVBQUU7SUFDL0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQW1CLEVBQUU7SUFDN0QsTUFBTSxDQUFDa0IsU0FBU0MsV0FBVyxHQUFHbkIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDb0IsT0FBT0MsU0FBUyxHQUFHckIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3NCLFdBQVdDLGFBQWEsR0FBR3ZCLCtDQUFRQSxDQUF1QjtJQUVqRSx5QkFBeUI7SUFDekIsTUFBTSxDQUFDd0IsVUFBVUMsWUFBWSxHQUFHekIsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUczQiwrQ0FBUUEsQ0FBQztRQUN6QzRCLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHckMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDc0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUN3QyxlQUFlQyxpQkFBaUIsR0FBR3pDLCtDQUFRQSxDQUFDO0lBRW5ELDhCQUE4QjtJQUM5QixNQUFNMEMsc0JBQXNCLE9BQU9DLFdBQW1CQztRQUNwRCxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLHFDQUErQyxPQUFWSCxXQUFVLFlBQVU7Z0JBQ3BGSSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsUUFBUVI7Z0JBQVU7WUFDM0M7WUFFQSxJQUFJQyxTQUFTUSxFQUFFLEVBQUU7Z0JBQ2Ysd0NBQXdDO2dCQUN4Q3BDLFlBQVlxQyxDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLFVBQzNCQSxRQUFRQyxFQUFFLEtBQUtkLFlBQ1g7NEJBQUUsR0FBR2EsT0FBTzs0QkFBRUosUUFBUVI7d0JBQWlCLElBQ3ZDWTtZQUVSLE9BQU87Z0JBQ0xFLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT3RDLE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q3NDLE1BQU07UUFDUjtJQUNGO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU1FLGdCQUFnQjtRQUNwQixJQUFJLENBQUN2RCxtQkFBbUIsQ0FBQ1EsY0FBYztRQUV2QyxJQUFJO1lBQ0YsTUFBTWdDLFdBQVcsTUFBTUMsTUFBTSwyQ0FBd0QsT0FBYmpDO1lBQ3hFLElBQUlnQyxTQUFTUSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTVEsT0FBTyxNQUFNaEIsU0FBU2lCLElBQUk7Z0JBQ2hDLElBQUlELEtBQUtFLE9BQU8sSUFBSUYsS0FBS0EsSUFBSSxFQUFFO29CQUM3QnBDLFlBQVlvQyxLQUFLQSxJQUFJO2dCQUN2QjtZQUNGLE9BQU87Z0JBQ0xGLFFBQVF2QyxLQUFLLENBQUMsNkJBQTZCeUIsU0FBU08sTUFBTTtZQUM1RDtRQUNGLEVBQUUsT0FBT2hDLE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsNEJBQTRCQTtRQUM1QztJQUNGO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU00QyxvQkFBb0IsT0FBT0M7UUFDL0JBLEVBQUVDLGNBQWM7UUFDaEI3QixtQkFBbUI7UUFFbkIsSUFBSTtZQUNGLE1BQU04QixtQkFBbUIvRCxPQUFPZ0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUt6RDtZQUNyRCxJQUFJLENBQUNzRCxrQkFBa0I7Z0JBQ3JCVCxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxNQUFNYSxZQUFZO2dCQUNoQixHQUFHN0MsU0FBUztnQkFDWk0sVUFBVU4sVUFBVU0sUUFBUSxDQUFDd0MsS0FBSyxDQUFDLEtBQUtqQixHQUFHLENBQUNrQixDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLElBQUlDLE1BQU0sQ0FBQ0YsQ0FBQUEsSUFBS0E7Z0JBQ3ZFRyxRQUFRVCxpQkFBaUJWLEVBQUU7WUFDN0I7WUFFQSxNQUFNb0IsTUFBTXZDLGlCQUNSLHNDQUFxRCxPQUFmQSxrQkFDdEM7WUFFSixNQUFNUyxTQUFTVCxpQkFBaUIsUUFBUTtZQUV4QyxNQUFNTyxXQUFXLE1BQU1DLE1BQU0rQixLQUFLO2dCQUNoQzlCO2dCQUNBQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ29CO1lBQ3ZCO1lBRUEsSUFBSTFCLFNBQVNRLEVBQUUsRUFBRTtnQkFDZkssTUFBTSxXQUFnRCxPQUFyQ3BCLGlCQUFpQixZQUFZLFNBQVE7Z0JBQ3REd0M7Z0JBQ0EsZUFBZTtnQkFDZixNQUFNQyxhQUFhLE1BQU03RSxxREFBVUEsQ0FBQzhFLFNBQVM7Z0JBQzdDakUsVUFBVWdFO2dCQUNWbkI7WUFDRixPQUFPO2dCQUNMLE1BQU1xQixZQUFZLE1BQU1wQyxTQUFTaUIsSUFBSTtnQkFDckNKLE1BQU0sYUFBMkR1QixPQUE5QzNDLGlCQUFpQixXQUFXLE9BQU0sY0FBaUQsT0FBckMyQyxVQUFVQyxPQUFPLElBQUk7WUFDeEY7UUFDRixFQUFFLE9BQU85RCxPQUFPO1lBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNzQyxNQUFNO1FBQ1IsU0FBVTtZQUNSckIsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNeUMsaUJBQWlCO1FBQ3JCbkQsYUFBYTtZQUNYQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjtRQUNBSSxrQkFBa0I7UUFDbEJFLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0wQyxvQkFBb0IsQ0FBQ0M7UUFDekJ6RCxhQUFhO1lBQ1hDLE1BQU13RCxRQUFReEQsSUFBSTtZQUNsQkMsVUFBVXVELFFBQVF2RCxRQUFRO1lBQzFCQyxVQUFVc0QsUUFBUXRELFFBQVE7WUFDMUJDLGFBQWFxRCxRQUFRckQsV0FBVztZQUNoQ0MsVUFBVW9ELFFBQVFwRCxRQUFRLENBQUNxRCxJQUFJLENBQUM7WUFDaENwRCxPQUFPbUQsUUFBUW5ELEtBQUssSUFBSTtZQUN4QkMsWUFBWWtELFFBQVFsRCxVQUFVO1lBQzlCQyxVQUFVaUQsUUFBUWpELFFBQVE7UUFDNUI7UUFDQUksa0JBQWtCNkMsUUFBUTNCLEVBQUU7UUFDNUJoQixpQkFBaUI7SUFDbkI7SUFFQSxNQUFNNkMsc0JBQXNCLE9BQU9DO1FBQ2pDLElBQUksQ0FBQ0MsUUFBUSxrREFBa0Q7WUFDN0Q7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNM0MsV0FBVyxNQUFNQyxNQUFNLHNDQUFnRCxPQUFWeUMsWUFBYTtnQkFDOUV4QyxRQUFRO1lBQ1Y7WUFFQSxJQUFJRixTQUFTUSxFQUFFLEVBQUU7Z0JBQ2ZLLE1BQU07Z0JBQ04sZUFBZTtnQkFDZixNQUFNcUIsYUFBYSxNQUFNN0UscURBQVVBLENBQUM4RSxTQUFTO2dCQUM3Q2pFLFVBQVVnRTtnQkFDVm5CO1lBQ0YsT0FBTztnQkFDTCxNQUFNcUIsWUFBWSxNQUFNcEMsU0FBU2lCLElBQUk7Z0JBQ3JDSixNQUFNLDZCQUFrRSxPQUFyQ3VCLFVBQVVDLE9BQU8sSUFBSTtZQUMxRDtRQUNGLEVBQUUsT0FBTzlELE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q3NDLE1BQU07UUFDUjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDekQsZ0RBQVNBOytCQUFDO1lBQ1IsTUFBTXdGLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztZQUN4QyxJQUFJRixlQUFlLFFBQVE7Z0JBQ3pCbkYsbUJBQW1CO1lBQ3JCO1FBQ0Y7OEJBQUcsRUFBRTtJQUVMLE1BQU1zRixjQUFjLENBQUMzQjtRQUNuQkEsRUFBRUMsY0FBYztRQUNoQnRELGNBQWM7UUFFZCxJQUFJTCxlQUFlLDRCQUE0QkUsa0JBQWtCLFNBQVM7WUFDeEVILG1CQUFtQjtZQUNuQm9GLGFBQWFHLE9BQU8sQ0FBQyxzQkFBc0I7WUFDM0NyRixjQUFjO1lBQ2RFLGlCQUFpQjtRQUNuQixPQUFPO1lBQ0xFLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU1rRixlQUFlO1FBQ25CeEYsbUJBQW1CO1FBQ25Cb0YsYUFBYUssVUFBVSxDQUFDO0lBQzFCO0lBRUE5RixnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUNJLGlCQUFpQjtZQUV0QixNQUFNMkY7bURBQWM7b0JBQ2xCLElBQUk7d0JBQ0YsTUFBTW5ELFdBQVcsTUFBTUMsTUFBTTt3QkFDN0IsTUFBTWUsT0FBTyxNQUFNaEIsU0FBU2lCLElBQUk7d0JBRWhDLElBQUlELEtBQUtFLE9BQU8sSUFBSUYsS0FBS0EsSUFBSSxFQUFFOzRCQUM3QjlDLFVBQVU4QyxLQUFLQSxJQUFJO3dCQUNyQixPQUFPOzRCQUNMeEMsU0FBUzt3QkFDWDtvQkFDRixFQUFFLE9BQU9ELE9BQU87d0JBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLDBCQUEwQkE7d0JBQ3hDQyxTQUFTO29CQUNYO2dCQUNGOztZQUVBMkU7UUFDRjs4QkFBRztRQUFDM0Y7S0FBZ0I7SUFFcEJKLGdEQUFTQTsrQkFBQztZQUNSLE1BQU1nRztxREFBZ0I7b0JBQ3BCLElBQUk7d0JBQ0Y5RSxXQUFXO3dCQUNYRSxTQUFTO3dCQUVULE1BQU13QixXQUFXLE1BQU1DLE1BQU0sR0FBbURqQyxPQUFoRHFGLDJCQUErQixFQUFDLGtCQUE2QixPQUFickY7d0JBQ2hGLE1BQU1nRCxPQUFPLE1BQU1oQixTQUFTaUIsSUFBSTt3QkFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFOzRCQUNoQjlDLFlBQVk0QyxLQUFLQSxJQUFJO3dCQUN2QixPQUFPOzRCQUNMeEMsU0FBUzt3QkFDWDtvQkFDRixFQUFFLE9BQU9ELE9BQU87d0JBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLDRCQUE0QkE7d0JBQzFDQyxTQUFTO29CQUNYLFNBQVU7d0JBQ1JGLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUEsSUFBSU4sY0FBYztnQkFDaEJvRjtZQUNGO1FBQ0Y7OEJBQUc7UUFBQ3BGO0tBQWE7SUFFakIsa0NBQWtDO0lBQ2xDWixnREFBU0E7K0JBQUM7WUFDUixJQUFJSSxtQkFBbUJRLGNBQWM7Z0JBQ25DK0M7WUFDRjtRQUNGOzhCQUFHO1FBQUMvQztRQUFjUjtLQUFnQjtJQUVsQyxNQUFNZ0csaUJBQWlCLENBQUNqRDtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBZSxPQUFPO1lBQzNCLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFhLE9BQU87WUFDekI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTWtELGFBQWEsQ0FBQ0M7UUFDbEIsT0FBTyxJQUFJQyxLQUFLRCxZQUFZRSxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3REQyxLQUFLO1lBQ0xDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFFBQVE7UUFDVjtJQUNGO0lBRUEsdUNBQXVDO0lBQ3ZDLElBQUksQ0FBQ3pHLGlCQUFpQjtRQUNwQixxQkFDRSw4REFBQzBHO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUtELFdBQVU7OENBQWdDOzs7Ozs7Ozs7OzswQ0FFbEQsOERBQUNFO2dDQUFHRixXQUFVOzBDQUFzRDs7Ozs7OzBDQUNwRSw4REFBQ0c7Z0NBQUVILFdBQVU7MENBQW1COzs7Ozs7Ozs7Ozs7a0NBR2xDLDhEQUFDSTt3QkFBS0MsVUFBVXpCO3dCQUFhb0IsV0FBVTs7MENBQ3JDLDhEQUFDRDs7a0RBQ0MsOERBQUNPO3dDQUFNTixXQUFVO2tEQUFrRDs7Ozs7O2tEQUduRSw4REFBQ087d0NBQ0NDLE1BQUs7d0NBQ0xDLE9BQU9sSDt3Q0FDUG1ILFVBQVUsQ0FBQ3pELElBQU16RCxjQUFjeUQsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3Q0FDN0NULFdBQVU7d0NBQ1ZZLGFBQVk7d0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OzswQ0FJWiw4REFBQ2Q7O2tEQUNDLDhEQUFDTzt3Q0FBTU4sV0FBVTtrREFBa0Q7Ozs7OztrREFHbkUsOERBQUNPO3dDQUNDQyxNQUFLO3dDQUNMQyxPQUFPaEg7d0NBQ1BpSCxVQUFVLENBQUN6RCxJQUFNdkQsaUJBQWlCdUQsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3Q0FDaERULFdBQVU7d0NBQ1ZZLGFBQVk7d0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs0QkFJWGxILDRCQUNDLDhEQUFDb0c7Z0NBQUlDLFdBQVU7MENBQ1pyRzs7Ozs7OzBDQUlMLDhEQUFDbUg7Z0NBQ0NOLE1BQUs7Z0NBQ0xSLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT1g7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNlO2dCQUFPZixXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUtELFdBQVU7c0RBQStCOzs7Ozs7Ozs7OztrREFFakQsOERBQUNEOzswREFDQyw4REFBQ0c7Z0RBQUdGLFdBQVU7MERBQWdEOzs7Ozs7MERBQzlELDhEQUFDRztnREFBRUgsV0FBVTswREFBMkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHNUMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUtELFdBQVU7a0RBQTJCOzs7Ozs7a0RBQzNDLDhEQUFDYzt3Q0FDQ0UsU0FBU2xDO3dDQUNUa0IsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRVCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDaUI7d0JBQU1qQixXQUFVO2tDQUNmLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNjO2dEQUNDRSxTQUFTLElBQU16RyxhQUFhO2dEQUM1QnlGLFdBQVcscUVBSVYsT0FIQzFGLGNBQWMsYUFDVix3Q0FDQTswREFFUDs7Ozs7OzBEQUdELDhEQUFDd0c7Z0RBQ0NFLFNBQVMsSUFBTXpHLGFBQWE7Z0RBQzVCeUYsV0FBVyxxRUFJVixPQUhDMUYsY0FBYyxVQUNWLHdDQUNBOzBEQUVQOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNTCw4REFBQzRHO29DQUFHbEIsV0FBVTs4Q0FBOEM7Ozs7Ozs4Q0FDNUQsOERBQUNtQjtvQ0FBSW5CLFdBQVU7OENBQ1o1RyxPQUFPZ0ksTUFBTSxLQUFLLGtCQUNqQiw4REFBQ3JCO3dDQUFJQyxXQUFVO2tEQUEyQjs7Ozs7K0NBRTFDNUcsT0FBT21ELEdBQUcsQ0FBQyxDQUFDOEUscUJBQ1osOERBQUNQOzRDQUVDRSxTQUFTLElBQU1sSCxnQkFBZ0J1SCxLQUFLL0QsSUFBSTs0Q0FDeEMwQyxXQUFXLDJEQUlWLE9BSENuRyxpQkFBaUJ3SCxLQUFLL0QsSUFBSSxHQUN0Qix1Q0FDQTtzREFHTiw0RUFBQ3lDO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUtELFdBQVU7a0VBQWVxQixLQUFLekcsSUFBSTs7Ozs7O2tFQUN4Qyw4REFBQ3FGO3dEQUFLRCxXQUFVOzs0REFDYnFCLEtBQUtDLE1BQU0sQ0FBQzlHLFFBQVE7NERBQUM7Ozs7Ozs7Ozs7Ozs7MkNBWHJCNkcsS0FBSzVFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FzQnRCLDhEQUFDOEU7d0JBQUt2QixXQUFVO2tDQUNkLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjFGLGNBQWMsMkJBQ2I7O2tEQUNFLDhEQUFDeUY7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRTtnREFBR0YsV0FBVTs7b0RBQXNEO3FEQUM5QzVHLGVBQUFBLE9BQU9nRSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLElBQUksS0FBS3pELDJCQUE1QlQsbUNBQUFBLGFBQTJDd0IsSUFBSTs7Ozs7OzswREFFckUsOERBQUN1RjtnREFBRUgsV0FBVTswREFBbUI7Ozs7Ozs7Ozs7OztvQ0FLckM5Rix3QkFDQyw4REFBQzZGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQXdCOzs7Ozs7Ozs7OzsrQ0FFckM1RixzQkFDRiw4REFBQzJGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQTRCOzs7Ozs7MERBQzNDLDhEQUFDRztnREFBRUgsV0FBVTswREFBZ0I1Rjs7Ozs7Ozs7Ozs7K0NBRTdCSixTQUFTb0gsTUFBTSxLQUFLLGtCQUN0Qiw4REFBQ3JCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQWdCOzs7Ozs7MERBQy9CLDhEQUFDd0I7Z0RBQUd4QixXQUFVOzBEQUE4Qzs7Ozs7OzBEQUM1RCw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQW1COzs7Ozs7Ozs7Ozs2REFHbEMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3lCO2dEQUFNekIsV0FBVTs7a0VBQ2YsOERBQUMwQjt3REFBTTFCLFdBQVU7a0VBQ2YsNEVBQUMyQjs7OEVBQ0MsOERBQUNDO29FQUFHNUIsV0FBVTs4RUFBc0Y7Ozs7Ozs4RUFHcEcsOERBQUM0QjtvRUFBRzVCLFdBQVU7OEVBQXNGOzs7Ozs7OEVBR3BHLDhEQUFDNEI7b0VBQUc1QixXQUFVOzhFQUFzRjs7Ozs7OzhFQUdwRyw4REFBQzRCO29FQUFHNUIsV0FBVTs4RUFBc0Y7Ozs7Ozs4RUFHcEcsOERBQUM0QjtvRUFBRzVCLFdBQVU7OEVBQXNGOzs7Ozs7OEVBR3BHLDhEQUFDNEI7b0VBQUc1QixXQUFVOzhFQUFzRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS3hHLDhEQUFDNkI7d0RBQU03QixXQUFVO2tFQUNkaEcsU0FBU3VDLEdBQUcsQ0FBQyxDQUFDQyx3QkFDYiw4REFBQ21GO2dFQUFvQjNCLFdBQVU7O2tGQUM3Qiw4REFBQzhCO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7OzhGQUNDLDhEQUFDQTtvRkFBSUMsV0FBVTs7d0ZBQ1p4RCxRQUFRdUYsU0FBUzt3RkFBQzt3RkFBRXZGLFFBQVF3RixRQUFROzs7Ozs7OzhGQUV2Qyw4REFBQ2pDO29GQUFJQyxXQUFVOzhGQUE0QnhELFFBQVF5RixLQUFLOzs7Ozs7OEZBQ3hELDhEQUFDbEM7b0ZBQUlDLFdBQVU7OEZBQTRCeEQsUUFBUTBGLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUc1RCw4REFBQ0o7d0VBQUc5QixXQUFVO2tGQUNaLDRFQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOzt3RkFBYzt3RkFBT3hELFFBQVEyRixjQUFjOzs7Ozs7OzhGQUMxRCw4REFBQ3BDOzt3RkFBSTt3RkFBS3ZELFFBQVE0RixlQUFlOzs7Ozs7OzhGQUNqQyw4REFBQ3JDO29GQUFJQyxXQUFVOzt3RkFDWnhELFFBQVE2RixVQUFVO3dGQUFDO3dGQUFXN0YsUUFBUTZGLFVBQVUsR0FBRyxJQUFJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFJcEUsOERBQUNQO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ1p4RCxRQUFRNEIsT0FBTyxpQkFDZDs7a0dBQ0UsOERBQUMyQjt3RkFBSUMsV0FBVTtrR0FBZXhELFFBQVE0QixPQUFPLENBQUN4RCxJQUFJOzs7Ozs7a0dBQ2xELDhEQUFDbUY7d0ZBQUlDLFdBQVU7a0dBQW9CeEQsUUFBUTRCLE9BQU8sQ0FBQ3ZELFFBQVE7Ozs7Ozs7NkdBRzdELDhEQUFDb0Y7Z0ZBQUtELFdBQVU7MEZBQW1COzs7Ozs7Ozs7Ozs7Ozs7O2tGQUl6Qyw4REFBQzhCO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDs4RkFBSyxJQUFJUCxLQUFLaEQsUUFBUThGLFVBQVUsRUFBRTdDLGtCQUFrQixDQUFDOzs7Ozs7OEZBQ3RELDhEQUFDTTtvRkFBSUMsV0FBVTs4RkFBb0J4RCxRQUFRK0YsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBR3pELDhEQUFDVDt3RUFBRzlCLFdBQVU7a0ZBQ1osNEVBQUN3Qzs0RUFDQy9CLE9BQU9qRSxRQUFRSixNQUFNOzRFQUNyQnNFLFVBQVUsQ0FBQ3pELElBQU12QixvQkFBb0JjLFFBQVFDLEVBQUUsRUFBRVEsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzs0RUFDL0RULFdBQVcsNEZBQTJILE9BQS9CWCxlQUFlN0MsUUFBUUosTUFBTTs7OEZBRXBJLDhEQUFDcUc7b0ZBQU9oQyxPQUFNOzhGQUFVOzs7Ozs7OEZBQ3hCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFZOzs7Ozs7OEZBQzFCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFjOzs7Ozs7OEZBQzVCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFZOzs7Ozs7OEZBQzFCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFZOzs7Ozs7Ozs7Ozs7Ozs7OztrRkFHOUIsOERBQUNxQjt3RUFBRzlCLFdBQVU7a0ZBQ1hWLFdBQVc5QyxRQUFRa0csU0FBUzs7Ozs7OzsrREFuRHhCbEcsUUFBUUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQThEN0I7O2tEQUNFLDhEQUFDc0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDRzs0REFBR0YsV0FBVTs7Z0VBQXNEO2lFQUM5QzVHLGdCQUFBQSxPQUFPZ0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUt6RCwyQkFBNUJULG9DQUFBQSxjQUEyQ3dCLElBQUk7Ozs7Ozs7c0VBRXJFLDhEQUFDdUY7NERBQUVILFdBQVU7c0VBQW1COzs7Ozs7Ozs7Ozs7OERBSWxDLDhEQUFDYztvREFDQ0UsU0FBUyxJQUFNdkYsaUJBQWlCLENBQUNEO29EQUNqQ3dFLFdBQVU7OERBRVR4RSxnQkFBZ0IsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTWxDLDhEQUFDdUU7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2tCO29EQUFHbEIsV0FBVTs4REFBeUM7Ozs7Ozs7Ozs7OzBEQUV6RCw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUN5QjtvREFBTXpCLFdBQVU7O3NFQUNmLDhEQUFDMEI7NERBQU0xQixXQUFVO3NFQUNmLDRFQUFDMkI7O2tGQUNDLDhEQUFDQzt3RUFBRzVCLFdBQVU7a0ZBQW9GOzs7Ozs7a0ZBR2xHLDhEQUFDNEI7d0VBQUc1QixXQUFVO2tGQUFvRjs7Ozs7O2tGQUdsRyw4REFBQzRCO3dFQUFHNUIsV0FBVTtrRkFBb0Y7Ozs7OztrRkFHbEcsOERBQUM0Qjt3RUFBRzVCLFdBQVU7a0ZBQW9GOzs7Ozs7a0ZBR2xHLDhEQUFDNEI7d0VBQUc1QixXQUFVO2tGQUFvRjs7Ozs7O2tGQUdsRyw4REFBQzRCO3dFQUFHNUIsV0FBVTtrRkFBb0Y7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUt0Ryw4REFBQzZCOzREQUFNN0IsV0FBVTs7Z0VBQ2R4RixTQUFTK0IsR0FBRyxDQUFDLENBQUM2Qix3QkFDYiw4REFBQ3VEO3dFQUFvQjNCLFdBQVU7OzBGQUM3Qiw4REFBQzhCO2dGQUFHOUIsV0FBVTswRkFDWiw0RUFBQ0Q7b0ZBQUlDLFdBQVU7O3dGQUNaNUIsUUFBUW5ELEtBQUssa0JBQ1osOERBQUMwSDs0RkFBSTNDLFdBQVU7NEZBQXlDNEMsS0FBS3hFLFFBQVFuRCxLQUFLOzRGQUFFNEgsS0FBS3pFLFFBQVF4RCxJQUFJOzs7Ozs7c0dBRS9GLDhEQUFDbUY7OzhHQUNDLDhEQUFDQTtvR0FBSUMsV0FBVTs4R0FBd0M1QixRQUFReEQsSUFBSTs7Ozs7OzhHQUNuRSw4REFBQ21GO29HQUFJQyxXQUFVOzhHQUE4QzVCLFFBQVFyRCxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRkFJdEYsOERBQUMrRztnRkFBRzlCLFdBQVU7MEZBQ1osNEVBQUNDO29GQUFLRCxXQUFVOzhGQUNiNUIsUUFBUXZELFFBQVEsQ0FBQ2lJLE9BQU8sQ0FBQyxLQUFLOzs7Ozs7Ozs7OzswRkFHbkMsOERBQUNoQjtnRkFBRzlCLFdBQVU7MEZBQ1g1QixRQUFRdEQsUUFBUTs7Ozs7OzBGQUVuQiw4REFBQ2dIO2dGQUFHOUIsV0FBVTswRkFDWDVCLFFBQVFsRCxVQUFVOzs7Ozs7MEZBRXJCLDhEQUFDNEc7Z0ZBQUc5QixXQUFVOzBGQUNaLDRFQUFDQztvRkFBS0QsV0FBVyw0REFJaEIsT0FIQzVCLFFBQVFqRCxRQUFRLEdBQ1osZ0NBQ0E7OEZBRUhpRCxRQUFRakQsUUFBUSxHQUFHLFdBQVc7Ozs7Ozs7Ozs7OzBGQUduQyw4REFBQzJHO2dGQUFHOUIsV0FBVTs7a0dBQ1osOERBQUNjO3dGQUNDRSxTQUFTLElBQU03QyxrQkFBa0JDO3dGQUNqQzRCLFdBQVU7a0dBQ1g7Ozs7OztrR0FHRCw4REFBQ2M7d0ZBQ0NFLFNBQVMsSUFBTTFDLG9CQUFvQkYsUUFBUTNCLEVBQUU7d0ZBQzdDdUQsV0FBVTtrR0FDWDs7Ozs7Ozs7Ozs7Ozt1RUExQ0k1QixRQUFRM0IsRUFBRTs7Ozs7Z0VBZ0RwQmpDLFNBQVM0RyxNQUFNLEtBQUssbUJBQ25CLDhEQUFDTzs4RUFDQyw0RUFBQ0c7d0VBQUdpQixTQUFTO3dFQUFHL0MsV0FBVTtrRkFBeUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBVzdFeEUsQ0FBQUEsaUJBQWlCLElBQUcsbUJBQ3BCLDhEQUFDdUU7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNrQjt3REFBR2xCLFdBQVU7a0VBQ1gxRSxpQkFBaUIsaUJBQWlCOzs7Ozs7a0VBRXJDLDhEQUFDNkU7d0RBQUVILFdBQVU7a0VBQ1YxRSxpQkFBaUIsK0JBQStCOzs7Ozs7Ozs7Ozs7MERBR3JELDhEQUFDOEU7Z0RBQUtDLFVBQVVyRDtnREFBbUJnRCxXQUFVOztrRUFDN0MsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDTzt3RUFBTU4sV0FBVTtrRkFBa0Q7Ozs7OztrRkFHbkUsOERBQUNPO3dFQUNDQyxNQUFLO3dFQUNMQyxPQUFPL0YsVUFBVUUsSUFBSTt3RUFDckI4RixVQUFVLENBQUN6RCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRTFCLE1BQU1xQyxFQUFFMEQsTUFBTSxDQUFDRixLQUFLO2dGQUFDO3dFQUN2RVQsV0FBVTt3RUFDVlksYUFBWTt3RUFDWkMsUUFBUTs7Ozs7Ozs7Ozs7OzBFQUlaLDhEQUFDZDs7a0ZBQ0MsOERBQUNPO3dFQUFNTixXQUFVO2tGQUFrRDs7Ozs7O2tGQUduRSw4REFBQ3dDO3dFQUNDL0IsT0FBTy9GLFVBQVVHLFFBQVE7d0VBQ3pCNkYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTO29GQUFFLEdBQUdBLElBQUk7b0ZBQUV6QixVQUFVb0MsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSztnRkFBQzt3RUFDM0VULFdBQVU7d0VBQ1ZhLFFBQVE7OzBGQUVSLDhEQUFDNEI7Z0ZBQU9oQyxPQUFNOzBGQUFjOzs7Ozs7MEZBQzVCLDhEQUFDZ0M7Z0ZBQU9oQyxPQUFNOzBGQUFNOzs7Ozs7NEVBQ25CNUcsaUJBQWlCLDBCQUNoQjs7a0dBQ0UsOERBQUM0STt3RkFBT2hDLE9BQU07a0dBQWE7Ozs7OztrR0FDM0IsOERBQUNnQzt3RkFBT2hDLE9BQU07a0dBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQU1wQyw4REFBQ1Y7O2tGQUNDLDhEQUFDTzt3RUFBTU4sV0FBVTtrRkFBa0Q7Ozs7OztrRkFHbkUsOERBQUNPO3dFQUNDQyxNQUFLO3dFQUNMQyxPQUFPL0YsVUFBVUksUUFBUTt3RUFDekI0RixVQUFVLENBQUN6RCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRXhCLFVBQVVtQyxFQUFFMEQsTUFBTSxDQUFDRixLQUFLO2dGQUFDO3dFQUMzRVQsV0FBVTt3RUFDVlksYUFBWTt3RUFDWkMsUUFBUTs7Ozs7Ozs7Ozs7OzBFQUlaLDhEQUFDZDs7a0ZBQ0MsOERBQUNPO3dFQUFNTixXQUFVO2tGQUFrRDs7Ozs7O2tGQUduRSw4REFBQ087d0VBQ0NDLE1BQUs7d0VBQ0xDLE9BQU8vRixVQUFVUSxVQUFVO3dFQUMzQndGLFVBQVUsQ0FBQ3pELElBQU10QyxhQUFhMkIsQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFcEIsWUFBWStCLEVBQUUwRCxNQUFNLENBQUNGLEtBQUs7Z0ZBQUM7d0VBQzdFVCxXQUFVO3dFQUNWWSxhQUFZO3dFQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS2QsOERBQUNkOzswRUFDQyw4REFBQ087Z0VBQU1OLFdBQVU7MEVBQWtEOzs7Ozs7MEVBR25FLDhEQUFDZ0Q7Z0VBQ0N2QyxPQUFPL0YsVUFBVUssV0FBVztnRUFDNUIyRixVQUFVLENBQUN6RCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7NEVBQUUsR0FBR0EsSUFBSTs0RUFBRXZCLGFBQWFrQyxFQUFFMEQsTUFBTSxDQUFDRixLQUFLO3dFQUFDO2dFQUM5RXdDLE1BQU07Z0VBQ05qRCxXQUFVO2dFQUNWWSxhQUFZO2dFQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7a0VBSVosOERBQUNkOzswRUFDQyw4REFBQ087Z0VBQU1OLFdBQVU7MEVBQWtEOzs7Ozs7MEVBR25FLDhEQUFDTztnRUFDQ0MsTUFBSztnRUFDTEMsT0FBTy9GLFVBQVVNLFFBQVE7Z0VBQ3pCMEYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUV0QixVQUFVaUMsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztnRUFDM0VULFdBQVU7Z0VBQ1ZZLGFBQVk7Ozs7Ozs7Ozs7OztrRUFJaEIsOERBQUNiOzswRUFDQyw4REFBQ087Z0VBQU1OLFdBQVU7MEVBQWtEOzs7Ozs7MEVBR25FLDhEQUFDTztnRUFDQ0MsTUFBSztnRUFDTEMsT0FBTy9GLFVBQVVPLEtBQUs7Z0VBQ3RCeUYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUVyQixPQUFPZ0MsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztnRUFDeEVULFdBQVU7Z0VBQ1ZZLGFBQVk7Ozs7Ozs7Ozs7OztrRUFJaEIsOERBQUNiO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ087Z0VBQ0NDLE1BQUs7Z0VBQ0wvRCxJQUFHO2dFQUNIeUcsU0FBU3hJLFVBQVVTLFFBQVE7Z0VBQzNCdUYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUVuQixVQUFVOEIsRUFBRTBELE1BQU0sQ0FBQ3VDLE9BQU87d0VBQUM7Z0VBQzdFbEQsV0FBVTs7Ozs7OzBFQUVaLDhEQUFDTTtnRUFBTTZDLFNBQVE7Z0VBQVduRCxXQUFVOzBFQUFzQzs7Ozs7Ozs7Ozs7O2tFQUs1RSw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDYztnRUFDQ04sTUFBSztnRUFDTFEsU0FBU2xEO2dFQUNUa0MsV0FBVTswRUFDWDs7Ozs7OzBFQUdELDhEQUFDYztnRUFDQ04sTUFBSztnRUFDTDRDLFVBQVVoSTtnRUFDVjRFLFdBQVcsaUZBSVYsT0FIQzVFLGtCQUNJLGtDQUNBOzBFQUdMQSxrQkFDSUUsaUJBQWlCLGdCQUFnQixjQUNqQ0EsaUJBQWlCLG1CQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBY2pFO0dBenpCd0JuQztLQUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9HZW8tVHJhdmVscy9mcm9udGVuZC9zcmMvYXBwL2FkbWluL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGFwaVNlcnZpY2UsIENpdHkgfSBmcm9tICdAL3NlcnZpY2VzL2FwaSc7XG5cbmludGVyZmFjZSBCb29raW5nUmVxdWVzdCB7XG4gIGlkOiBzdHJpbmc7XG4gIGZpcnN0TmFtZTogc3RyaW5nO1xuICBsYXN0TmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBwaG9uZTogc3RyaW5nO1xuICBwaWNrdXBMb2NhdGlvbjogc3RyaW5nO1xuICBkcm9wb2ZmTG9jYXRpb246IHN0cmluZztcbiAgcGlja3VwRGF0ZTogc3RyaW5nO1xuICBwaWNrdXBUaW1lOiBzdHJpbmc7XG4gIHBhc3NlbmdlcnM6IG51bWJlcjtcbiAgc3BlY2lhbFJlcXVlc3RzPzogc3RyaW5nO1xuICBzdGF0dXM6ICdQRU5ESU5HJyB8ICdDT05GSVJNRUQnIHwgJ0lOX1BST0dSRVNTJyB8ICdDT01QTEVURUQnIHwgJ0NBTkNFTExFRCc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICBjaXR5OiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgc2x1Zzogc3RyaW5nO1xuICB9O1xuICB2ZWhpY2xlPzoge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIGNhdGVnb3J5OiBzdHJpbmc7XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluUGFnZSgpIHtcbiAgY29uc3QgW2lzQXV0aGVudGljYXRlZCwgc2V0SXNBdXRoZW50aWNhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2xvZ2luRW1haWwsIHNldExvZ2luRW1haWxdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbG9naW5QYXNzd29yZCwgc2V0TG9naW5QYXNzd29yZF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtsb2dpbkVycm9yLCBzZXRMb2dpbkVycm9yXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2l0eSwgc2V0U2VsZWN0ZWRDaXR5XSA9IHVzZVN0YXRlKCdsb25kb24nKTtcbiAgY29uc3QgW2NpdGllcywgc2V0Q2l0aWVzXSA9IHVzZVN0YXRlPENpdHlbXT4oW10pO1xuICBjb25zdCBbYm9va2luZ3MsIHNldEJvb2tpbmdzXSA9IHVzZVN0YXRlPEJvb2tpbmdSZXF1ZXN0W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTwnYm9va2luZ3MnIHwgJ2ZsZWV0Jz4oJ2Jvb2tpbmdzJyk7XG5cbiAgLy8gRmxlZXQgbWFuYWdlbWVudCBzdGF0ZVxuICBjb25zdCBbdmVoaWNsZXMsIHNldFZlaGljbGVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtmbGVldEZvcm0sIHNldEZsZWV0Rm9ybV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogJycsXG4gICAgY2F0ZWdvcnk6ICdQUklWQVRFX0NBUicsXG4gICAgY2FwYWNpdHk6ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBmZWF0dXJlczogJycsXG4gICAgaW1hZ2U6ICcnLFxuICAgIHByaWNlUmFuZ2U6ICcnLFxuICAgIGlzQWN0aXZlOiB0cnVlXG4gIH0pO1xuICBjb25zdCBbc3VibWl0dGluZ0ZsZWV0LCBzZXRTdWJtaXR0aW5nRmxlZXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdGluZ1ZlaGljbGUsIHNldEVkaXRpbmdWZWhpY2xlXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2hvd0ZsZWV0Rm9ybSwgc2V0U2hvd0ZsZWV0Rm9ybV0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gU3RhdHVzIHVwZGF0ZSBmdW5jdGlvbmFsaXR5XG4gIGNvbnN0IHVwZGF0ZUJvb2tpbmdTdGF0dXMgPSBhc3luYyAoYm9va2luZ0lkOiBzdHJpbmcsIG5ld1N0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvYm9va2luZy8ke2Jvb2tpbmdJZH0vc3RhdHVzYCwge1xuICAgICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1czogbmV3U3RhdHVzIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAvLyBVcGRhdGUgdGhlIGJvb2tpbmcgaW4gdGhlIGxvY2FsIHN0YXRlXG4gICAgICAgIHNldEJvb2tpbmdzKHByZXYgPT4gcHJldi5tYXAoYm9va2luZyA9PlxuICAgICAgICAgIGJvb2tpbmcuaWQgPT09IGJvb2tpbmdJZFxuICAgICAgICAgICAgPyB7IC4uLmJvb2tpbmcsIHN0YXR1czogbmV3U3RhdHVzIGFzIGFueSB9XG4gICAgICAgICAgICA6IGJvb2tpbmdcbiAgICAgICAgKSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbGVydCgnRmFpbGVkIHRvIHVwZGF0ZSBib29raW5nIHN0YXR1cycpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm9yIHVwZGF0aW5nIGJvb2tpbmcgc3RhdHVzJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZldGNoIHZlaGljbGVzIGZvciBzZWxlY3RlZCBjaXR5XG4gIGNvbnN0IGZldGNoVmVoaWNsZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQgfHwgIXNlbGVjdGVkQ2l0eSkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdmVoaWNsZXM/Y2l0eT0ke3NlbGVjdGVkQ2l0eX1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEuZGF0YSkge1xuICAgICAgICAgIHNldFZlaGljbGVzKGRhdGEuZGF0YSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCB2ZWhpY2xlczonLCByZXNwb25zZS5zdGF0dXMpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB2ZWhpY2xlczonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZsZWV0IG1hbmFnZW1lbnQgZnVuY3Rpb25zXG4gIGNvbnN0IGhhbmRsZUZsZWV0U3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRTdWJtaXR0aW5nRmxlZXQodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3Qgc2VsZWN0ZWRDaXR5RGF0YSA9IGNpdGllcy5maW5kKGMgPT4gYy5zbHVnID09PSBzZWxlY3RlZENpdHkpO1xuICAgICAgaWYgKCFzZWxlY3RlZENpdHlEYXRhKSB7XG4gICAgICAgIGFsZXJ0KCdQbGVhc2Ugc2VsZWN0IGEgY2l0eSBmaXJzdCcpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGZsZWV0RGF0YSA9IHtcbiAgICAgICAgLi4uZmxlZXRGb3JtLFxuICAgICAgICBmZWF0dXJlczogZmxlZXRGb3JtLmZlYXR1cmVzLnNwbGl0KCcsJykubWFwKGYgPT4gZi50cmltKCkpLmZpbHRlcihmID0+IGYpLFxuICAgICAgICBjaXR5SWQ6IHNlbGVjdGVkQ2l0eURhdGEuaWRcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHVybCA9IGVkaXRpbmdWZWhpY2xlXG4gICAgICAgID8gYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdmVoaWNsZXMvJHtlZGl0aW5nVmVoaWNsZX1gXG4gICAgICAgIDogJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdmVoaWNsZXMnO1xuXG4gICAgICBjb25zdCBtZXRob2QgPSBlZGl0aW5nVmVoaWNsZSA/ICdQVVQnIDogJ1BPU1QnO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICBtZXRob2QsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShmbGVldERhdGEpLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBhbGVydChgVmVoaWNsZSAke2VkaXRpbmdWZWhpY2xlID8gJ3VwZGF0ZWQnIDogJ2FkZGVkJ30gc3VjY2Vzc2Z1bGx5IWApO1xuICAgICAgICByZXNldEZsZWV0Rm9ybSgpO1xuICAgICAgICAvLyBSZWZyZXNoIGRhdGFcbiAgICAgICAgY29uc3QgY2l0aWVzRGF0YSA9IGF3YWl0IGFwaVNlcnZpY2UuZ2V0Q2l0aWVzKCk7XG4gICAgICAgIHNldENpdGllcyhjaXRpZXNEYXRhKTtcbiAgICAgICAgZmV0Y2hWZWhpY2xlcygpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBhbGVydChgRmFpbGVkIHRvICR7ZWRpdGluZ1ZlaGljbGUgPyAndXBkYXRlJyA6ICdhZGQnfSB2ZWhpY2xlOiAke2Vycm9yRGF0YS5tZXNzYWdlIHx8ICdVbmtub3duIGVycm9yJ31gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIHZlaGljbGU6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm9yIGFkZGluZyB2ZWhpY2xlLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTdWJtaXR0aW5nRmxlZXQoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZXNldEZsZWV0Rm9ybSA9ICgpID0+IHtcbiAgICBzZXRGbGVldEZvcm0oe1xuICAgICAgbmFtZTogJycsXG4gICAgICBjYXRlZ29yeTogJ1BSSVZBVEVfQ0FSJyxcbiAgICAgIGNhcGFjaXR5OiAnJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgIGZlYXR1cmVzOiAnJyxcbiAgICAgIGltYWdlOiAnJyxcbiAgICAgIHByaWNlUmFuZ2U6ICcnLFxuICAgICAgaXNBY3RpdmU6IHRydWVcbiAgICB9KTtcbiAgICBzZXRFZGl0aW5nVmVoaWNsZShudWxsKTtcbiAgICBzZXRTaG93RmxlZXRGb3JtKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0VmVoaWNsZSA9ICh2ZWhpY2xlOiBhbnkpID0+IHtcbiAgICBzZXRGbGVldEZvcm0oe1xuICAgICAgbmFtZTogdmVoaWNsZS5uYW1lLFxuICAgICAgY2F0ZWdvcnk6IHZlaGljbGUuY2F0ZWdvcnksXG4gICAgICBjYXBhY2l0eTogdmVoaWNsZS5jYXBhY2l0eSxcbiAgICAgIGRlc2NyaXB0aW9uOiB2ZWhpY2xlLmRlc2NyaXB0aW9uLFxuICAgICAgZmVhdHVyZXM6IHZlaGljbGUuZmVhdHVyZXMuam9pbignLCAnKSxcbiAgICAgIGltYWdlOiB2ZWhpY2xlLmltYWdlIHx8ICcnLFxuICAgICAgcHJpY2VSYW5nZTogdmVoaWNsZS5wcmljZVJhbmdlLFxuICAgICAgaXNBY3RpdmU6IHZlaGljbGUuaXNBY3RpdmVcbiAgICB9KTtcbiAgICBzZXRFZGl0aW5nVmVoaWNsZSh2ZWhpY2xlLmlkKTtcbiAgICBzZXRTaG93RmxlZXRGb3JtKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVZlaGljbGUgPSBhc3luYyAodmVoaWNsZUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBkZWxldGUgdGhpcyB2ZWhpY2xlPycpKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdmVoaWNsZXMvJHt2ZWhpY2xlSWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBhbGVydCgnVmVoaWNsZSBkZWxldGVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICAgICAgLy8gUmVmcmVzaCBkYXRhXG4gICAgICAgIGNvbnN0IGNpdGllc0RhdGEgPSBhd2FpdCBhcGlTZXJ2aWNlLmdldENpdGllcygpO1xuICAgICAgICBzZXRDaXRpZXMoY2l0aWVzRGF0YSk7XG4gICAgICAgIGZldGNoVmVoaWNsZXMoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgYWxlcnQoYEZhaWxlZCB0byBkZWxldGUgdmVoaWNsZTogJHtlcnJvckRhdGEubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcid9YCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIHZlaGljbGU6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm9yIGRlbGV0aW5nIHZlaGljbGUuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIENoZWNrIGlmIHVzZXIgaXMgYWxyZWFkeSBhdXRoZW50aWNhdGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgYXV0aFN0YXR1cyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pbkF1dGhlbnRpY2F0ZWQnKTtcbiAgICBpZiAoYXV0aFN0YXR1cyA9PT0gJ3RydWUnKSB7XG4gICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgY29uc3QgaGFuZGxlTG9naW4gPSAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldExvZ2luRXJyb3IoJycpO1xuXG4gICAgaWYgKGxvZ2luRW1haWwgPT09ICdhZG1pbkBnb2dlb3RyYXZlbHMuY29tJyAmJiBsb2dpblBhc3N3b3JkID09PSAnYWRtaW4nKSB7XG4gICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQodHJ1ZSk7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWRtaW5BdXRoZW50aWNhdGVkJywgJ3RydWUnKTtcbiAgICAgIHNldExvZ2luRW1haWwoJycpO1xuICAgICAgc2V0TG9naW5QYXNzd29yZCgnJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldExvZ2luRXJyb3IoJ0ludmFsaWQgZW1haWwgb3IgcGFzc3dvcmQnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTG9nb3V0ID0gKCkgPT4ge1xuICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FkbWluQXV0aGVudGljYXRlZCcpO1xuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHJldHVybjtcblxuICAgIGNvbnN0IGZldGNoQ2l0aWVzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnaHR0cDovL2xvY2FsaG9zdDo1MDAwL2FwaS9jaXRpZXMnKTtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzICYmIGRhdGEuZGF0YSkge1xuICAgICAgICAgIHNldENpdGllcyhkYXRhLmRhdGEpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBjaXRpZXMnKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2l0aWVzOicsIGVycm9yKTtcbiAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGNpdGllcycpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBmZXRjaENpdGllcygpO1xuICB9LCBbaXNBdXRoZW50aWNhdGVkXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBmZXRjaEJvb2tpbmdzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9ib29raW5nP2NpdHk9JHtzZWxlY3RlZENpdHl9YCk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAgIHNldEJvb2tpbmdzKGRhdGEuZGF0YSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGJvb2tpbmdzJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGJvb2tpbmdzOicsIGVycm9yKTtcbiAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGJvb2tpbmdzJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaWYgKHNlbGVjdGVkQ2l0eSkge1xuICAgICAgZmV0Y2hCb29raW5ncygpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkQ2l0eV0pO1xuXG4gIC8vIFNlcGFyYXRlIHVzZUVmZmVjdCBmb3IgdmVoaWNsZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNBdXRoZW50aWNhdGVkICYmIHNlbGVjdGVkQ2l0eSkge1xuICAgICAgZmV0Y2hWZWhpY2xlcygpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkQ2l0eSwgaXNBdXRoZW50aWNhdGVkXSk7XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnUEVORElORyc6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnO1xuICAgICAgY2FzZSAnQ09ORklSTUVEJzogcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJztcbiAgICAgIGNhc2UgJ0lOX1BST0dSRVNTJzogcmV0dXJuICdiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMCc7XG4gICAgICBjYXNlICdDT01QTEVURUQnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCc7XG4gICAgICBjYXNlICdDQU5DRUxMRUQnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVTdHJpbmcpLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tR0InLCB7XG4gICAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgbWludXRlOiAnMi1kaWdpdCdcbiAgICB9KTtcbiAgfTtcblxuICAvLyBTaG93IGxvZ2luIGZvcm0gaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LTUwIHRvLWFjY2VudC01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy1lbGVnYW50LXhsIHAtOCB3LWZ1bGwgbWF4LXctbWRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWFjY2VudC1ncmFkaWVudCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC0yeGxcIj5HPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZXJpZiBmb250LWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi0yXCI+QWRtaW4gUG9ydGFsPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5Hb0dlbyBUcmF2ZWxzIExvbmRvbjwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVMb2dpbn0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBFbWFpbCBBZGRyZXNzXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2xvZ2luRW1haWx9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRMb2dpbkVtYWlsKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiYWRtaW5AZ29nZW90cmF2ZWxzLmNvbVwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBQYXNzd29yZFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtsb2dpblBhc3N3b3JkfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TG9naW5QYXNzd29yZChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgcGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge2xvZ2luRXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgdGV4dC1yZWQtNzAwIHB4LTQgcHktMyByb3VuZGVkLWxnIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7bG9naW5FcnJvcn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYWNjZW50LTUwMCB0ZXh0LXdoaXRlIHB5LTMgcHgtNCByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctYWNjZW50LTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBTaWduIEluXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctbmV1dHJhbC01MFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LWxnIGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctYWNjZW50LWdyYWRpZW50IHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+Rzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZXJpZiBmb250LWJvbGQgdGV4dC1wcmltYXJ5LTkwMFwiPkdvR2VvIFRyYXZlbHM8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMFwiPkFkbWluIFBvcnRhbDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTcwMFwiPldlbGNvbWUsIEFkbWluPC9zcGFuPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1hY2NlbnQtNjAwIGhvdmVyOnRleHQtYWNjZW50LTcwMCBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBMb2dvdXRcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2hlYWRlcj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XG4gICAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgICA8YXNpZGUgY2xhc3NOYW1lPVwidy02NCBiZy13aGl0ZSBzaGFkb3ctbGcgaC1zY3JlZW4gc3RpY2t5IHRvcC0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIFRhYnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMSBiZy1uZXV0cmFsLTEwMCByb3VuZGVkLWxnIHAtMVwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignYm9va2luZ3MnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweS0yIHB4LTMgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2Jvb2tpbmdzJ1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtcHJpbWFyeS05MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcHJpbWFyeS02MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEJvb2tpbmdzXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdmbGVldCcpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB5LTIgcHgtMyByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnZmxlZXQnXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctd2hpdGUgdGV4dC1wcmltYXJ5LTkwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1wcmltYXJ5LTYwMCBob3Zlcjp0ZXh0LXByaW1hcnktOTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgRmxlZXRcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIG1iLTZcIj5DaXRpZXM8L2gyPlxuICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge2NpdGllcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwIHRleHQtc21cIj5Mb2FkaW5nIGNpdGllcy4uLjwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIGNpdGllcy5tYXAoKGNpdHkpID0+IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2NpdHkuaWR9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZENpdHkoY2l0eS5zbHVnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0zIHJvdW5kZWQteGwgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDaXR5ID09PSBjaXR5LnNsdWdcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1hY2NlbnQtNTAwIHRleHQtd2hpdGUgc2hhZG93LW1kJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcHJpbWFyeS03MDAgaG92ZXI6YmctcHJpbWFyeS01MCBob3Zlcjp0ZXh0LXByaW1hcnktOTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57Y2l0eS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBvcGFjaXR5LTc1XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NpdHkuX2NvdW50LnZlaGljbGVzfSB2ZWhpY2xlc1xuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvYXNpZGU+XG5cbiAgICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIHAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdib29raW5ncycgPyAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1zZXJpZiBmb250LWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIEJvb2tpbmcgUmVxdWVzdHMgLSB7Y2l0aWVzLmZpbmQoYyA9PiBjLnNsdWcgPT09IHNlbGVjdGVkQ2l0eSk/Lm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICBNYW5hZ2UgYW5kIHRyYWNrIGFsbCBib29raW5nIHJlcXVlc3RzIGZvciB0aGlzIGNpdHlcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWFjY2VudC01MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDAgbXQtNFwiPkxvYWRpbmcgYm9va2luZ3MuLi48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IGVycm9yID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC14bCBtYi00XCI+4pqg77iPPC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogYm9va2luZ3MubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi00XCI+8J+TizwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi0yXCI+Tm8gYm9va2luZ3MgZm91bmQ8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5ObyBib29raW5nIHJlcXVlc3RzIGZvciB0aGlzIGNpdHkgeWV0LjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtM3hsIGVsZWdhbnQtc2hhZG93LWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ3VzdG9tZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgSm91cm5leVxuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIERhdGUgJiBUaW1lXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBDcmVhdGVkXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Ym9va2luZ3MubWFwKChib29raW5nKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXtib29raW5nLmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwIHNtb290aC10cmFuc2l0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Ym9va2luZy5maXJzdE5hbWV9IHtib29raW5nLmxhc3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMFwiPntib29raW5nLmVtYWlsfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS02MDBcIj57Ym9va2luZy5waG9uZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RnJvbToge2Jvb2tpbmcucGlja3VwTG9jYXRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlRvOiB7Ym9va2luZy5kcm9wb2ZmTG9jYXRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Ym9va2luZy5wYXNzZW5nZXJzfSBwYXNzZW5nZXJ7Ym9va2luZy5wYXNzZW5nZXJzID4gMSA/ICdzJyA6ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Jvb2tpbmcudmVoaWNsZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Jvb2tpbmcudmVoaWNsZS5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMFwiPntib29raW5nLnZlaGljbGUuY2F0ZWdvcnl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTUwMFwiPk5vdCBzcGVjaWZpZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+e25ldyBEYXRlKGJvb2tpbmcucGlja3VwRGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1HQicpfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwXCI+e2Jvb2tpbmcucGlja3VwVGltZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtib29raW5nLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQm9va2luZ1N0YXR1cyhib29raW5nLmlkLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1zZW1pYm9sZCBib3JkZXItMCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwICR7Z2V0U3RhdHVzQ29sb3IoYm9va2luZy5zdGF0dXMpfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBFTkRJTkdcIj5QRU5ESU5HPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ09ORklSTUVEXCI+Q09ORklSTUVEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSU5fUFJPR1JFU1NcIj5JTl9QUk9HUkVTUzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNPTVBMRVRFRFwiPkNPTVBMRVRFRDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNBTkNFTExFRFwiPkNBTkNFTExFRDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGJvb2tpbmcuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtc2VyaWYgZm9udC1ib2xkIHRleHQtcHJpbWFyeS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgRmxlZXQgTWFuYWdlbWVudCAtIHtjaXRpZXMuZmluZChjID0+IGMuc2x1ZyA9PT0gc2VsZWN0ZWRDaXR5KT8ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIE1hbmFnZSBwcmVtaXVtIHZlaGljbGVzIGZvciB0aGlzIGNpdHlcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0ZsZWV0Rm9ybSghc2hvd0ZsZWV0Rm9ybSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYWNjZW50LTUwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctYWNjZW50LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2hvd0ZsZWV0Rm9ybSA/ICdDYW5jZWwnIDogJ0FkZCBOZXcgVmVoaWNsZSd9XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVmVoaWNsZSBMaXN0ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LWxnIG1iLThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDBcIj5DdXJyZW50IEZsZWV0PC9oMj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgZGl2aWRlLXkgZGl2aWRlLW5ldXRyYWwtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLW5ldXRyYWwtNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZlaGljbGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhdGVnb3J5XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYXBhY2l0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUHJpY2UgUmFuZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQWN0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtbmV1dHJhbC0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt2ZWhpY2xlcy5tYXAoKHZlaGljbGUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17dmVoaWNsZS5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctbmV1dHJhbC01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUuaW1hZ2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHJvdW5kZWQtbGcgb2JqZWN0LWNvdmVyIG1yLTNcIiBzcmM9e3ZlaGljbGUuaW1hZ2V9IGFsdD17dmVoaWNsZS5uYW1lfSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktOTAwXCI+e3ZlaGljbGUubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMCB0cnVuY2F0ZSBtYXgtdy14c1wiPnt2ZWhpY2xlLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZS5jYXRlZ29yeS5yZXBsYWNlKCdfJywgJyAnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZS5jYXBhY2l0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZS5wcmljZVJhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVoaWNsZS5pc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUuaXNBY3RpdmUgPyAnQWN0aXZlJyA6ICdJbmFjdGl2ZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdFZlaGljbGUodmVoaWNsZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYWNjZW50LTYwMCBob3Zlcjp0ZXh0LWFjY2VudC05MDAgbXItM1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEVkaXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVWZWhpY2xlKHZlaGljbGUuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZXMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjb2xTcGFuPXs2fSBjbGFzc05hbWU9XCJweC02IHB5LTggdGV4dC1jZW50ZXIgdGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTm8gdmVoaWNsZXMgZm91bmQgZm9yIHRoaXMgY2l0eS4gQWRkIHlvdXIgZmlyc3QgdmVoaWNsZSB0byBnZXQgc3RhcnRlZC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVmVoaWNsZSBGb3JtICovfVxuICAgICAgICAgICAgICAgIHsoc2hvd0ZsZWV0Rm9ybSB8fCB0cnVlKSAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBwLTggbXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtlZGl0aW5nVmVoaWNsZSA/ICdFZGl0IFZlaGljbGUnIDogJ0FkZCBOZXcgVmVoaWNsZSd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtlZGl0aW5nVmVoaWNsZSA/ICdVcGRhdGUgdmVoaWNsZSBpbmZvcm1hdGlvbicgOiAnQWRkIGEgbmV3IHZlaGljbGUgdG8gdGhlIGZsZWV0J31cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlRmxlZXRTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFZlaGljbGUgTmFtZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZsZWV0Rm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBNZXJjZWRlcyBTLUNsYXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFZlaGljbGUgQ2F0ZWdvcnkgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBjYXRlZ29yeTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUFJJVkFURV9DQVJcIj5Qcml2YXRlIENhcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQlVTXCI+UHJpdmF0ZSBCdXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkQ2l0eSA9PT0gJ2xvbmRvbicgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSEVMSUNPUFRFUlwiPkhlbGljb3B0ZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQUklWQVRFX0pFVFwiPlByaXZhdGUgSmV0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ2FwYWNpdHkgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0uY2FwYWNpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY2FwYWNpdHk6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCA0IHBhc3NlbmdlcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUHJpY2UgUmFuZ2UgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0ucHJpY2VSYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBwcmljZVJhbmdlOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgwqMyMDAtMzAwL2hvdXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIERlc2NyaXB0aW9uICpcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmliZSB0aGUgdmVoaWNsZSBhbmQgaXRzIGx1eHVyeSBmZWF0dXJlcy4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBGZWF0dXJlcyAoY29tbWEtc2VwYXJhdGVkKVxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmxlZXRGb3JtLmZlYXR1cmVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBmZWF0dXJlczogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgTGVhdGhlciBzZWF0cywgV2lGaSwgQ2hhbXBhZ25lIHNlcnZpY2UsIFByb2Zlc3Npb25hbCBjaGF1ZmZldXJcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBJbWFnZSBVUkxcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInVybFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmxlZXRGb3JtLmltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBpbWFnZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9leGFtcGxlLmNvbS92ZWhpY2xlLWltYWdlLmpwZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiaXNBY3RpdmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZmxlZXRGb3JtLmlzQWN0aXZlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBpc0FjdGl2ZTogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYWNjZW50LTYwMCBmb2N1czpyaW5nLWFjY2VudC01MDAgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJpc0FjdGl2ZVwiIGNsYXNzTmFtZT1cIm1sLTIgYmxvY2sgdGV4dC1zbSB0ZXh0LXByaW1hcnktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlIGlzIGFjdGl2ZSBhbmQgYXZhaWxhYmxlIGZvciBib29raW5nXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17cmVzZXRGbGVldEZvcm19XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1uZXV0cmFsLTIwMCB0ZXh0LW5ldXRyYWwtNzAwIHB4LTYgcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctbmV1dHJhbC0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3N1Ym1pdHRpbmdGbGVldH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWFjY2VudC01MDAgdGV4dC13aGl0ZSBweC04IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN1Ym1pdHRpbmdGbGVldFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWFjY2VudC02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c3VibWl0dGluZ0ZsZWV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gKGVkaXRpbmdWZWhpY2xlID8gJ1VwZGF0aW5nLi4uJyA6ICdBZGRpbmcuLi4nKVxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IChlZGl0aW5nVmVoaWNsZSA/ICdVcGRhdGUgVmVoaWNsZScgOiAnQWRkIFZlaGljbGUnKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImFwaVNlcnZpY2UiLCJBZG1pblBhZ2UiLCJjaXRpZXMiLCJpc0F1dGhlbnRpY2F0ZWQiLCJzZXRJc0F1dGhlbnRpY2F0ZWQiLCJsb2dpbkVtYWlsIiwic2V0TG9naW5FbWFpbCIsImxvZ2luUGFzc3dvcmQiLCJzZXRMb2dpblBhc3N3b3JkIiwibG9naW5FcnJvciIsInNldExvZ2luRXJyb3IiLCJzZWxlY3RlZENpdHkiLCJzZXRTZWxlY3RlZENpdHkiLCJzZXRDaXRpZXMiLCJib29raW5ncyIsInNldEJvb2tpbmdzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwidmVoaWNsZXMiLCJzZXRWZWhpY2xlcyIsImZsZWV0Rm9ybSIsInNldEZsZWV0Rm9ybSIsIm5hbWUiLCJjYXRlZ29yeSIsImNhcGFjaXR5IiwiZGVzY3JpcHRpb24iLCJmZWF0dXJlcyIsImltYWdlIiwicHJpY2VSYW5nZSIsImlzQWN0aXZlIiwic3VibWl0dGluZ0ZsZWV0Iiwic2V0U3VibWl0dGluZ0ZsZWV0IiwiZWRpdGluZ1ZlaGljbGUiLCJzZXRFZGl0aW5nVmVoaWNsZSIsInNob3dGbGVldEZvcm0iLCJzZXRTaG93RmxlZXRGb3JtIiwidXBkYXRlQm9va2luZ1N0YXR1cyIsImJvb2tpbmdJZCIsIm5ld1N0YXR1cyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdGF0dXMiLCJvayIsInByZXYiLCJtYXAiLCJib29raW5nIiwiaWQiLCJhbGVydCIsImNvbnNvbGUiLCJmZXRjaFZlaGljbGVzIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwiaGFuZGxlRmxlZXRTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJzZWxlY3RlZENpdHlEYXRhIiwiZmluZCIsImMiLCJzbHVnIiwiZmxlZXREYXRhIiwic3BsaXQiLCJmIiwidHJpbSIsImZpbHRlciIsImNpdHlJZCIsInVybCIsInJlc2V0RmxlZXRGb3JtIiwiY2l0aWVzRGF0YSIsImdldENpdGllcyIsImVycm9yRGF0YSIsIm1lc3NhZ2UiLCJoYW5kbGVFZGl0VmVoaWNsZSIsInZlaGljbGUiLCJqb2luIiwiaGFuZGxlRGVsZXRlVmVoaWNsZSIsInZlaGljbGVJZCIsImNvbmZpcm0iLCJhdXRoU3RhdHVzIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImhhbmRsZUxvZ2luIiwic2V0SXRlbSIsImhhbmRsZUxvZ291dCIsInJlbW92ZUl0ZW0iLCJmZXRjaENpdGllcyIsImZldGNoQm9va2luZ3MiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsImdldFN0YXR1c0NvbG9yIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGF5IiwibW9udGgiLCJ5ZWFyIiwiaG91ciIsIm1pbnV0ZSIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJoMSIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIiwiYnV0dG9uIiwiaGVhZGVyIiwib25DbGljayIsImFzaWRlIiwiaDIiLCJuYXYiLCJsZW5ndGgiLCJjaXR5IiwiX2NvdW50IiwibWFpbiIsImgzIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJlbWFpbCIsInBob25lIiwicGlja3VwTG9jYXRpb24iLCJkcm9wb2ZmTG9jYXRpb24iLCJwYXNzZW5nZXJzIiwicGlja3VwRGF0ZSIsInBpY2t1cFRpbWUiLCJzZWxlY3QiLCJvcHRpb24iLCJjcmVhdGVkQXQiLCJpbWciLCJzcmMiLCJhbHQiLCJyZXBsYWNlIiwiY29sU3BhbiIsInRleHRhcmVhIiwicm93cyIsImNoZWNrZWQiLCJodG1sRm9yIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});