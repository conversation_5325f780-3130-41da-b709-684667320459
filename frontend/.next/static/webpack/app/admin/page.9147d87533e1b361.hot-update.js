"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    var _cities_find, _cities_find1;\n    _s();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginEmail, setLoginEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginPassword, setLoginPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCity, setSelectedCity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('london');\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bookings, setBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bookings');\n    // Fleet management state\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fleetForm, setFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        category: 'PRIVATE_CAR',\n        capacity: '',\n        description: '',\n        features: '',\n        image: '',\n        priceRange: '',\n        isActive: true\n    });\n    const [submittingFleet, setSubmittingFleet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingVehicle, setEditingVehicle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFleetForm, setShowFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Status update functionality\n    const updateBookingStatus = async (bookingId, newStatus)=>{\n        try {\n            const response = await fetch(\"http://localhost:5000/api/booking/\".concat(bookingId, \"/status\"), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                // Update the booking in the local state\n                setBookings((prev)=>prev.map((booking)=>booking.id === bookingId ? {\n                            ...booking,\n                            status: newStatus\n                        } : booking));\n            } else {\n                alert('Failed to update booking status');\n            }\n        } catch (error) {\n            console.error('Error updating status:', error);\n            alert('Error updating booking status');\n        }\n    };\n    // Fetch vehicles for selected city\n    const fetchVehicles = async ()=>{\n        if (!isAuthenticated || !selectedCity) return;\n        try {\n            const response = await fetch(\"http://localhost:5000/api/vehicles?city=\".concat(selectedCity));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.data) {\n                    setVehicles(data.data);\n                }\n            } else {\n                console.error('Failed to fetch vehicles:', response.status);\n            }\n        } catch (error) {\n            console.error('Error fetching vehicles:', error);\n        }\n    };\n    // Fleet management functions\n    const handleFleetSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmittingFleet(true);\n        try {\n            const selectedCityData = cities.find((c)=>c.slug === selectedCity);\n            if (!selectedCityData) {\n                alert('Please select a city first');\n                return;\n            }\n            const fleetData = {\n                ...fleetForm,\n                features: fleetForm.features.split(',').map((f)=>f.trim()).filter((f)=>f),\n                cityId: selectedCityData.id\n            };\n            const url = editingVehicle ? \"http://localhost:5000/api/vehicles/\".concat(editingVehicle) : 'http://localhost:5000/api/vehicles';\n            const method = editingVehicle ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(fleetData)\n            });\n            if (response.ok) {\n                alert(\"Vehicle \".concat(editingVehicle ? 'updated' : 'added', \" successfully!\"));\n                resetFleetForm();\n                // Refresh data\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n                fetchVehicles();\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to \".concat(editingVehicle ? 'update' : 'add', \" vehicle: \").concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error adding vehicle:', error);\n            alert('Error adding vehicle. Please try again.');\n        } finally{\n            setSubmittingFleet(false);\n        }\n    };\n    const resetFleetForm = ()=>{\n        setFleetForm({\n            name: '',\n            category: 'PRIVATE_CAR',\n            capacity: '',\n            description: '',\n            features: '',\n            image: '',\n            priceRange: '',\n            isActive: true\n        });\n        setEditingVehicle(null);\n        setShowFleetForm(false);\n    };\n    const handleEditVehicle = (vehicle)=>{\n        setFleetForm({\n            name: vehicle.name,\n            category: vehicle.category,\n            capacity: vehicle.capacity,\n            description: vehicle.description,\n            features: vehicle.features.join(', '),\n            image: vehicle.image || '',\n            priceRange: vehicle.priceRange,\n            isActive: vehicle.isActive\n        });\n        setEditingVehicle(vehicle.id);\n        setShowFleetForm(true);\n    };\n    const handleDeleteVehicle = async (vehicleId)=>{\n        if (!confirm('Are you sure you want to delete this vehicle?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"http://localhost:5000/api/vehicles/\".concat(vehicleId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                alert('Vehicle deleted successfully!');\n                // Refresh data\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n                fetchVehicles();\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to delete vehicle: \".concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error deleting vehicle:', error);\n            alert('Error deleting vehicle. Please try again.');\n        }\n    };\n    // Check if user is already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const authStatus = localStorage.getItem('adminAuthenticated');\n            if (authStatus === 'true') {\n                setIsAuthenticated(true);\n            }\n        }\n    }[\"AdminPage.useEffect\"], []);\n    const handleLogin = (e)=>{\n        e.preventDefault();\n        setLoginError('');\n        if (loginEmail === '<EMAIL>' && loginPassword === 'admin') {\n            setIsAuthenticated(true);\n            localStorage.setItem('adminAuthenticated', 'true');\n            setLoginEmail('');\n            setLoginPassword('');\n        } else {\n            setLoginError('Invalid email or password');\n        }\n    };\n    const handleLogout = ()=>{\n        setIsAuthenticated(false);\n        localStorage.removeItem('adminAuthenticated');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const fetchCities = {\n                \"AdminPage.useEffect.fetchCities\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/api/cities');\n                        const data = await response.json();\n                        if (data.success && data.data) {\n                            setCities(data.data);\n                        } else {\n                            setError('Failed to load cities');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching cities:', error);\n                        setError('Failed to load cities');\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchCities\"];\n            fetchCities();\n        }\n    }[\"AdminPage.useEffect\"], [\n        isAuthenticated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const fetchBookings = {\n                \"AdminPage.useEffect.fetchBookings\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"\".concat(\"http://localhost:5000/api\", \"/booking?city=\").concat(selectedCity));\n                        const data = await response.json();\n                        if (data.success) {\n                            setBookings(data.data);\n                        } else {\n                            setError('Failed to load bookings');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching bookings:', error);\n                        setError('Failed to load bookings');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchBookings\"];\n            if (selectedCity) {\n                fetchBookings();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity\n    ]);\n    // Separate useEffect for vehicles\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (isAuthenticated && selectedCity) {\n                fetchVehicles();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity,\n        isAuthenticated\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'PENDING':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'CONFIRMED':\n                return 'bg-blue-100 text-blue-800';\n            case 'IN_PROGRESS':\n                return 'bg-purple-100 text-purple-800';\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'CANCELLED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-GB', {\n            day: '2-digit',\n            month: 'short',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // Show login form if not authenticated\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-elegant-xl p-8 w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-accent-gradient rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-2xl\",\n                                    children: \"G\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 mb-2\",\n                                children: \"Admin Portal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-600\",\n                                children: \"GoGeo Travels London\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleLogin,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Email Address\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: loginEmail,\n                                        onChange: (e)=>setLoginEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: loginPassword,\n                                        onChange: (e)=>setLoginPassword(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"Enter your password\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\",\n                                children: loginError\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"w-full bg-accent-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-accent-600 transition-colors duration-200\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-neutral-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-lg border-b border-neutral-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-accent-gradient rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-serif font-bold text-primary-900\",\n                                                children: \"GoGeo Travels\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-primary-600\",\n                                                children: \"Admin Portal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-primary-700\",\n                                        children: \"Welcome, Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"text-sm text-accent-600 hover:text-accent-700 font-medium\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 380,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"w-64 bg-white shadow-lg h-screen sticky top-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-neutral-100 rounded-lg p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('bookings'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'bookings' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('fleet'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'fleet' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Fleet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-primary-900 mb-6\",\n                                    children: \"Cities\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: cities.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-600 text-sm\",\n                                        children: \"Loading cities...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, this) : cities.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedCity(city.slug),\n                                            className: \"w-full text-left px-4 py-3 rounded-xl transition-colors \".concat(selectedCity === city.slug ? 'bg-accent-500 text-white shadow-md' : 'text-primary-700 hover:bg-primary-50 hover:text-primary-900'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: city.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm opacity-75\",\n                                                        children: [\n                                                            city._count.vehicles,\n                                                            \" vehicles\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, city.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto\",\n                            children: activeTab === 'bookings' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                                children: [\n                                                    \"Booking Requests - \",\n                                                    (_cities_find = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find === void 0 ? void 0 : _cities_find.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600\",\n                                                children: \"Manage and track all booking requests for this city\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600 mt-4\",\n                                                children: \"Loading bookings...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-500 text-xl mb-4\",\n                                                children: \"⚠️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this) : bookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-primary-900 mb-2\",\n                                                children: \"No bookings found\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600\",\n                                                children: \"No booking requests for this city yet.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-3xl elegant-shadow-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Customer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Journey\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Vehicle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Date & Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                        children: bookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"hover:bg-gray-50 smooth-transition\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold text-primary-900\",\n                                                                                    children: [\n                                                                                        booking.firstName,\n                                                                                        \" \",\n                                                                                        booking.lastName\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 524,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-primary-600\",\n                                                                                    children: booking.email\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-primary-600\",\n                                                                                    children: booking.phone\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 528,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"From: \",\n                                                                                        booking.pickupLocation\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"To: \",\n                                                                                        booking.dropoffLocation\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600 mt-1\",\n                                                                                    children: [\n                                                                                        booking.passengers,\n                                                                                        \" passenger\",\n                                                                                        booking.passengers > 1 ? 's' : ''\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: booking.vehicle ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: booking.vehicle.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 544,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-primary-600\",\n                                                                                        children: booking.vehicle.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-primary-500\",\n                                                                                children: \"Not specified\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 548,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 541,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: new Date(booking.pickupDate).toLocaleDateString('en-GB')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600\",\n                                                                                    children: booking.pickupTime\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 555,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: booking.status,\n                                                                            onChange: (e)=>updateBookingStatus(booking.id, e.target.value),\n                                                                            className: \"px-3 py-1 rounded-full text-xs font-semibold border-0 focus:ring-2 focus:ring-accent-500 \".concat(getStatusColor(booking.status)),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"PENDING\",\n                                                                                    children: \"PENDING\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 564,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"CONFIRMED\",\n                                                                                    children: \"CONFIRMED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"IN_PROGRESS\",\n                                                                                    children: \"IN_PROGRESS\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"COMPLETED\",\n                                                                                    children: \"COMPLETED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 567,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"CANCELLED\",\n                                                                                    children: \"CANCELLED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-600\",\n                                                                        children: formatDate(booking.createdAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, booking.id, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                                            children: [\n                                                                \"Fleet Management - \",\n                                                                (_cities_find1 = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find1 === void 0 ? void 0 : _cities_find1.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-primary-600\",\n                                                            children: \"Manage premium vehicles for this city\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFleetForm(!showFleetForm),\n                                                    className: \"bg-accent-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-accent-600 transition-colors\",\n                                                    children: showFleetForm ? 'Cancel' : 'Add New Vehicle'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-lg mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-neutral-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-primary-900\",\n                                                    children: \"Current Fleet\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full divide-y divide-neutral-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-neutral-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Vehicle\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Capacity\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Price Range\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-neutral-200\",\n                                                            children: [\n                                                                vehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-neutral-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        vehicle.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                            className: \"h-10 w-10 rounded-lg object-cover mr-3\",\n                                                                                            src: vehicle.image,\n                                                                                            alt: vehicle.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                            lineNumber: 638,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-medium text-primary-900\",\n                                                                                                    children: vehicle.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                                    lineNumber: 641,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm text-primary-600 truncate max-w-xs\",\n                                                                                                    children: vehicle.description\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                                    lineNumber: 642,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 636,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800\",\n                                                                                    children: vehicle.category.replace('_', ' ')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 647,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-900\",\n                                                                                children: vehicle.capacity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 651,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-900\",\n                                                                                children: vehicle.priceRange\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(vehicle.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                                    children: vehicle.isActive ? 'Active' : 'Inactive'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 658,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleEditVehicle(vehicle),\n                                                                                        className: \"text-accent-600 hover:text-accent-900 mr-3\",\n                                                                                        children: \"Edit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 667,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteVehicle(vehicle.id),\n                                                                                        className: \"text-red-600 hover:text-red-900\",\n                                                                                        children: \"Delete\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 673,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, vehicle.id, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 27\n                                                                    }, this)),\n                                                                vehicles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        colSpan: 6,\n                                                                        className: \"px-6 py-8 text-center text-primary-600\",\n                                                                        children: \"No vehicles found for this city. Add your first vehicle to get started.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 684,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 text-xs text-primary-600\",\n                                        children: [\n                                            \"Form visible: \",\n                                            showFleetForm ? 'YES' : 'NO',\n                                            \" | Editing: \",\n                                            editingVehicle || 'None'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this),\n                                    showFleetForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-primary-900\",\n                                                        children: editingVehicle ? 'Edit Vehicle' : 'Add New Vehicle'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-primary-600 mt-1\",\n                                                        children: editingVehicle ? 'Update vehicle information' : 'Add a new vehicle to the fleet'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleFleetSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Vehicle Name *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.name,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    name: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., Mercedes S-Class\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 716,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 712,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Vehicle Category *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: fleetForm.category,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    category: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        required: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"PRIVATE_CAR\",\n                                                                                children: \"Private Car\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"BUS\",\n                                                                                children: \"Private Bus\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 737,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            selectedCity === 'london' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"HELICOPTER\",\n                                                                                        children: \"Helicopter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 740,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"PRIVATE_JET\",\n                                                                                        children: \"Private Jet\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 741,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Capacity *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.capacity,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    capacity: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., 4 passengers\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 751,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Price Range *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.priceRange,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    priceRange: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., \\xa3200-300/hour\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Description *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 777,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: fleetForm.description,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            description: e.target.value\n                                                                        })),\n                                                                rows: 4,\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"Describe the vehicle and its luxury features...\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Features (comma-separated)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: fleetForm.features,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            features: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"e.g., Leather seats, WiFi, Champagne service, Professional chauffeur\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Image URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"url\",\n                                                                value: fleetForm.image,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            image: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"https://example.com/vehicle-image.jpg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 807,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                id: \"isActive\",\n                                                                checked: fleetForm.isActive,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            isActive: e.target.checked\n                                                                        })),\n                                                                className: \"h-4 w-4 text-accent-600 focus:ring-accent-500 border-neutral-300 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"isActive\",\n                                                                className: \"ml-2 block text-sm text-primary-800\",\n                                                                children: \"Vehicle is active and available for booking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: resetFleetForm,\n                                                                className: \"bg-neutral-200 text-neutral-700 px-6 py-3 rounded-lg font-semibold hover:bg-neutral-300 transition-colors\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"submit\",\n                                                                disabled: submittingFleet,\n                                                                className: \"bg-accent-500 text-white px-8 py-3 rounded-lg font-semibold transition-colors \".concat(submittingFleet ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent-600'),\n                                                                children: submittingFleet ? editingVehicle ? 'Updating...' : 'Adding...' : editingVehicle ? 'Update Vehicle' : 'Add Vehicle'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 405,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"OJE2clIpiDyqZBOo1Ow9VbcafA4=\");\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0QztBQUNNO0FBNEJuQyxTQUFTRztRQXNiZ0JDLGNBc0hJQTs7SUEzaUIxQyxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ08sWUFBWUMsY0FBYyxHQUFHUiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNTLGVBQWVDLGlCQUFpQixHQUFHViwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNXLFlBQVlDLGNBQWMsR0FBR1osK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYSxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDSSxRQUFRVyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFTLEVBQUU7SUFDL0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQW1CLEVBQUU7SUFDN0QsTUFBTSxDQUFDa0IsU0FBU0MsV0FBVyxHQUFHbkIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDb0IsT0FBT0MsU0FBUyxHQUFHckIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3NCLFdBQVdDLGFBQWEsR0FBR3ZCLCtDQUFRQSxDQUF1QjtJQUVqRSx5QkFBeUI7SUFDekIsTUFBTSxDQUFDd0IsVUFBVUMsWUFBWSxHQUFHekIsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUczQiwrQ0FBUUEsQ0FBQztRQUN6QzRCLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHckMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDc0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUN3QyxlQUFlQyxpQkFBaUIsR0FBR3pDLCtDQUFRQSxDQUFDO0lBRW5ELDhCQUE4QjtJQUM5QixNQUFNMEMsc0JBQXNCLE9BQU9DLFdBQW1CQztRQUNwRCxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLHFDQUErQyxPQUFWSCxXQUFVLFlBQVU7Z0JBQ3BGSSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsUUFBUVI7Z0JBQVU7WUFDM0M7WUFFQSxJQUFJQyxTQUFTUSxFQUFFLEVBQUU7Z0JBQ2Ysd0NBQXdDO2dCQUN4Q3BDLFlBQVlxQyxDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLFVBQzNCQSxRQUFRQyxFQUFFLEtBQUtkLFlBQ1g7NEJBQUUsR0FBR2EsT0FBTzs0QkFBRUosUUFBUVI7d0JBQWlCLElBQ3ZDWTtZQUVSLE9BQU87Z0JBQ0xFLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT3RDLE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q3NDLE1BQU07UUFDUjtJQUNGO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU1FLGdCQUFnQjtRQUNwQixJQUFJLENBQUN2RCxtQkFBbUIsQ0FBQ1EsY0FBYztRQUV2QyxJQUFJO1lBQ0YsTUFBTWdDLFdBQVcsTUFBTUMsTUFBTSwyQ0FBd0QsT0FBYmpDO1lBQ3hFLElBQUlnQyxTQUFTUSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTVEsT0FBTyxNQUFNaEIsU0FBU2lCLElBQUk7Z0JBQ2hDLElBQUlELEtBQUtFLE9BQU8sSUFBSUYsS0FBS0EsSUFBSSxFQUFFO29CQUM3QnBDLFlBQVlvQyxLQUFLQSxJQUFJO2dCQUN2QjtZQUNGLE9BQU87Z0JBQ0xGLFFBQVF2QyxLQUFLLENBQUMsNkJBQTZCeUIsU0FBU08sTUFBTTtZQUM1RDtRQUNGLEVBQUUsT0FBT2hDLE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsNEJBQTRCQTtRQUM1QztJQUNGO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU00QyxvQkFBb0IsT0FBT0M7UUFDL0JBLEVBQUVDLGNBQWM7UUFDaEI3QixtQkFBbUI7UUFFbkIsSUFBSTtZQUNGLE1BQU04QixtQkFBbUIvRCxPQUFPZ0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUt6RDtZQUNyRCxJQUFJLENBQUNzRCxrQkFBa0I7Z0JBQ3JCVCxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxNQUFNYSxZQUFZO2dCQUNoQixHQUFHN0MsU0FBUztnQkFDWk0sVUFBVU4sVUFBVU0sUUFBUSxDQUFDd0MsS0FBSyxDQUFDLEtBQUtqQixHQUFHLENBQUNrQixDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLElBQUlDLE1BQU0sQ0FBQ0YsQ0FBQUEsSUFBS0E7Z0JBQ3ZFRyxRQUFRVCxpQkFBaUJWLEVBQUU7WUFDN0I7WUFFQSxNQUFNb0IsTUFBTXZDLGlCQUNSLHNDQUFxRCxPQUFmQSxrQkFDdEM7WUFFSixNQUFNUyxTQUFTVCxpQkFBaUIsUUFBUTtZQUV4QyxNQUFNTyxXQUFXLE1BQU1DLE1BQU0rQixLQUFLO2dCQUNoQzlCO2dCQUNBQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ29CO1lBQ3ZCO1lBRUEsSUFBSTFCLFNBQVNRLEVBQUUsRUFBRTtnQkFDZkssTUFBTSxXQUFnRCxPQUFyQ3BCLGlCQUFpQixZQUFZLFNBQVE7Z0JBQ3REd0M7Z0JBQ0EsZUFBZTtnQkFDZixNQUFNQyxhQUFhLE1BQU03RSxxREFBVUEsQ0FBQzhFLFNBQVM7Z0JBQzdDakUsVUFBVWdFO2dCQUNWbkI7WUFDRixPQUFPO2dCQUNMLE1BQU1xQixZQUFZLE1BQU1wQyxTQUFTaUIsSUFBSTtnQkFDckNKLE1BQU0sYUFBMkR1QixPQUE5QzNDLGlCQUFpQixXQUFXLE9BQU0sY0FBaUQsT0FBckMyQyxVQUFVQyxPQUFPLElBQUk7WUFDeEY7UUFDRixFQUFFLE9BQU85RCxPQUFPO1lBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNzQyxNQUFNO1FBQ1IsU0FBVTtZQUNSckIsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNeUMsaUJBQWlCO1FBQ3JCbkQsYUFBYTtZQUNYQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjtRQUNBSSxrQkFBa0I7UUFDbEJFLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0wQyxvQkFBb0IsQ0FBQ0M7UUFDekJ6RCxhQUFhO1lBQ1hDLE1BQU13RCxRQUFReEQsSUFBSTtZQUNsQkMsVUFBVXVELFFBQVF2RCxRQUFRO1lBQzFCQyxVQUFVc0QsUUFBUXRELFFBQVE7WUFDMUJDLGFBQWFxRCxRQUFRckQsV0FBVztZQUNoQ0MsVUFBVW9ELFFBQVFwRCxRQUFRLENBQUNxRCxJQUFJLENBQUM7WUFDaENwRCxPQUFPbUQsUUFBUW5ELEtBQUssSUFBSTtZQUN4QkMsWUFBWWtELFFBQVFsRCxVQUFVO1lBQzlCQyxVQUFVaUQsUUFBUWpELFFBQVE7UUFDNUI7UUFDQUksa0JBQWtCNkMsUUFBUTNCLEVBQUU7UUFDNUJoQixpQkFBaUI7SUFDbkI7SUFFQSxNQUFNNkMsc0JBQXNCLE9BQU9DO1FBQ2pDLElBQUksQ0FBQ0MsUUFBUSxrREFBa0Q7WUFDN0Q7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNM0MsV0FBVyxNQUFNQyxNQUFNLHNDQUFnRCxPQUFWeUMsWUFBYTtnQkFDOUV4QyxRQUFRO1lBQ1Y7WUFFQSxJQUFJRixTQUFTUSxFQUFFLEVBQUU7Z0JBQ2ZLLE1BQU07Z0JBQ04sZUFBZTtnQkFDZixNQUFNcUIsYUFBYSxNQUFNN0UscURBQVVBLENBQUM4RSxTQUFTO2dCQUM3Q2pFLFVBQVVnRTtnQkFDVm5CO1lBQ0YsT0FBTztnQkFDTCxNQUFNcUIsWUFBWSxNQUFNcEMsU0FBU2lCLElBQUk7Z0JBQ3JDSixNQUFNLDZCQUFrRSxPQUFyQ3VCLFVBQVVDLE9BQU8sSUFBSTtZQUMxRDtRQUNGLEVBQUUsT0FBTzlELE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q3NDLE1BQU07UUFDUjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDekQsZ0RBQVNBOytCQUFDO1lBQ1IsTUFBTXdGLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztZQUN4QyxJQUFJRixlQUFlLFFBQVE7Z0JBQ3pCbkYsbUJBQW1CO1lBQ3JCO1FBQ0Y7OEJBQUcsRUFBRTtJQUVMLE1BQU1zRixjQUFjLENBQUMzQjtRQUNuQkEsRUFBRUMsY0FBYztRQUNoQnRELGNBQWM7UUFFZCxJQUFJTCxlQUFlLDRCQUE0QkUsa0JBQWtCLFNBQVM7WUFDeEVILG1CQUFtQjtZQUNuQm9GLGFBQWFHLE9BQU8sQ0FBQyxzQkFBc0I7WUFDM0NyRixjQUFjO1lBQ2RFLGlCQUFpQjtRQUNuQixPQUFPO1lBQ0xFLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU1rRixlQUFlO1FBQ25CeEYsbUJBQW1CO1FBQ25Cb0YsYUFBYUssVUFBVSxDQUFDO0lBQzFCO0lBRUE5RixnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUNJLGlCQUFpQjtZQUV0QixNQUFNMkY7bURBQWM7b0JBQ2xCLElBQUk7d0JBQ0YsTUFBTW5ELFdBQVcsTUFBTUMsTUFBTTt3QkFDN0IsTUFBTWUsT0FBTyxNQUFNaEIsU0FBU2lCLElBQUk7d0JBRWhDLElBQUlELEtBQUtFLE9BQU8sSUFBSUYsS0FBS0EsSUFBSSxFQUFFOzRCQUM3QjlDLFVBQVU4QyxLQUFLQSxJQUFJO3dCQUNyQixPQUFPOzRCQUNMeEMsU0FBUzt3QkFDWDtvQkFDRixFQUFFLE9BQU9ELE9BQU87d0JBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLDBCQUEwQkE7d0JBQ3hDQyxTQUFTO29CQUNYO2dCQUNGOztZQUVBMkU7UUFDRjs4QkFBRztRQUFDM0Y7S0FBZ0I7SUFFcEJKLGdEQUFTQTsrQkFBQztZQUNSLE1BQU1nRztxREFBZ0I7b0JBQ3BCLElBQUk7d0JBQ0Y5RSxXQUFXO3dCQUNYRSxTQUFTO3dCQUVULE1BQU13QixXQUFXLE1BQU1DLE1BQU0sR0FBbURqQyxPQUFoRHFGLDJCQUErQixFQUFDLGtCQUE2QixPQUFickY7d0JBQ2hGLE1BQU1nRCxPQUFPLE1BQU1oQixTQUFTaUIsSUFBSTt3QkFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFOzRCQUNoQjlDLFlBQVk0QyxLQUFLQSxJQUFJO3dCQUN2QixPQUFPOzRCQUNMeEMsU0FBUzt3QkFDWDtvQkFDRixFQUFFLE9BQU9ELE9BQU87d0JBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLDRCQUE0QkE7d0JBQzFDQyxTQUFTO29CQUNYLFNBQVU7d0JBQ1JGLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUEsSUFBSU4sY0FBYztnQkFDaEJvRjtZQUNGO1FBQ0Y7OEJBQUc7UUFBQ3BGO0tBQWE7SUFFakIsa0NBQWtDO0lBQ2xDWixnREFBU0E7K0JBQUM7WUFDUixJQUFJSSxtQkFBbUJRLGNBQWM7Z0JBQ25DK0M7WUFDRjtRQUNGOzhCQUFHO1FBQUMvQztRQUFjUjtLQUFnQjtJQUVsQyxNQUFNZ0csaUJBQWlCLENBQUNqRDtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBZSxPQUFPO1lBQzNCLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFhLE9BQU87WUFDekI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTWtELGFBQWEsQ0FBQ0M7UUFDbEIsT0FBTyxJQUFJQyxLQUFLRCxZQUFZRSxrQkFBa0IsQ0FBQyxTQUFTO1lBQ3REQyxLQUFLO1lBQ0xDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFFBQVE7UUFDVjtJQUNGO0lBRUEsdUNBQXVDO0lBQ3ZDLElBQUksQ0FBQ3pHLGlCQUFpQjtRQUNwQixxQkFDRSw4REFBQzBHO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUtELFdBQVU7OENBQWdDOzs7Ozs7Ozs7OzswQ0FFbEQsOERBQUNFO2dDQUFHRixXQUFVOzBDQUFzRDs7Ozs7OzBDQUNwRSw4REFBQ0c7Z0NBQUVILFdBQVU7MENBQW1COzs7Ozs7Ozs7Ozs7a0NBR2xDLDhEQUFDSTt3QkFBS0MsVUFBVXpCO3dCQUFhb0IsV0FBVTs7MENBQ3JDLDhEQUFDRDs7a0RBQ0MsOERBQUNPO3dDQUFNTixXQUFVO2tEQUFrRDs7Ozs7O2tEQUduRSw4REFBQ087d0NBQ0NDLE1BQUs7d0NBQ0xDLE9BQU9sSDt3Q0FDUG1ILFVBQVUsQ0FBQ3pELElBQU16RCxjQUFjeUQsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3Q0FDN0NULFdBQVU7d0NBQ1ZZLGFBQVk7d0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OzswQ0FJWiw4REFBQ2Q7O2tEQUNDLDhEQUFDTzt3Q0FBTU4sV0FBVTtrREFBa0Q7Ozs7OztrREFHbkUsOERBQUNPO3dDQUNDQyxNQUFLO3dDQUNMQyxPQUFPaEg7d0NBQ1BpSCxVQUFVLENBQUN6RCxJQUFNdkQsaUJBQWlCdUQsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3Q0FDaERULFdBQVU7d0NBQ1ZZLGFBQVk7d0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs0QkFJWGxILDRCQUNDLDhEQUFDb0c7Z0NBQUlDLFdBQVU7MENBQ1pyRzs7Ozs7OzBDQUlMLDhEQUFDbUg7Z0NBQ0NOLE1BQUs7Z0NBQ0xSLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT1g7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNlO2dCQUFPZixXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUtELFdBQVU7c0RBQStCOzs7Ozs7Ozs7OztrREFFakQsOERBQUNEOzswREFDQyw4REFBQ0c7Z0RBQUdGLFdBQVU7MERBQWdEOzs7Ozs7MERBQzlELDhEQUFDRztnREFBRUgsV0FBVTswREFBMkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHNUMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUtELFdBQVU7a0RBQTJCOzs7Ozs7a0RBQzNDLDhEQUFDYzt3Q0FDQ0UsU0FBU2xDO3dDQUNUa0IsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRVCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDaUI7d0JBQU1qQixXQUFVO2tDQUNmLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNjO2dEQUNDRSxTQUFTLElBQU16RyxhQUFhO2dEQUM1QnlGLFdBQVcscUVBSVYsT0FIQzFGLGNBQWMsYUFDVix3Q0FDQTswREFFUDs7Ozs7OzBEQUdELDhEQUFDd0c7Z0RBQ0NFLFNBQVMsSUFBTXpHLGFBQWE7Z0RBQzVCeUYsV0FBVyxxRUFJVixPQUhDMUYsY0FBYyxVQUNWLHdDQUNBOzBEQUVQOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNTCw4REFBQzRHO29DQUFHbEIsV0FBVTs4Q0FBOEM7Ozs7Ozs4Q0FDNUQsOERBQUNtQjtvQ0FBSW5CLFdBQVU7OENBQ1o1RyxPQUFPZ0ksTUFBTSxLQUFLLGtCQUNqQiw4REFBQ3JCO3dDQUFJQyxXQUFVO2tEQUEyQjs7Ozs7K0NBRTFDNUcsT0FBT21ELEdBQUcsQ0FBQyxDQUFDOEUscUJBQ1osOERBQUNQOzRDQUVDRSxTQUFTLElBQU1sSCxnQkFBZ0J1SCxLQUFLL0QsSUFBSTs0Q0FDeEMwQyxXQUFXLDJEQUlWLE9BSENuRyxpQkFBaUJ3SCxLQUFLL0QsSUFBSSxHQUN0Qix1Q0FDQTtzREFHTiw0RUFBQ3lDO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUtELFdBQVU7a0VBQWVxQixLQUFLekcsSUFBSTs7Ozs7O2tFQUN4Qyw4REFBQ3FGO3dEQUFLRCxXQUFVOzs0REFDYnFCLEtBQUtDLE1BQU0sQ0FBQzlHLFFBQVE7NERBQUM7Ozs7Ozs7Ozs7Ozs7MkNBWHJCNkcsS0FBSzVFLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FzQnRCLDhEQUFDOEU7d0JBQUt2QixXQUFVO2tDQUNkLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjFGLGNBQWMsMkJBQ2I7O2tEQUNFLDhEQUFDeUY7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRTtnREFBR0YsV0FBVTs7b0RBQXNEO3FEQUM5QzVHLGVBQUFBLE9BQU9nRSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLElBQUksS0FBS3pELDJCQUE1QlQsbUNBQUFBLGFBQTJDd0IsSUFBSTs7Ozs7OzswREFFckUsOERBQUN1RjtnREFBRUgsV0FBVTswREFBbUI7Ozs7Ozs7Ozs7OztvQ0FLckM5Rix3QkFDQyw4REFBQzZGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQXdCOzs7Ozs7Ozs7OzsrQ0FFckM1RixzQkFDRiw4REFBQzJGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQTRCOzs7Ozs7MERBQzNDLDhEQUFDRztnREFBRUgsV0FBVTswREFBZ0I1Rjs7Ozs7Ozs7Ozs7K0NBRTdCSixTQUFTb0gsTUFBTSxLQUFLLGtCQUN0Qiw4REFBQ3JCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQWdCOzs7Ozs7MERBQy9CLDhEQUFDd0I7Z0RBQUd4QixXQUFVOzBEQUE4Qzs7Ozs7OzBEQUM1RCw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQW1COzs7Ozs7Ozs7Ozs2REFHbEMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ3lCO2dEQUFNekIsV0FBVTs7a0VBQ2YsOERBQUMwQjt3REFBTTFCLFdBQVU7a0VBQ2YsNEVBQUMyQjs7OEVBQ0MsOERBQUNDO29FQUFHNUIsV0FBVTs4RUFBc0Y7Ozs7Ozs4RUFHcEcsOERBQUM0QjtvRUFBRzVCLFdBQVU7OEVBQXNGOzs7Ozs7OEVBR3BHLDhEQUFDNEI7b0VBQUc1QixXQUFVOzhFQUFzRjs7Ozs7OzhFQUdwRyw4REFBQzRCO29FQUFHNUIsV0FBVTs4RUFBc0Y7Ozs7Ozs4RUFHcEcsOERBQUM0QjtvRUFBRzVCLFdBQVU7OEVBQXNGOzs7Ozs7OEVBR3BHLDhEQUFDNEI7b0VBQUc1QixXQUFVOzhFQUFzRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS3hHLDhEQUFDNkI7d0RBQU03QixXQUFVO2tFQUNkaEcsU0FBU3VDLEdBQUcsQ0FBQyxDQUFDQyx3QkFDYiw4REFBQ21GO2dFQUFvQjNCLFdBQVU7O2tGQUM3Qiw4REFBQzhCO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7OzhGQUNDLDhEQUFDQTtvRkFBSUMsV0FBVTs7d0ZBQ1p4RCxRQUFRdUYsU0FBUzt3RkFBQzt3RkFBRXZGLFFBQVF3RixRQUFROzs7Ozs7OzhGQUV2Qyw4REFBQ2pDO29GQUFJQyxXQUFVOzhGQUE0QnhELFFBQVF5RixLQUFLOzs7Ozs7OEZBQ3hELDhEQUFDbEM7b0ZBQUlDLFdBQVU7OEZBQTRCeEQsUUFBUTBGLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUc1RCw4REFBQ0o7d0VBQUc5QixXQUFVO2tGQUNaLDRFQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOzt3RkFBYzt3RkFBT3hELFFBQVEyRixjQUFjOzs7Ozs7OzhGQUMxRCw4REFBQ3BDOzt3RkFBSTt3RkFBS3ZELFFBQVE0RixlQUFlOzs7Ozs7OzhGQUNqQyw4REFBQ3JDO29GQUFJQyxXQUFVOzt3RkFDWnhELFFBQVE2RixVQUFVO3dGQUFDO3dGQUFXN0YsUUFBUTZGLFVBQVUsR0FBRyxJQUFJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFJcEUsOERBQUNQO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ1p4RCxRQUFRNEIsT0FBTyxpQkFDZDs7a0dBQ0UsOERBQUMyQjt3RkFBSUMsV0FBVTtrR0FBZXhELFFBQVE0QixPQUFPLENBQUN4RCxJQUFJOzs7Ozs7a0dBQ2xELDhEQUFDbUY7d0ZBQUlDLFdBQVU7a0dBQW9CeEQsUUFBUTRCLE9BQU8sQ0FBQ3ZELFFBQVE7Ozs7Ozs7NkdBRzdELDhEQUFDb0Y7Z0ZBQUtELFdBQVU7MEZBQW1COzs7Ozs7Ozs7Ozs7Ozs7O2tGQUl6Qyw4REFBQzhCO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDs4RkFBSyxJQUFJUCxLQUFLaEQsUUFBUThGLFVBQVUsRUFBRTdDLGtCQUFrQixDQUFDOzs7Ozs7OEZBQ3RELDhEQUFDTTtvRkFBSUMsV0FBVTs4RkFBb0J4RCxRQUFRK0YsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBR3pELDhEQUFDVDt3RUFBRzlCLFdBQVU7a0ZBQ1osNEVBQUN3Qzs0RUFDQy9CLE9BQU9qRSxRQUFRSixNQUFNOzRFQUNyQnNFLFVBQVUsQ0FBQ3pELElBQU12QixvQkFBb0JjLFFBQVFDLEVBQUUsRUFBRVEsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzs0RUFDL0RULFdBQVcsNEZBQTJILE9BQS9CWCxlQUFlN0MsUUFBUUosTUFBTTs7OEZBRXBJLDhEQUFDcUc7b0ZBQU9oQyxPQUFNOzhGQUFVOzs7Ozs7OEZBQ3hCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFZOzs7Ozs7OEZBQzFCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFjOzs7Ozs7OEZBQzVCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFZOzs7Ozs7OEZBQzFCLDhEQUFDZ0M7b0ZBQU9oQyxPQUFNOzhGQUFZOzs7Ozs7Ozs7Ozs7Ozs7OztrRkFHOUIsOERBQUNxQjt3RUFBRzlCLFdBQVU7a0ZBQ1hWLFdBQVc5QyxRQUFRa0csU0FBUzs7Ozs7OzsrREFuRHhCbEcsUUFBUUMsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQThEN0I7O2tEQUNFLDhEQUFDc0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDRzs0REFBR0YsV0FBVTs7Z0VBQXNEO2lFQUM5QzVHLGdCQUFBQSxPQUFPZ0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUt6RCwyQkFBNUJULG9DQUFBQSxjQUEyQ3dCLElBQUk7Ozs7Ozs7c0VBRXJFLDhEQUFDdUY7NERBQUVILFdBQVU7c0VBQW1COzs7Ozs7Ozs7Ozs7OERBSWxDLDhEQUFDYztvREFDQ0UsU0FBUyxJQUFNdkYsaUJBQWlCLENBQUNEO29EQUNqQ3dFLFdBQVU7OERBRVR4RSxnQkFBZ0IsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTWxDLDhEQUFDdUU7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2tCO29EQUFHbEIsV0FBVTs4REFBeUM7Ozs7Ozs7Ozs7OzBEQUV6RCw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUN5QjtvREFBTXpCLFdBQVU7O3NFQUNmLDhEQUFDMEI7NERBQU0xQixXQUFVO3NFQUNmLDRFQUFDMkI7O2tGQUNDLDhEQUFDQzt3RUFBRzVCLFdBQVU7a0ZBQW9GOzs7Ozs7a0ZBR2xHLDhEQUFDNEI7d0VBQUc1QixXQUFVO2tGQUFvRjs7Ozs7O2tGQUdsRyw4REFBQzRCO3dFQUFHNUIsV0FBVTtrRkFBb0Y7Ozs7OztrRkFHbEcsOERBQUM0Qjt3RUFBRzVCLFdBQVU7a0ZBQW9GOzs7Ozs7a0ZBR2xHLDhEQUFDNEI7d0VBQUc1QixXQUFVO2tGQUFvRjs7Ozs7O2tGQUdsRyw4REFBQzRCO3dFQUFHNUIsV0FBVTtrRkFBb0Y7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUt0Ryw4REFBQzZCOzREQUFNN0IsV0FBVTs7Z0VBQ2R4RixTQUFTK0IsR0FBRyxDQUFDLENBQUM2Qix3QkFDYiw4REFBQ3VEO3dFQUFvQjNCLFdBQVU7OzBGQUM3Qiw4REFBQzhCO2dGQUFHOUIsV0FBVTswRkFDWiw0RUFBQ0Q7b0ZBQUlDLFdBQVU7O3dGQUNaNUIsUUFBUW5ELEtBQUssa0JBQ1osOERBQUMwSDs0RkFBSTNDLFdBQVU7NEZBQXlDNEMsS0FBS3hFLFFBQVFuRCxLQUFLOzRGQUFFNEgsS0FBS3pFLFFBQVF4RCxJQUFJOzs7Ozs7c0dBRS9GLDhEQUFDbUY7OzhHQUNDLDhEQUFDQTtvR0FBSUMsV0FBVTs4R0FBd0M1QixRQUFReEQsSUFBSTs7Ozs7OzhHQUNuRSw4REFBQ21GO29HQUFJQyxXQUFVOzhHQUE4QzVCLFFBQVFyRCxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRkFJdEYsOERBQUMrRztnRkFBRzlCLFdBQVU7MEZBQ1osNEVBQUNDO29GQUFLRCxXQUFVOzhGQUNiNUIsUUFBUXZELFFBQVEsQ0FBQ2lJLE9BQU8sQ0FBQyxLQUFLOzs7Ozs7Ozs7OzswRkFHbkMsOERBQUNoQjtnRkFBRzlCLFdBQVU7MEZBQ1g1QixRQUFRdEQsUUFBUTs7Ozs7OzBGQUVuQiw4REFBQ2dIO2dGQUFHOUIsV0FBVTswRkFDWDVCLFFBQVFsRCxVQUFVOzs7Ozs7MEZBRXJCLDhEQUFDNEc7Z0ZBQUc5QixXQUFVOzBGQUNaLDRFQUFDQztvRkFBS0QsV0FBVyw0REFJaEIsT0FIQzVCLFFBQVFqRCxRQUFRLEdBQ1osZ0NBQ0E7OEZBRUhpRCxRQUFRakQsUUFBUSxHQUFHLFdBQVc7Ozs7Ozs7Ozs7OzBGQUduQyw4REFBQzJHO2dGQUFHOUIsV0FBVTs7a0dBQ1osOERBQUNjO3dGQUNDRSxTQUFTLElBQU03QyxrQkFBa0JDO3dGQUNqQzRCLFdBQVU7a0dBQ1g7Ozs7OztrR0FHRCw4REFBQ2M7d0ZBQ0NFLFNBQVMsSUFBTTFDLG9CQUFvQkYsUUFBUTNCLEVBQUU7d0ZBQzdDdUQsV0FBVTtrR0FDWDs7Ozs7Ozs7Ozs7Ozt1RUExQ0k1QixRQUFRM0IsRUFBRTs7Ozs7Z0VBZ0RwQmpDLFNBQVM0RyxNQUFNLEtBQUssbUJBQ25CLDhEQUFDTzs4RUFDQyw0RUFBQ0c7d0VBQUdpQixTQUFTO3dFQUFHL0MsV0FBVTtrRkFBeUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBVy9FLDhEQUFDRDt3Q0FBSUMsV0FBVTs7NENBQWdDOzRDQUM5QnhFLGdCQUFnQixRQUFROzRDQUFLOzRDQUFhRixrQkFBa0I7Ozs7Ozs7b0NBSTVFRSwrQkFDQyw4REFBQ3VFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDa0I7d0RBQUdsQixXQUFVO2tFQUNYMUUsaUJBQWlCLGlCQUFpQjs7Ozs7O2tFQUVyQyw4REFBQzZFO3dEQUFFSCxXQUFVO2tFQUNWMUUsaUJBQWlCLCtCQUErQjs7Ozs7Ozs7Ozs7OzBEQUdyRCw4REFBQzhFO2dEQUFLQyxVQUFVckQ7Z0RBQW1CZ0QsV0FBVTs7a0VBQzdDLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEOztrRkFDQyw4REFBQ087d0VBQU1OLFdBQVU7a0ZBQWtEOzs7Ozs7a0ZBR25FLDhEQUFDTzt3RUFDQ0MsTUFBSzt3RUFDTEMsT0FBTy9GLFVBQVVFLElBQUk7d0VBQ3JCOEYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTO29GQUFFLEdBQUdBLElBQUk7b0ZBQUUxQixNQUFNcUMsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSztnRkFBQzt3RUFDdkVULFdBQVU7d0VBQ1ZZLGFBQVk7d0VBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OzswRUFJWiw4REFBQ2Q7O2tGQUNDLDhEQUFDTzt3RUFBTU4sV0FBVTtrRkFBa0Q7Ozs7OztrRkFHbkUsOERBQUN3Qzt3RUFDQy9CLE9BQU8vRixVQUFVRyxRQUFRO3dFQUN6QjZGLFVBQVUsQ0FBQ3pELElBQU10QyxhQUFhMkIsQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFekIsVUFBVW9DLEVBQUUwRCxNQUFNLENBQUNGLEtBQUs7Z0ZBQUM7d0VBQzNFVCxXQUFVO3dFQUNWYSxRQUFROzswRkFFUiw4REFBQzRCO2dGQUFPaEMsT0FBTTswRkFBYzs7Ozs7OzBGQUM1Qiw4REFBQ2dDO2dGQUFPaEMsT0FBTTswRkFBTTs7Ozs7OzRFQUNuQjVHLGlCQUFpQiwwQkFDaEI7O2tHQUNFLDhEQUFDNEk7d0ZBQU9oQyxPQUFNO2tHQUFhOzs7Ozs7a0dBQzNCLDhEQUFDZ0M7d0ZBQU9oQyxPQUFNO2tHQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRUFNcEMsOERBQUNWOztrRkFDQyw4REFBQ087d0VBQU1OLFdBQVU7a0ZBQWtEOzs7Ozs7a0ZBR25FLDhEQUFDTzt3RUFDQ0MsTUFBSzt3RUFDTEMsT0FBTy9GLFVBQVVJLFFBQVE7d0VBQ3pCNEYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTO29GQUFFLEdBQUdBLElBQUk7b0ZBQUV4QixVQUFVbUMsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSztnRkFBQzt3RUFDM0VULFdBQVU7d0VBQ1ZZLGFBQVk7d0VBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OzswRUFJWiw4REFBQ2Q7O2tGQUNDLDhEQUFDTzt3RUFBTU4sV0FBVTtrRkFBa0Q7Ozs7OztrRkFHbkUsOERBQUNPO3dFQUNDQyxNQUFLO3dFQUNMQyxPQUFPL0YsVUFBVVEsVUFBVTt3RUFDM0J3RixVQUFVLENBQUN6RCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRXBCLFlBQVkrQixFQUFFMEQsTUFBTSxDQUFDRixLQUFLO2dGQUFDO3dFQUM3RVQsV0FBVTt3RUFDVlksYUFBWTt3RUFDWkMsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUtkLDhEQUFDZDs7MEVBQ0MsOERBQUNPO2dFQUFNTixXQUFVOzBFQUFrRDs7Ozs7OzBFQUduRSw4REFBQ2dEO2dFQUNDdkMsT0FBTy9GLFVBQVVLLFdBQVc7Z0VBQzVCMkYsVUFBVSxDQUFDekQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUV2QixhQUFha0MsRUFBRTBELE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztnRUFDOUV3QyxNQUFNO2dFQUNOakQsV0FBVTtnRUFDVlksYUFBWTtnRUFDWkMsUUFBUTs7Ozs7Ozs7Ozs7O2tFQUlaLDhEQUFDZDs7MEVBQ0MsOERBQUNPO2dFQUFNTixXQUFVOzBFQUFrRDs7Ozs7OzBFQUduRSw4REFBQ087Z0VBQ0NDLE1BQUs7Z0VBQ0xDLE9BQU8vRixVQUFVTSxRQUFRO2dFQUN6QjBGLFVBQVUsQ0FBQ3pELElBQU10QyxhQUFhMkIsQ0FBQUEsT0FBUzs0RUFBRSxHQUFHQSxJQUFJOzRFQUFFdEIsVUFBVWlDLEVBQUUwRCxNQUFNLENBQUNGLEtBQUs7d0VBQUM7Z0VBQzNFVCxXQUFVO2dFQUNWWSxhQUFZOzs7Ozs7Ozs7Ozs7a0VBSWhCLDhEQUFDYjs7MEVBQ0MsOERBQUNPO2dFQUFNTixXQUFVOzBFQUFrRDs7Ozs7OzBFQUduRSw4REFBQ087Z0VBQ0NDLE1BQUs7Z0VBQ0xDLE9BQU8vRixVQUFVTyxLQUFLO2dFQUN0QnlGLFVBQVUsQ0FBQ3pELElBQU10QyxhQUFhMkIsQ0FBQUEsT0FBUzs0RUFBRSxHQUFHQSxJQUFJOzRFQUFFckIsT0FBT2dDLEVBQUUwRCxNQUFNLENBQUNGLEtBQUs7d0VBQUM7Z0VBQ3hFVCxXQUFVO2dFQUNWWSxhQUFZOzs7Ozs7Ozs7Ozs7a0VBSWhCLDhEQUFDYjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNPO2dFQUNDQyxNQUFLO2dFQUNML0QsSUFBRztnRUFDSHlHLFNBQVN4SSxVQUFVUyxRQUFRO2dFQUMzQnVGLFVBQVUsQ0FBQ3pELElBQU10QyxhQUFhMkIsQ0FBQUEsT0FBUzs0RUFBRSxHQUFHQSxJQUFJOzRFQUFFbkIsVUFBVThCLEVBQUUwRCxNQUFNLENBQUN1QyxPQUFPO3dFQUFDO2dFQUM3RWxELFdBQVU7Ozs7OzswRUFFWiw4REFBQ007Z0VBQU02QyxTQUFRO2dFQUFXbkQsV0FBVTswRUFBc0M7Ozs7Ozs7Ozs7OztrRUFLNUUsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2M7Z0VBQ0NOLE1BQUs7Z0VBQ0xRLFNBQVNsRDtnRUFDVGtDLFdBQVU7MEVBQ1g7Ozs7OzswRUFHRCw4REFBQ2M7Z0VBQ0NOLE1BQUs7Z0VBQ0w0QyxVQUFVaEk7Z0VBQ1Y0RSxXQUFXLGlGQUlWLE9BSEM1RSxrQkFDSSxrQ0FDQTswRUFHTEEsa0JBQ0lFLGlCQUFpQixnQkFBZ0IsY0FDakNBLGlCQUFpQixtQkFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWNqRTtHQTl6QndCbkM7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvR2VvLVRyYXZlbHMvZnJvbnRlbmQvc3JjL2FwcC9hZG1pbi9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhcGlTZXJ2aWNlLCBDaXR5IH0gZnJvbSAnQC9zZXJ2aWNlcy9hcGknO1xuXG5pbnRlcmZhY2UgQm9va2luZ1JlcXVlc3Qge1xuICBpZDogc3RyaW5nO1xuICBmaXJzdE5hbWU6IHN0cmluZztcbiAgbGFzdE5hbWU6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgcGhvbmU6IHN0cmluZztcbiAgcGlja3VwTG9jYXRpb246IHN0cmluZztcbiAgZHJvcG9mZkxvY2F0aW9uOiBzdHJpbmc7XG4gIHBpY2t1cERhdGU6IHN0cmluZztcbiAgcGlja3VwVGltZTogc3RyaW5nO1xuICBwYXNzZW5nZXJzOiBudW1iZXI7XG4gIHNwZWNpYWxSZXF1ZXN0cz86IHN0cmluZztcbiAgc3RhdHVzOiAnUEVORElORycgfCAnQ09ORklSTUVEJyB8ICdJTl9QUk9HUkVTUycgfCAnQ09NUExFVEVEJyB8ICdDQU5DRUxMRUQnO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgY2l0eToge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIHNsdWc6IHN0cmluZztcbiAgfTtcbiAgdmVoaWNsZT86IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBjYXRlZ29yeTogc3RyaW5nO1xuICB9O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZG1pblBhZ2UoKSB7XG4gIGNvbnN0IFtpc0F1dGhlbnRpY2F0ZWQsIHNldElzQXV0aGVudGljYXRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsb2dpbkVtYWlsLCBzZXRMb2dpbkVtYWlsXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2xvZ2luUGFzc3dvcmQsIHNldExvZ2luUGFzc3dvcmRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbG9naW5FcnJvciwgc2V0TG9naW5FcnJvcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzZWxlY3RlZENpdHksIHNldFNlbGVjdGVkQ2l0eV0gPSB1c2VTdGF0ZSgnbG9uZG9uJyk7XG4gIGNvbnN0IFtjaXRpZXMsIHNldENpdGllc10gPSB1c2VTdGF0ZTxDaXR5W10+KFtdKTtcbiAgY29uc3QgW2Jvb2tpbmdzLCBzZXRCb29raW5nc10gPSB1c2VTdGF0ZTxCb29raW5nUmVxdWVzdFtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8J2Jvb2tpbmdzJyB8ICdmbGVldCc+KCdib29raW5ncycpO1xuXG4gIC8vIEZsZWV0IG1hbmFnZW1lbnQgc3RhdGVcbiAgY29uc3QgW3ZlaGljbGVzLCBzZXRWZWhpY2xlc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbZmxlZXRGb3JtLCBzZXRGbGVldEZvcm1dID0gdXNlU3RhdGUoe1xuICAgIG5hbWU6ICcnLFxuICAgIGNhdGVnb3J5OiAnUFJJVkFURV9DQVInLFxuICAgIGNhcGFjaXR5OiAnJyxcbiAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgZmVhdHVyZXM6ICcnLFxuICAgIGltYWdlOiAnJyxcbiAgICBwcmljZVJhbmdlOiAnJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZVxuICB9KTtcbiAgY29uc3QgW3N1Ym1pdHRpbmdGbGVldCwgc2V0U3VibWl0dGluZ0ZsZWV0XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRpbmdWZWhpY2xlLCBzZXRFZGl0aW5nVmVoaWNsZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Nob3dGbGVldEZvcm0sIHNldFNob3dGbGVldEZvcm1dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIFN0YXR1cyB1cGRhdGUgZnVuY3Rpb25hbGl0eVxuICBjb25zdCB1cGRhdGVCb29raW5nU3RhdHVzID0gYXN5bmMgKGJvb2tpbmdJZDogc3RyaW5nLCBuZXdTdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL2Jvb2tpbmcvJHtib29raW5nSWR9L3N0YXR1c2AsIHtcbiAgICAgICAgbWV0aG9kOiAnUEFUQ0gnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBzdGF0dXM6IG5ld1N0YXR1cyB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgLy8gVXBkYXRlIHRoZSBib29raW5nIGluIHRoZSBsb2NhbCBzdGF0ZVxuICAgICAgICBzZXRCb29raW5ncyhwcmV2ID0+IHByZXYubWFwKGJvb2tpbmcgPT5cbiAgICAgICAgICBib29raW5nLmlkID09PSBib29raW5nSWRcbiAgICAgICAgICAgID8geyAuLi5ib29raW5nLCBzdGF0dXM6IG5ld1N0YXR1cyBhcyBhbnkgfVxuICAgICAgICAgICAgOiBib29raW5nXG4gICAgICAgICkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWxlcnQoJ0ZhaWxlZCB0byB1cGRhdGUgYm9va2luZyBzdGF0dXMnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgc3RhdHVzOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvciB1cGRhdGluZyBib29raW5nIHN0YXR1cycpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGZXRjaCB2ZWhpY2xlcyBmb3Igc2VsZWN0ZWQgY2l0eVxuICBjb25zdCBmZXRjaFZlaGljbGVzID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghaXNBdXRoZW50aWNhdGVkIHx8ICFzZWxlY3RlZENpdHkpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzP2NpdHk9JHtzZWxlY3RlZENpdHl9YCk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgaWYgKGRhdGEuc3VjY2VzcyAmJiBkYXRhLmRhdGEpIHtcbiAgICAgICAgICBzZXRWZWhpY2xlcyhkYXRhLmRhdGEpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggdmVoaWNsZXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdmVoaWNsZXM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGbGVldCBtYW5hZ2VtZW50IGZ1bmN0aW9uc1xuICBjb25zdCBoYW5kbGVGbGVldFN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0U3VibWl0dGluZ0ZsZWV0KHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNlbGVjdGVkQ2l0eURhdGEgPSBjaXRpZXMuZmluZChjID0+IGMuc2x1ZyA9PT0gc2VsZWN0ZWRDaXR5KTtcbiAgICAgIGlmICghc2VsZWN0ZWRDaXR5RGF0YSkge1xuICAgICAgICBhbGVydCgnUGxlYXNlIHNlbGVjdCBhIGNpdHkgZmlyc3QnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBmbGVldERhdGEgPSB7XG4gICAgICAgIC4uLmZsZWV0Rm9ybSxcbiAgICAgICAgZmVhdHVyZXM6IGZsZWV0Rm9ybS5mZWF0dXJlcy5zcGxpdCgnLCcpLm1hcChmID0+IGYudHJpbSgpKS5maWx0ZXIoZiA9PiBmKSxcbiAgICAgICAgY2l0eUlkOiBzZWxlY3RlZENpdHlEYXRhLmlkXG4gICAgICB9O1xuXG4gICAgICBjb25zdCB1cmwgPSBlZGl0aW5nVmVoaWNsZVxuICAgICAgICA/IGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzLyR7ZWRpdGluZ1ZlaGljbGV9YFxuICAgICAgICA6ICdodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzJztcblxuICAgICAgY29uc3QgbWV0aG9kID0gZWRpdGluZ1ZlaGljbGUgPyAnUFVUJyA6ICdQT1NUJztcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgbWV0aG9kLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZmxlZXREYXRhKSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYWxlcnQoYFZlaGljbGUgJHtlZGl0aW5nVmVoaWNsZSA/ICd1cGRhdGVkJyA6ICdhZGRlZCd9IHN1Y2Nlc3NmdWxseSFgKTtcbiAgICAgICAgcmVzZXRGbGVldEZvcm0oKTtcbiAgICAgICAgLy8gUmVmcmVzaCBkYXRhXG4gICAgICAgIGNvbnN0IGNpdGllc0RhdGEgPSBhd2FpdCBhcGlTZXJ2aWNlLmdldENpdGllcygpO1xuICAgICAgICBzZXRDaXRpZXMoY2l0aWVzRGF0YSk7XG4gICAgICAgIGZldGNoVmVoaWNsZXMoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgYWxlcnQoYEZhaWxlZCB0byAke2VkaXRpbmdWZWhpY2xlID8gJ3VwZGF0ZScgOiAnYWRkJ30gdmVoaWNsZTogJHtlcnJvckRhdGEubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcid9YCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyB2ZWhpY2xlOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvciBhZGRpbmcgdmVoaWNsZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0U3VibWl0dGluZ0ZsZWV0KGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVzZXRGbGVldEZvcm0gPSAoKSA9PiB7XG4gICAgc2V0RmxlZXRGb3JtKHtcbiAgICAgIG5hbWU6ICcnLFxuICAgICAgY2F0ZWdvcnk6ICdQUklWQVRFX0NBUicsXG4gICAgICBjYXBhY2l0eTogJycsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBmZWF0dXJlczogJycsXG4gICAgICBpbWFnZTogJycsXG4gICAgICBwcmljZVJhbmdlOiAnJyxcbiAgICAgIGlzQWN0aXZlOiB0cnVlXG4gICAgfSk7XG4gICAgc2V0RWRpdGluZ1ZlaGljbGUobnVsbCk7XG4gICAgc2V0U2hvd0ZsZWV0Rm9ybShmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdFZlaGljbGUgPSAodmVoaWNsZTogYW55KSA9PiB7XG4gICAgc2V0RmxlZXRGb3JtKHtcbiAgICAgIG5hbWU6IHZlaGljbGUubmFtZSxcbiAgICAgIGNhdGVnb3J5OiB2ZWhpY2xlLmNhdGVnb3J5LFxuICAgICAgY2FwYWNpdHk6IHZlaGljbGUuY2FwYWNpdHksXG4gICAgICBkZXNjcmlwdGlvbjogdmVoaWNsZS5kZXNjcmlwdGlvbixcbiAgICAgIGZlYXR1cmVzOiB2ZWhpY2xlLmZlYXR1cmVzLmpvaW4oJywgJyksXG4gICAgICBpbWFnZTogdmVoaWNsZS5pbWFnZSB8fCAnJyxcbiAgICAgIHByaWNlUmFuZ2U6IHZlaGljbGUucHJpY2VSYW5nZSxcbiAgICAgIGlzQWN0aXZlOiB2ZWhpY2xlLmlzQWN0aXZlXG4gICAgfSk7XG4gICAgc2V0RWRpdGluZ1ZlaGljbGUodmVoaWNsZS5pZCk7XG4gICAgc2V0U2hvd0ZsZWV0Rm9ybSh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVWZWhpY2xlID0gYXN5bmMgKHZlaGljbGVJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgdmVoaWNsZT8nKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzLyR7dmVoaWNsZUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYWxlcnQoJ1ZlaGljbGUgZGVsZXRlZCBzdWNjZXNzZnVsbHkhJyk7XG4gICAgICAgIC8vIFJlZnJlc2ggZGF0YVxuICAgICAgICBjb25zdCBjaXRpZXNEYXRhID0gYXdhaXQgYXBpU2VydmljZS5nZXRDaXRpZXMoKTtcbiAgICAgICAgc2V0Q2l0aWVzKGNpdGllc0RhdGEpO1xuICAgICAgICBmZXRjaFZlaGljbGVzKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGFsZXJ0KGBGYWlsZWQgdG8gZGVsZXRlIHZlaGljbGU6ICR7ZXJyb3JEYXRhLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InfWApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyB2ZWhpY2xlOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvciBkZWxldGluZyB2ZWhpY2xlLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH1cbiAgfTtcblxuICAvLyBDaGVjayBpZiB1c2VyIGlzIGFscmVhZHkgYXV0aGVudGljYXRlZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGF1dGhTdGF0dXMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaW5BdXRoZW50aWNhdGVkJyk7XG4gICAgaWYgKGF1dGhTdGF0dXMgPT09ICd0cnVlJykge1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZUxvZ2luID0gKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRMb2dpbkVycm9yKCcnKTtcblxuICAgIGlmIChsb2dpbkVtYWlsID09PSAnYWRtaW5AZ29nZW90cmF2ZWxzLmNvbScgJiYgbG9naW5QYXNzd29yZCA9PT0gJ2FkbWluJykge1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FkbWluQXV0aGVudGljYXRlZCcsICd0cnVlJyk7XG4gICAgICBzZXRMb2dpbkVtYWlsKCcnKTtcbiAgICAgIHNldExvZ2luUGFzc3dvcmQoJycpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRMb2dpbkVycm9yKCdJbnZhbGlkIGVtYWlsIG9yIHBhc3N3b3JkJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcbiAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoZmFsc2UpO1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhZG1pbkF1dGhlbnRpY2F0ZWQnKTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNBdXRoZW50aWNhdGVkKSByZXR1cm47XG5cbiAgICBjb25zdCBmZXRjaENpdGllcyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvY2l0aWVzJyk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgaWYgKGRhdGEuc3VjY2VzcyAmJiBkYXRhLmRhdGEpIHtcbiAgICAgICAgICBzZXRDaXRpZXMoZGF0YS5kYXRhKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGxvYWQgY2l0aWVzJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNpdGllczonLCBlcnJvcik7XG4gICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBjaXRpZXMnKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZmV0Y2hDaXRpZXMoKTtcbiAgfSwgW2lzQXV0aGVudGljYXRlZF0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgZmV0Y2hCb29raW5ncyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTH0vYm9va2luZz9jaXR5PSR7c2VsZWN0ZWRDaXR5fWApO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBzZXRCb29raW5ncyhkYXRhLmRhdGEpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBib29raW5ncycpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBib29raW5nczonLCBlcnJvcik7XG4gICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBib29raW5ncycpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIGlmIChzZWxlY3RlZENpdHkpIHtcbiAgICAgIGZldGNoQm9va2luZ3MoKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZENpdHldKTtcblxuICAvLyBTZXBhcmF0ZSB1c2VFZmZlY3QgZm9yIHZlaGljbGVzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzQXV0aGVudGljYXRlZCAmJiBzZWxlY3RlZENpdHkpIHtcbiAgICAgIGZldGNoVmVoaWNsZXMoKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZENpdHksIGlzQXV0aGVudGljYXRlZF0pO1xuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ1BFTkRJTkcnOiByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJztcbiAgICAgIGNhc2UgJ0NPTkZJUk1FRCc6IHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCc7XG4gICAgICBjYXNlICdJTl9QUk9HUkVTUyc6IHJldHVybiAnYmctcHVycGxlLTEwMCB0ZXh0LXB1cnBsZS04MDAnO1xuICAgICAgY2FzZSAnQ09NUExFVEVEJzogcmV0dXJuICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnO1xuICAgICAgY2FzZSAnQ0FOQ0VMTEVEJzogcmV0dXJuICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXREYXRlID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBuZXcgRGF0ZShkYXRlU3RyaW5nKS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLUdCJywge1xuICAgICAgZGF5OiAnMi1kaWdpdCcsXG4gICAgICBtb250aDogJ3Nob3J0JyxcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnXG4gICAgfSk7XG4gIH07XG5cbiAgLy8gU2hvdyBsb2dpbiBmb3JtIGlmIG5vdCBhdXRoZW50aWNhdGVkXG4gIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHJpbWFyeS01MCB0by1hY2NlbnQtNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTJ4bCBzaGFkb3ctZWxlZ2FudC14bCBwLTggdy1mdWxsIG1heC13LW1kXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi04XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1hY2NlbnQtZ3JhZGllbnQgcm91bmRlZC0yeGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtMnhsXCI+Rzwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VyaWYgZm9udC1ib2xkIHRleHQtcHJpbWFyeS05MDAgbWItMlwiPkFkbWluIFBvcnRhbDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwXCI+R29HZW8gVHJhdmVscyBMb25kb248L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlTG9naW59IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgRW1haWwgQWRkcmVzc1xuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtsb2dpbkVtYWlsfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TG9naW5FbWFpbChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImFkbWluQGdvZ2VvdHJhdmVscy5jb21cIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgUGFzc3dvcmRcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bG9naW5QYXNzd29yZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldExvZ2luUGFzc3dvcmQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIHBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHtsb2dpbkVycm9yICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTcwMCBweC00IHB5LTMgcm91bmRlZC1sZyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAge2xvZ2luRXJyb3J9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWFjY2VudC01MDAgdGV4dC13aGl0ZSBweS0zIHB4LTQgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLWFjY2VudC02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgU2lnbiBJblxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9mb3JtPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLW5ldXRyYWwtNTBcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1sZyBib3JkZXItYiBib3JkZXItbmV1dHJhbC0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGgtMTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWFjY2VudC1ncmFkaWVudCByb3VuZGVkLXhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC1sZ1wiPkc8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VyaWYgZm9udC1ib2xkIHRleHQtcHJpbWFyeS05MDBcIj5Hb0dlbyBUcmF2ZWxzPC9oMT5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS02MDBcIj5BZG1pbiBQb3J0YWw8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS03MDBcIj5XZWxjb21lLCBBZG1pbjwvc3Bhbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUxvZ291dH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYWNjZW50LTYwMCBob3Zlcjp0ZXh0LWFjY2VudC03MDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgTG9nb3V0XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgICAgPGFzaWRlIGNsYXNzTmFtZT1cInctNjQgYmctd2hpdGUgc2hhZG93LWxnIGgtc2NyZWVuIHN0aWNreSB0b3AtMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICB7LyogTmF2aWdhdGlvbiBUYWJzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTEgYmctbmV1dHJhbC0xMDAgcm91bmRlZC1sZyBwLTFcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIoJ2Jvb2tpbmdzJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4LTEgcHktMiBweC0zIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09ICdib29raW5ncydcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13aGl0ZSB0ZXh0LXByaW1hcnktOTAwIHNoYWRvdy1zbSdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXByaW1hcnktNjAwIGhvdmVyOnRleHQtcHJpbWFyeS05MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBCb29raW5nc1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignZmxlZXQnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweS0yIHB4LTMgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2ZsZWV0J1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtcHJpbWFyeS05MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcHJpbWFyeS02MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEZsZWV0XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi02XCI+Q2l0aWVzPC9oMj5cbiAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIHtjaXRpZXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMCB0ZXh0LXNtXCI+TG9hZGluZyBjaXRpZXMuLi48L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBjaXRpZXMubWFwKChjaXR5KSA9PiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXtjaXR5LmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRDaXR5KGNpdHkuc2x1Zyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgdGV4dC1sZWZ0IHB4LTQgcHktMyByb3VuZGVkLXhsIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkQ2l0eSA9PT0gY2l0eS5zbHVnXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctYWNjZW50LTUwMCB0ZXh0LXdoaXRlIHNoYWRvdy1tZCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXByaW1hcnktNzAwIGhvdmVyOmJnLXByaW1hcnktNTAgaG92ZXI6dGV4dC1wcmltYXJ5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2NpdHkubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gb3BhY2l0eS03NVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjaXR5Ll9jb3VudC52ZWhpY2xlc30gdmVoaWNsZXNcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2FzaWRlPlxuXG4gICAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBwLThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICB7YWN0aXZlVGFiID09PSAnYm9va2luZ3MnID8gKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtc2VyaWYgZm9udC1ib2xkIHRleHQtcHJpbWFyeS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICBCb29raW5nIFJlcXVlc3RzIC0ge2NpdGllcy5maW5kKGMgPT4gYy5zbHVnID09PSBzZWxlY3RlZENpdHkpPy5uYW1lfVxuICAgICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgTWFuYWdlIGFuZCB0cmFjayBhbGwgYm9va2luZyByZXF1ZXN0cyBmb3IgdGhpcyBjaXR5XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1hY2NlbnQtNTAwIG14LWF1dG9cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwIG10LTRcIj5Mb2FkaW5nIGJvb2tpbmdzLi4uPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiBlcnJvciA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNTAwIHRleHQteGwgbWItNFwiPuKaoO+4jzwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMFwiPntlcnJvcn08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IGJvb2tpbmdzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC02eGwgbWItNFwiPvCfk4s8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgbWItMlwiPk5vIGJvb2tpbmdzIGZvdW5kPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwXCI+Tm8gYm9va2luZyByZXF1ZXN0cyBmb3IgdGhpcyBjaXR5IHlldC48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLTN4bCBlbGVnYW50LXNoYWRvdy1sZyBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEN1c3RvbWVyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEpvdXJuZXlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVmVoaWNsZVxuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBEYXRlICYgVGltZVxuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ3JlYXRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Jvb2tpbmdzLm1hcCgoYm9va2luZykgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17Ym9va2luZy5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctZ3JheS01MCBzbW9vdGgtdHJhbnNpdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Jvb2tpbmcuZmlyc3ROYW1lfSB7Ym9va2luZy5sYXN0TmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS02MDBcIj57Ym9va2luZy5lbWFpbH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXByaW1hcnktNjAwXCI+e2Jvb2tpbmcucGhvbmV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPkZyb206IHtib29raW5nLnBpY2t1cExvY2F0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5Ubzoge2Jvb2tpbmcuZHJvcG9mZkxvY2F0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Jvb2tpbmcucGFzc2VuZ2Vyc30gcGFzc2VuZ2Vye2Jvb2tpbmcucGFzc2VuZ2VycyA+IDEgPyAncycgOiAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtib29raW5nLnZlaGljbGUgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntib29raW5nLnZlaGljbGUubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj57Ym9va2luZy52ZWhpY2xlLmNhdGVnb3J5fTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS01MDBcIj5Ob3Qgc3BlY2lmaWVkPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PntuZXcgRGF0ZShib29raW5nLnBpY2t1cERhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tR0InKX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMFwiPntib29raW5nLnBpY2t1cFRpbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Ym9va2luZy5zdGF0dXN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUJvb2tpbmdTdGF0dXMoYm9va2luZy5pZCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgYm9yZGVyLTAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCAke2dldFN0YXR1c0NvbG9yKGJvb2tpbmcuc3RhdHVzKX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQRU5ESU5HXCI+UEVORElORzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNPTkZJUk1FRFwiPkNPTkZJUk1FRDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIklOX1BST0dSRVNTXCI+SU5fUFJPR1JFU1M8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJDT01QTEVURURcIj5DT01QTEVURUQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJDQU5DRUxMRURcIj5DQU5DRUxMRUQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtcHJpbWFyeS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZShib29raW5nLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LXNlcmlmIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEZsZWV0IE1hbmFnZW1lbnQgLSB7Y2l0aWVzLmZpbmQoYyA9PiBjLnNsdWcgPT09IHNlbGVjdGVkQ2l0eSk/Lm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBNYW5hZ2UgcHJlbWl1bSB2ZWhpY2xlcyBmb3IgdGhpcyBjaXR5XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dGbGVldEZvcm0oIXNob3dGbGVldEZvcm0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWFjY2VudC01MDAgdGV4dC13aGl0ZSBweC02IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLWFjY2VudC02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3Nob3dGbGVldEZvcm0gPyAnQ2FuY2VsJyA6ICdBZGQgTmV3IFZlaGljbGUnfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFZlaGljbGUgTGlzdCAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBtYi04XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItbmV1dHJhbC0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwXCI+Q3VycmVudCBGbGVldDwvaDI+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGRpdmlkZS15IGRpdmlkZS1uZXV0cmFsLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1uZXV0cmFsLTUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYXRlZ29yeVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2FwYWNpdHlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByaWNlIFJhbmdlXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFjdGlvbnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLW5ldXRyYWwtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZXMubWFwKCh2ZWhpY2xlKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e3ZlaGljbGUuaWR9IGNsYXNzTmFtZT1cImhvdmVyOmJnLW5ldXRyYWwtNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZWhpY2xlLmltYWdlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIGNsYXNzTmFtZT1cImgtMTAgdy0xMCByb3VuZGVkLWxnIG9iamVjdC1jb3ZlciBtci0zXCIgc3JjPXt2ZWhpY2xlLmltYWdlfSBhbHQ9e3ZlaGljbGUubmFtZX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTkwMFwiPnt2ZWhpY2xlLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS02MDAgdHJ1bmNhdGUgbWF4LXcteHNcIj57dmVoaWNsZS5kZXNjcmlwdGlvbn08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsIGJnLXByaW1hcnktMTAwIHRleHQtcHJpbWFyeS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUuY2F0ZWdvcnkucmVwbGFjZSgnXycsICcgJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUuY2FwYWNpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUucHJpY2VSYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlaGljbGUuaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2ZWhpY2xlLmlzQWN0aXZlID8gJ0FjdGl2ZScgOiAnSW5hY3RpdmUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVkaXRWZWhpY2xlKHZlaGljbGUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWFjY2VudC02MDAgaG92ZXI6dGV4dC1hY2NlbnQtOTAwIG1yLTNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBFZGl0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlVmVoaWNsZSh2ZWhpY2xlLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERlbGV0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGVzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY29sU3Bhbj17Nn0gY2xhc3NOYW1lPVwicHgtNiBweS04IHRleHQtY2VudGVyIHRleHQtcHJpbWFyeS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vIHZlaGljbGVzIGZvdW5kIGZvciB0aGlzIGNpdHkuIEFkZCB5b3VyIGZpcnN0IHZlaGljbGUgdG8gZ2V0IHN0YXJ0ZWQuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIERlYnVnIGluZm8gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHRleHQteHMgdGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgRm9ybSB2aXNpYmxlOiB7c2hvd0ZsZWV0Rm9ybSA/ICdZRVMnIDogJ05PJ30gfCBFZGl0aW5nOiB7ZWRpdGluZ1ZlaGljbGUgfHwgJ05vbmUnfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFZlaGljbGUgRm9ybSAqL31cbiAgICAgICAgICAgICAgICB7c2hvd0ZsZWV0Rm9ybSAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy1sZyBwLThcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZWRpdGluZ1ZlaGljbGUgPyAnRWRpdCBWZWhpY2xlJyA6ICdBZGQgTmV3IFZlaGljbGUnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7ZWRpdGluZ1ZlaGljbGUgPyAnVXBkYXRlIHZlaGljbGUgaW5mb3JtYXRpb24nIDogJ0FkZCBhIG5ldyB2ZWhpY2xlIHRvIHRoZSBmbGVldCd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZUZsZWV0U3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS04MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlIE5hbWUgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgTWVyY2VkZXMgUy1DbGFzc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS04MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlIENhdGVnb3J5ICpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0uY2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY2F0ZWdvcnk6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBSSVZBVEVfQ0FSXCI+UHJpdmF0ZSBDYXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkJVU1wiPlByaXZhdGUgQnVzPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZENpdHkgPT09ICdsb25kb24nICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkhFTElDT1BURVJcIj5IZWxpY29wdGVyPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUFJJVkFURV9KRVRcIj5Qcml2YXRlIEpldDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIENhcGFjaXR5ICpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmxlZXRGb3JtLmNhcGFjaXR5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZsZWV0Rm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGNhcGFjaXR5OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgNCBwYXNzZW5nZXJzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFByaWNlIFJhbmdlICpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmxlZXRGb3JtLnByaWNlUmFuZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgcHJpY2VSYW5nZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIMKjMjAwLTMwMC9ob3VyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBEZXNjcmlwdGlvbiAqXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZsZWV0Rm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGVzY3JpYmUgdGhlIHZlaGljbGUgYW5kIGl0cyBsdXh1cnkgZmVhdHVyZXMuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS04MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgRmVhdHVyZXMgKGNvbW1hLXNlcGFyYXRlZClcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5mZWF0dXJlc31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZmVhdHVyZXM6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIExlYXRoZXIgc2VhdHMsIFdpRmksIENoYW1wYWduZSBzZXJ2aWNlLCBQcm9mZXNzaW9uYWwgY2hhdWZmZXVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS04MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgSW1hZ2UgVVJMXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5pbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgaW1hZ2U6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb20vdmVoaWNsZS1pbWFnZS5qcGdcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImlzQWN0aXZlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2ZsZWV0Rm9ybS5pc0FjdGl2ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgaXNBY3RpdmU6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWFjY2VudC02MDAgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiaXNBY3RpdmVcIiBjbGFzc05hbWU9XCJtbC0yIGJsb2NrIHRleHQtc20gdGV4dC1wcmltYXJ5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgVmVoaWNsZSBpcyBhY3RpdmUgYW5kIGF2YWlsYWJsZSBmb3IgYm9va2luZ1xuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3Jlc2V0RmxlZXRGb3JtfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctbmV1dHJhbC0yMDAgdGV4dC1uZXV0cmFsLTcwMCBweC02IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLW5ldXRyYWwtMzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdWJtaXR0aW5nRmxlZXR9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BiZy1hY2NlbnQtNTAwIHRleHQtd2hpdGUgcHgtOCBweS0zIHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzdWJtaXR0aW5nRmxlZXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdvcGFjaXR5LTUwIGN1cnNvci1ub3QtYWxsb3dlZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdob3ZlcjpiZy1hY2NlbnQtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge3N1Ym1pdHRpbmdGbGVldFxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IChlZGl0aW5nVmVoaWNsZSA/ICdVcGRhdGluZy4uLicgOiAnQWRkaW5nLi4uJylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAoZWRpdGluZ1ZlaGljbGUgPyAnVXBkYXRlIFZlaGljbGUnIDogJ0FkZCBWZWhpY2xlJylcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJhcGlTZXJ2aWNlIiwiQWRtaW5QYWdlIiwiY2l0aWVzIiwiaXNBdXRoZW50aWNhdGVkIiwic2V0SXNBdXRoZW50aWNhdGVkIiwibG9naW5FbWFpbCIsInNldExvZ2luRW1haWwiLCJsb2dpblBhc3N3b3JkIiwic2V0TG9naW5QYXNzd29yZCIsImxvZ2luRXJyb3IiLCJzZXRMb2dpbkVycm9yIiwic2VsZWN0ZWRDaXR5Iiwic2V0U2VsZWN0ZWRDaXR5Iiwic2V0Q2l0aWVzIiwiYm9va2luZ3MiLCJzZXRCb29raW5ncyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInZlaGljbGVzIiwic2V0VmVoaWNsZXMiLCJmbGVldEZvcm0iLCJzZXRGbGVldEZvcm0iLCJuYW1lIiwiY2F0ZWdvcnkiLCJjYXBhY2l0eSIsImRlc2NyaXB0aW9uIiwiZmVhdHVyZXMiLCJpbWFnZSIsInByaWNlUmFuZ2UiLCJpc0FjdGl2ZSIsInN1Ym1pdHRpbmdGbGVldCIsInNldFN1Ym1pdHRpbmdGbGVldCIsImVkaXRpbmdWZWhpY2xlIiwic2V0RWRpdGluZ1ZlaGljbGUiLCJzaG93RmxlZXRGb3JtIiwic2V0U2hvd0ZsZWV0Rm9ybSIsInVwZGF0ZUJvb2tpbmdTdGF0dXMiLCJib29raW5nSWQiLCJuZXdTdGF0dXMiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwic3RhdHVzIiwib2siLCJwcmV2IiwibWFwIiwiYm9va2luZyIsImlkIiwiYWxlcnQiLCJjb25zb2xlIiwiZmV0Y2hWZWhpY2xlcyIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsImhhbmRsZUZsZWV0U3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0Iiwic2VsZWN0ZWRDaXR5RGF0YSIsImZpbmQiLCJjIiwic2x1ZyIsImZsZWV0RGF0YSIsInNwbGl0IiwiZiIsInRyaW0iLCJmaWx0ZXIiLCJjaXR5SWQiLCJ1cmwiLCJyZXNldEZsZWV0Rm9ybSIsImNpdGllc0RhdGEiLCJnZXRDaXRpZXMiLCJlcnJvckRhdGEiLCJtZXNzYWdlIiwiaGFuZGxlRWRpdFZlaGljbGUiLCJ2ZWhpY2xlIiwiam9pbiIsImhhbmRsZURlbGV0ZVZlaGljbGUiLCJ2ZWhpY2xlSWQiLCJjb25maXJtIiwiYXV0aFN0YXR1cyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJoYW5kbGVMb2dpbiIsInNldEl0ZW0iLCJoYW5kbGVMb2dvdXQiLCJyZW1vdmVJdGVtIiwiZmV0Y2hDaXRpZXMiLCJmZXRjaEJvb2tpbmdzIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJnZXRTdGF0dXNDb2xvciIsImZvcm1hdERhdGUiLCJkYXRlU3RyaW5nIiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImRheSIsIm1vbnRoIiwieWVhciIsImhvdXIiLCJtaW51dGUiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwiaDEiLCJwIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsImJ1dHRvbiIsImhlYWRlciIsIm9uQ2xpY2siLCJhc2lkZSIsImgyIiwibmF2IiwibGVuZ3RoIiwiY2l0eSIsIl9jb3VudCIsIm1haW4iLCJoMyIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJ0ZCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiZW1haWwiLCJwaG9uZSIsInBpY2t1cExvY2F0aW9uIiwiZHJvcG9mZkxvY2F0aW9uIiwicGFzc2VuZ2VycyIsInBpY2t1cERhdGUiLCJwaWNrdXBUaW1lIiwic2VsZWN0Iiwib3B0aW9uIiwiY3JlYXRlZEF0IiwiaW1nIiwic3JjIiwiYWx0IiwicmVwbGFjZSIsImNvbFNwYW4iLCJ0ZXh0YXJlYSIsInJvd3MiLCJjaGVja2VkIiwiaHRtbEZvciIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});