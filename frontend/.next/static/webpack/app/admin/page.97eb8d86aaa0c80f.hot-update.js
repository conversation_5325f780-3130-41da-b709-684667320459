"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    var _cities_find;\n    _s();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginEmail, setLoginEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginPassword, setLoginPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCity, setSelectedCity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('london');\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bookings, setBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bookings');\n    // Fleet management state\n    const [fleetForm, setFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        category: 'PRIVATE_CAR',\n        capacity: '',\n        description: '',\n        features: '',\n        image: '',\n        priceRange: '',\n        isActive: true\n    });\n    const [submittingFleet, setSubmittingFleet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Status update functionality\n    const updateBookingStatus = async (bookingId, newStatus)=>{\n        try {\n            const response = await fetch(\"http://localhost:5000/api/booking/\".concat(bookingId, \"/status\"), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                // Update the booking in the local state\n                setBookings((prev)=>prev.map((booking)=>booking.id === bookingId ? {\n                            ...booking,\n                            status: newStatus\n                        } : booking));\n            } else {\n                alert('Failed to update booking status');\n            }\n        } catch (error) {\n            console.error('Error updating status:', error);\n            alert('Error updating booking status');\n        }\n    };\n    // Fleet management functions\n    const handleFleetSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmittingFleet(true);\n        try {\n            const selectedCityData = cities.find((c)=>c.slug === selectedCity);\n            if (!selectedCityData) {\n                alert('Please select a city first');\n                return;\n            }\n            const fleetData = {\n                ...fleetForm,\n                features: fleetForm.features.split(',').map((f)=>f.trim()).filter((f)=>f),\n                cityId: selectedCityData.id\n            };\n            const response = await fetch('http://localhost:5000/api/vehicles', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(fleetData)\n            });\n            if (response.ok) {\n                alert('Vehicle added successfully!');\n                setFleetForm({\n                    name: '',\n                    category: 'PRIVATE_CAR',\n                    capacity: '',\n                    description: '',\n                    features: '',\n                    image: '',\n                    priceRange: '',\n                    isActive: true\n                });\n                // Refresh cities to update vehicle count\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to add vehicle: \".concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error adding vehicle:', error);\n            alert('Error adding vehicle. Please try again.');\n        } finally{\n            setSubmittingFleet(false);\n        }\n    };\n    // Check if user is already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const authStatus = localStorage.getItem('adminAuthenticated');\n            if (authStatus === 'true') {\n                setIsAuthenticated(true);\n            }\n        }\n    }[\"AdminPage.useEffect\"], []);\n    const handleLogin = (e)=>{\n        e.preventDefault();\n        setLoginError('');\n        if (loginEmail === '<EMAIL>' && loginPassword === 'admin') {\n            setIsAuthenticated(true);\n            localStorage.setItem('adminAuthenticated', 'true');\n            setLoginEmail('');\n            setLoginPassword('');\n        } else {\n            setLoginError('Invalid email or password');\n        }\n    };\n    const handleLogout = ()=>{\n        setIsAuthenticated(false);\n        localStorage.removeItem('adminAuthenticated');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const fetchCities = {\n                \"AdminPage.useEffect.fetchCities\": async ()=>{\n                    try {\n                        const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                        setCities(citiesData);\n                    } catch (error) {\n                        console.error('Error fetching cities:', error);\n                        setError('Failed to load cities');\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchCities\"];\n            fetchCities();\n        }\n    }[\"AdminPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const fetchBookings = {\n                \"AdminPage.useEffect.fetchBookings\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"\".concat(\"http://localhost:5000/api\", \"/booking?city=\").concat(selectedCity));\n                        const data = await response.json();\n                        if (data.success) {\n                            setBookings(data.data);\n                        } else {\n                            setError('Failed to load bookings');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching bookings:', error);\n                        setError('Failed to load bookings');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchBookings\"];\n            if (selectedCity) {\n                fetchBookings();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'PENDING':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'CONFIRMED':\n                return 'bg-blue-100 text-blue-800';\n            case 'IN_PROGRESS':\n                return 'bg-purple-100 text-purple-800';\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'CANCELLED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-GB', {\n            day: '2-digit',\n            month: 'short',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // Show login form if not authenticated\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-elegant-xl p-8 w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-accent-gradient rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-2xl\",\n                                    children: \"G\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 mb-2\",\n                                children: \"Admin Portal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-600\",\n                                children: \"GoGeo Travels London\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleLogin,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Email Address\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: loginEmail,\n                                        onChange: (e)=>setLoginEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: loginPassword,\n                                        onChange: (e)=>setLoginPassword(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"Enter your password\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\",\n                                children: loginError\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"w-full bg-accent-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-accent-600 transition-colors duration-200\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-neutral-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-lg border-b border-neutral-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-accent-gradient rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-serif font-bold text-primary-900\",\n                                                children: \"GoGeo Travels\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-primary-600\",\n                                                children: \"Admin Portal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-primary-700\",\n                                        children: \"Welcome, Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"text-sm text-accent-600 hover:text-accent-700 font-medium\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 290,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"w-64 bg-white shadow-lg h-screen sticky top-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-neutral-100 rounded-lg p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('bookings'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'bookings' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('fleet'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'fleet' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Fleet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-primary-900 mb-6\",\n                                    children: \"Cities\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: cities.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedCity(city.slug),\n                                            className: \"w-full text-left px-4 py-3 rounded-xl transition-colors \".concat(selectedCity === city.slug ? 'bg-accent-500 text-white shadow-md' : 'text-primary-700 hover:bg-primary-50 hover:text-primary-900'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: city.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm opacity-75\",\n                                                        children: [\n                                                            city._count.vehicles,\n                                                            \" vehicles\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, city.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                            children: [\n                                                \"Booking Requests - \",\n                                                (_cities_find = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find === void 0 ? void 0 : _cities_find.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-primary-600\",\n                                            children: \"Manage and track all booking requests for this city\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-primary-600 mt-4\",\n                                            children: \"Loading bookings...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-500 text-xl mb-4\",\n                                            children: \"⚠️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this) : bookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDCCB\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-primary-900 mb-2\",\n                                            children: \"No bookings found\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-primary-600\",\n                                            children: \"No booking requests for this city yet.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-3xl elegant-shadow-lg overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"min-w-full divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                children: \"Customer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                children: \"Journey\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                children: \"Vehicle\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                children: \"Date & Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                children: \"Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    className: \"bg-white divide-y divide-gray-200\",\n                                                    children: bookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50 smooth-transition\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-semibold text-primary-900\",\n                                                                                children: [\n                                                                                    booking.firstName,\n                                                                                    \" \",\n                                                                                    booking.lastName\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-primary-600\",\n                                                                                children: booking.email\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-primary-600\",\n                                                                                children: booking.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-primary-900\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    \"From: \",\n                                                                                    booking.pickupLocation\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 437,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    \"To: \",\n                                                                                    booking.dropoffLocation\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-primary-600 mt-1\",\n                                                                                children: [\n                                                                                    booking.passengers,\n                                                                                    \" passenger\",\n                                                                                    booking.passengers > 1 ? 's' : ''\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-primary-900\",\n                                                                        children: booking.vehicle ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: booking.vehicle.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 448,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600\",\n                                                                                    children: booking.vehicle.category\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-primary-500\",\n                                                                            children: \"Not specified\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-primary-900\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: new Date(booking.pickupDate).toLocaleDateString('en-GB')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 458,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-primary-600\",\n                                                                                children: booking.pickupTime\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: booking.status,\n                                                                        onChange: (e)=>updateBookingStatus(booking.id, e.target.value),\n                                                                        className: \"px-3 py-1 rounded-full text-xs font-semibold border-0 focus:ring-2 focus:ring-accent-500 \".concat(getStatusColor(booking.status)),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"PENDING\",\n                                                                                children: \"PENDING\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"CONFIRMED\",\n                                                                                children: \"CONFIRMED\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"IN_PROGRESS\",\n                                                                                children: \"IN_PROGRESS\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"COMPLETED\",\n                                                                                children: \"COMPLETED\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 471,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"CANCELLED\",\n                                                                                children: \"CANCELLED\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 472,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-600\",\n                                                                    children: formatDate(booking.createdAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, booking.id, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"2ysF0XZQ6whH7nexQNfFOw8j3LU=\");\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});