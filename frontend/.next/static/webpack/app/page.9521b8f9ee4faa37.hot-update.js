"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Navigation */ \"(app-pages-browser)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_HeroSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/HeroSection */ \"(app-pages-browser)/./src/components/HeroSection.tsx\");\n/* harmony import */ var _components_FleetSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FleetSection */ \"(app-pages-browser)/./src/components/FleetSection.tsx\");\n/* harmony import */ var _components_AboutSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AboutSection */ \"(app-pages-browser)/./src/components/AboutSection.tsx\");\n/* harmony import */ var _components_BookingForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BookingForm */ \"(app-pages-browser)/./src/components/BookingForm.tsx\");\n/* harmony import */ var _components_ContactSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ContactSection */ \"(app-pages-browser)/./src/components/ContactSection.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Footer */ \"(app-pages-browser)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_CitySelectionModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/CitySelectionModal */ \"(app-pages-browser)/./src/components/CitySelectionModal.tsx\");\n/* harmony import */ var _contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/CityContext */ \"(app-pages-browser)/./src/contexts/CityContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HomeContent() {\n    _s();\n    const { selectedCity, showCityModal, setSelectedCity } = (0,_contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__.useCityContext)();\n    console.log('HomeContent render - selectedCity:', selectedCity, 'showCityModal:', showCityModal);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HeroSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FleetSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AboutSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BookingForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-4 right-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowCityModal(true),\n                    className: \"bg-red-500 text-white px-4 py-2 rounded\",\n                    children: \"Test City Modal\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CitySelectionModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showCityModal,\n                onCitySelect: setSelectedCity\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_s(HomeContent, \"mlvlkV739fvk/j1j4gCqQLUvibM=\", false, function() {\n    return [\n        _contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__.useCityContext\n    ];\n});\n_c = HomeContent;\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_CityContext__WEBPACK_IMPORTED_MODULE_9__.CityProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HomeContent, {}, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"HomeContent\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});