/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/admin/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FGeo-Travels%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FGeo-Travels%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/page.tsx */ \"(app-pages-browser)/./src/app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ0YWhhZmFyb29xdWklMkZEb2N1bWVudHMlMkZhdWdtZW50LXByb2plY3RzJTJGR2VvLVRyYXZlbHMlMkZmcm9udGVuZCUyRnNyYyUyRmFwcCUyRmFkbWluJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBK0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvR2VvLVRyYXZlbHMvZnJvbnRlbmQvc3JjL2FwcC9hZG1pbi9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FGeo-Travels%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy90YWhhZmFyb29xdWkvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvR2VvLVRyYXZlbHMvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    var _cities_find, _cities_find1;\n    _s();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginEmail, setLoginEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginPassword, setLoginPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCity, setSelectedCity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('london');\n    const [cities, setCities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bookings, setBookings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bookings');\n    // Fleet management state\n    const [vehicles, setVehicles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [fleetForm, setFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        category: 'PRIVATE_CAR',\n        capacity: '',\n        description: '',\n        features: '',\n        image: '',\n        priceRange: '',\n        isActive: true\n    });\n    const [submittingFleet, setSubmittingFleet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingVehicle, setEditingVehicle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFleetForm, setShowFleetForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Status update functionality\n    const updateBookingStatus = async (bookingId, newStatus)=>{\n        try {\n            const response = await fetch(\"http://localhost:5000/api/booking/\".concat(bookingId, \"/status\"), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                // Update the booking in the local state\n                setBookings((prev)=>prev.map((booking)=>booking.id === bookingId ? {\n                            ...booking,\n                            status: newStatus\n                        } : booking));\n            } else {\n                alert('Failed to update booking status');\n            }\n        } catch (error) {\n            console.error('Error updating status:', error);\n            alert('Error updating booking status');\n        }\n    };\n    // Fetch vehicles for selected city\n    const fetchVehicles = async ()=>{\n        if (!isAuthenticated || !selectedCity) return;\n        try {\n            const response = await fetch(\"http://localhost:5000/api/vehicles?city=\".concat(selectedCity));\n            if (response.ok) {\n                const data = await response.json();\n                console.log('Vehicles data:', data); // Debug log\n                setVehicles(data.data || []);\n            } else {\n                console.error('Failed to fetch vehicles:', response.status);\n            }\n        } catch (error) {\n            console.error('Error fetching vehicles:', error);\n        }\n    };\n    // Fleet management functions\n    const handleFleetSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmittingFleet(true);\n        try {\n            const selectedCityData = cities.find((c)=>c.slug === selectedCity);\n            if (!selectedCityData) {\n                alert('Please select a city first');\n                return;\n            }\n            const fleetData = {\n                ...fleetForm,\n                features: fleetForm.features.split(',').map((f)=>f.trim()).filter((f)=>f),\n                cityId: selectedCityData.id\n            };\n            const url = editingVehicle ? \"http://localhost:5000/api/vehicles/\".concat(editingVehicle) : 'http://localhost:5000/api/vehicles';\n            const method = editingVehicle ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(fleetData)\n            });\n            if (response.ok) {\n                alert(\"Vehicle \".concat(editingVehicle ? 'updated' : 'added', \" successfully!\"));\n                resetFleetForm();\n                // Refresh data\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n                fetchVehicles();\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to \".concat(editingVehicle ? 'update' : 'add', \" vehicle: \").concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error adding vehicle:', error);\n            alert('Error adding vehicle. Please try again.');\n        } finally{\n            setSubmittingFleet(false);\n        }\n    };\n    const resetFleetForm = ()=>{\n        setFleetForm({\n            name: '',\n            category: 'PRIVATE_CAR',\n            capacity: '',\n            description: '',\n            features: '',\n            image: '',\n            priceRange: '',\n            isActive: true\n        });\n        setEditingVehicle(null);\n        setShowFleetForm(false);\n    };\n    const handleEditVehicle = (vehicle)=>{\n        setFleetForm({\n            name: vehicle.name,\n            category: vehicle.category,\n            capacity: vehicle.capacity,\n            description: vehicle.description,\n            features: vehicle.features.join(', '),\n            image: vehicle.image || '',\n            priceRange: vehicle.priceRange,\n            isActive: vehicle.isActive\n        });\n        setEditingVehicle(vehicle.id);\n        setShowFleetForm(true);\n    };\n    const handleDeleteVehicle = async (vehicleId)=>{\n        if (!confirm('Are you sure you want to delete this vehicle?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"http://localhost:5000/api/vehicles/\".concat(vehicleId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                alert('Vehicle deleted successfully!');\n                // Refresh data\n                const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                setCities(citiesData);\n                fetchVehicles();\n            } else {\n                const errorData = await response.json();\n                alert(\"Failed to delete vehicle: \".concat(errorData.message || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error deleting vehicle:', error);\n            alert('Error deleting vehicle. Please try again.');\n        }\n    };\n    // Check if user is already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const authStatus = localStorage.getItem('adminAuthenticated');\n            if (authStatus === 'true') {\n                setIsAuthenticated(true);\n            }\n        }\n    }[\"AdminPage.useEffect\"], []);\n    const handleLogin = (e)=>{\n        e.preventDefault();\n        setLoginError('');\n        if (loginEmail === '<EMAIL>' && loginPassword === 'admin') {\n            setIsAuthenticated(true);\n            localStorage.setItem('adminAuthenticated', 'true');\n            setLoginEmail('');\n            setLoginPassword('');\n        } else {\n            setLoginError('Invalid email or password');\n        }\n    };\n    const handleLogout = ()=>{\n        setIsAuthenticated(false);\n        localStorage.removeItem('adminAuthenticated');\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const fetchCities = {\n                \"AdminPage.useEffect.fetchCities\": async ()=>{\n                    try {\n                        const citiesData = await _services_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getCities();\n                        setCities(citiesData);\n                    } catch (error) {\n                        console.error('Error fetching cities:', error);\n                        setError('Failed to load cities');\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchCities\"];\n            fetchCities();\n        }\n    }[\"AdminPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            const fetchBookings = {\n                \"AdminPage.useEffect.fetchBookings\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        const response = await fetch(\"\".concat(\"http://localhost:5000/api\", \"/booking?city=\").concat(selectedCity));\n                        const data = await response.json();\n                        if (data.success) {\n                            setBookings(data.data);\n                        } else {\n                            setError('Failed to load bookings');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching bookings:', error);\n                        setError('Failed to load bookings');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdminPage.useEffect.fetchBookings\"];\n            if (selectedCity) {\n                fetchBookings();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity\n    ]);\n    // Separate useEffect for vehicles\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPage.useEffect\": ()=>{\n            if (isAuthenticated && selectedCity) {\n                fetchVehicles();\n            }\n        }\n    }[\"AdminPage.useEffect\"], [\n        selectedCity,\n        isAuthenticated\n    ]);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'PENDING':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'CONFIRMED':\n                return 'bg-blue-100 text-blue-800';\n            case 'IN_PROGRESS':\n                return 'bg-purple-100 text-purple-800';\n            case 'COMPLETED':\n                return 'bg-green-100 text-green-800';\n            case 'CANCELLED':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-GB', {\n            day: '2-digit',\n            month: 'short',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    // Show login form if not authenticated\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-primary-50 to-accent-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-elegant-xl p-8 w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-accent-gradient rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-2xl\",\n                                    children: \"G\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-serif font-bold text-primary-900 mb-2\",\n                                children: \"Admin Portal\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-600\",\n                                children: \"GoGeo Travels London\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleLogin,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Email Address\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: loginEmail,\n                                        onChange: (e)=>setLoginEmail(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: loginPassword,\n                                        onChange: (e)=>setLoginPassword(e.target.value),\n                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                        placeholder: \"Enter your password\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm\",\n                                children: loginError\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"w-full bg-accent-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-accent-600 transition-colors duration-200\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n            lineNumber: 312,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-neutral-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-lg border-b border-neutral-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-accent-gradient rounded-xl flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"G\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-serif font-bold text-primary-900\",\n                                                children: \"GoGeo Travels\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-primary-600\",\n                                                children: \"Admin Portal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-primary-700\",\n                                        children: \"Welcome, Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"text-sm text-accent-600 hover:text-accent-700 font-medium\",\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"w-64 bg-white shadow-lg h-screen sticky top-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-neutral-100 rounded-lg p-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('bookings'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'bookings' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Bookings\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('fleet'),\n                                                className: \"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors \".concat(activeTab === 'fleet' ? 'bg-white text-primary-900 shadow-sm' : 'text-primary-600 hover:text-primary-900'),\n                                                children: \"Fleet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-primary-900 mb-6\",\n                                    children: \"Cities\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: cities.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-primary-600 text-sm\",\n                                        children: \"Loading cities...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this) : cities.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedCity(city.slug),\n                                            className: \"w-full text-left px-4 py-3 rounded-xl transition-colors \".concat(selectedCity === city.slug ? 'bg-accent-500 text-white shadow-md' : 'text-primary-700 hover:bg-primary-50 hover:text-primary-900'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: city.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm opacity-75\",\n                                                        children: [\n                                                            city._count.vehicles,\n                                                            \" vehicles\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, city.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto\",\n                            children: activeTab === 'bookings' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                                children: [\n                                                    \"Booking Requests - \",\n                                                    (_cities_find = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find === void 0 ? void 0 : _cities_find.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600\",\n                                                children: \"Manage and track all booking requests for this city\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, this),\n                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-500 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600 mt-4\",\n                                                children: \"Loading bookings...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-red-500 text-xl mb-4\",\n                                                children: \"⚠️\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-600\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this) : bookings.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83D\\uDCCB\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-primary-900 mb-2\",\n                                                children: \"No bookings found\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-primary-600\",\n                                                children: \"No booking requests for this city yet.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-3xl elegant-shadow-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"min-w-full divide-y divide-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        className: \"bg-gray-50\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Customer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Journey\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Vehicle\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Date & Time\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"px-6 py-4 text-left text-xs font-semibold text-primary-900 uppercase tracking-wider\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        className: \"bg-white divide-y divide-gray-200\",\n                                                        children: bookings.map((booking)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"hover:bg-gray-50 smooth-transition\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold text-primary-900\",\n                                                                                    children: [\n                                                                                        booking.firstName,\n                                                                                        \" \",\n                                                                                        booking.lastName\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-primary-600\",\n                                                                                    children: booking.email\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 519,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-primary-600\",\n                                                                                    children: booking.phone\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: [\n                                                                                        \"From: \",\n                                                                                        booking.pickupLocation\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 525,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"To: \",\n                                                                                        booking.dropoffLocation\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 526,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600 mt-1\",\n                                                                                    children: [\n                                                                                        booking.passengers,\n                                                                                        \" passenger\",\n                                                                                        booking.passengers > 1 ? 's' : ''\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: booking.vehicle ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: booking.vehicle.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 536,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-primary-600\",\n                                                                                        children: booking.vehicle.category\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 537,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-primary-500\",\n                                                                                children: \"Not specified\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-primary-900\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: new Date(booking.pickupDate).toLocaleDateString('en-GB')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 546,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-primary-600\",\n                                                                                    children: booking.pickupTime\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: booking.status,\n                                                                            onChange: (e)=>updateBookingStatus(booking.id, e.target.value),\n                                                                            className: \"px-3 py-1 rounded-full text-xs font-semibold border-0 focus:ring-2 focus:ring-accent-500 \".concat(getStatusColor(booking.status)),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"PENDING\",\n                                                                                    children: \"PENDING\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 556,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"CONFIRMED\",\n                                                                                    children: \"CONFIRMED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"IN_PROGRESS\",\n                                                                                    children: \"IN_PROGRESS\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 558,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"COMPLETED\",\n                                                                                    children: \"COMPLETED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 559,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"CANCELLED\",\n                                                                                    children: \"CANCELLED\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-600\",\n                                                                        children: formatDate(booking.createdAt)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, booking.id, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-serif font-bold text-primary-900 mb-2\",\n                                                            children: [\n                                                                \"Fleet Management - \",\n                                                                (_cities_find1 = cities.find((c)=>c.slug === selectedCity)) === null || _cities_find1 === void 0 ? void 0 : _cities_find1.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-primary-600\",\n                                                            children: \"Manage premium vehicles for this city\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFleetForm(!showFleetForm),\n                                                    className: \"bg-accent-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-accent-600 transition-colors\",\n                                                    children: showFleetForm ? 'Cancel' : 'Add New Vehicle'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-lg mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-neutral-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-primary-900\",\n                                                    children: \"Current Fleet\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"min-w-full divide-y divide-neutral-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-neutral-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Vehicle\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Category\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Capacity\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Price Range\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-6 py-3 text-left text-xs font-medium text-primary-700 uppercase tracking-wider\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"bg-white divide-y divide-neutral-200\",\n                                                            children: [\n                                                                vehicles.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                        className: \"hover:bg-neutral-50\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center\",\n                                                                                    children: [\n                                                                                        vehicle.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                            className: \"h-10 w-10 rounded-lg object-cover mr-3\",\n                                                                                            src: vehicle.image,\n                                                                                            alt: vehicle.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                            lineNumber: 630,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-medium text-primary-900\",\n                                                                                                    children: vehicle.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                                    lineNumber: 633,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm text-primary-600 truncate max-w-xs\",\n                                                                                                    children: vehicle.description\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                                    lineNumber: 634,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                            lineNumber: 632,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800\",\n                                                                                    children: vehicle.category.replace('_', ' ')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 639,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 638,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-900\",\n                                                                                children: vehicle.capacity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-primary-900\",\n                                                                                children: vehicle.priceRange\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 646,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(vehicle.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                                    children: vehicle.isActive ? 'Active' : 'Inactive'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 649,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleEditVehicle(vehicle),\n                                                                                        className: \"text-accent-600 hover:text-accent-900 mr-3\",\n                                                                                        children: \"Edit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 659,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteVehicle(vehicle.id),\n                                                                                        className: \"text-red-600 hover:text-red-900\",\n                                                                                        children: \"Delete\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 665,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, vehicle.id, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 27\n                                                                    }, this)),\n                                                                vehicles.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        colSpan: 6,\n                                                                        className: \"px-6 py-8 text-center text-primary-600\",\n                                                                        children: \"No vehicles found for this city. Add your first vehicle to get started.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                    lineNumber: 675,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 17\n                                    }, this),\n                                    showFleetForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl shadow-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-primary-900\",\n                                                        children: editingVehicle ? 'Edit Vehicle' : 'Add New Vehicle'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-primary-600 mt-1\",\n                                                        children: editingVehicle ? 'Update vehicle information' : 'Add a new vehicle to the fleet'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                onSubmit: handleFleetSubmit,\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Vehicle Name *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.name,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    name: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., Mercedes S-Class\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Vehicle Category *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 714,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: fleetForm.category,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    category: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        required: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"PRIVATE_CAR\",\n                                                                                children: \"Private Car\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 723,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"BUS\",\n                                                                                children: \"Private Bus\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                lineNumber: 724,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            selectedCity === 'london' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"HELICOPTER\",\n                                                                                        children: \"Helicopter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 727,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"PRIVATE_JET\",\n                                                                                        children: \"Private Jet\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                                        lineNumber: 728,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 717,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Capacity *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.capacity,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    capacity: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., 4 passengers\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                        children: \"Price Range *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: fleetForm.priceRange,\n                                                                        onChange: (e)=>setFleetForm((prev)=>({\n                                                                                    ...prev,\n                                                                                    priceRange: e.target.value\n                                                                                })),\n                                                                        className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                        placeholder: \"e.g., \\xa3200-300/hour\",\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Description *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: fleetForm.description,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            description: e.target.value\n                                                                        })),\n                                                                rows: 4,\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"Describe the vehicle and its luxury features...\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Features (comma-separated)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: fleetForm.features,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            features: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"e.g., Leather seats, WiFi, Champagne service, Professional chauffeur\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-primary-800 mb-2\",\n                                                                children: \"Image URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"url\",\n                                                                value: fleetForm.image,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            image: e.target.value\n                                                                        })),\n                                                                className: \"w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-accent-500 text-primary-900\",\n                                                                placeholder: \"https://example.com/vehicle-image.jpg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                id: \"isActive\",\n                                                                checked: fleetForm.isActive,\n                                                                onChange: (e)=>setFleetForm((prev)=>({\n                                                                            ...prev,\n                                                                            isActive: e.target.checked\n                                                                        })),\n                                                                className: \"h-4 w-4 text-accent-600 focus:ring-accent-500 border-neutral-300 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"isActive\",\n                                                                className: \"ml-2 block text-sm text-primary-800\",\n                                                                children: \"Vehicle is active and available for booking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: resetFleetForm,\n                                                                className: \"bg-neutral-200 text-neutral-700 px-6 py-3 rounded-lg font-semibold hover:bg-neutral-300 transition-colors\",\n                                                                children: \"Cancel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"submit\",\n                                                                disabled: submittingFleet,\n                                                                className: \"bg-accent-500 text-white px-8 py-3 rounded-lg font-semibold transition-colors \".concat(submittingFleet ? 'opacity-50 cursor-not-allowed' : 'hover:bg-accent-600'),\n                                                                children: submittingFleet ? editingVehicle ? 'Updating...' : 'Adding...' : editingVehicle ? 'Update Vehicle' : 'Add Vehicle'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Geo-Travels/frontend/src/app/admin/page.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"OJE2clIpiDyqZBOo1Ow9VbcafA4=\");\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWRtaW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0QztBQUNNO0FBNEJuQyxTQUFTRztRQThhZ0JDLGNBc0hJQTs7SUFuaUIxQyxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ08sWUFBWUMsY0FBYyxHQUFHUiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNTLGVBQWVDLGlCQUFpQixHQUFHViwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNXLFlBQVlDLGNBQWMsR0FBR1osK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYSxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDSSxRQUFRVyxVQUFVLEdBQUdmLCtDQUFRQSxDQUFTLEVBQUU7SUFDL0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQW1CLEVBQUU7SUFDN0QsTUFBTSxDQUFDa0IsU0FBU0MsV0FBVyxHQUFHbkIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDb0IsT0FBT0MsU0FBUyxHQUFHckIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ3NCLFdBQVdDLGFBQWEsR0FBR3ZCLCtDQUFRQSxDQUF1QjtJQUVqRSx5QkFBeUI7SUFDekIsTUFBTSxDQUFDd0IsVUFBVUMsWUFBWSxHQUFHekIsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUczQiwrQ0FBUUEsQ0FBQztRQUN6QzRCLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0EsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHckMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDc0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUN3QyxlQUFlQyxpQkFBaUIsR0FBR3pDLCtDQUFRQSxDQUFDO0lBRW5ELDhCQUE4QjtJQUM5QixNQUFNMEMsc0JBQXNCLE9BQU9DLFdBQW1CQztRQUNwRCxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLHFDQUErQyxPQUFWSCxXQUFVLFlBQVU7Z0JBQ3BGSSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRUMsUUFBUVI7Z0JBQVU7WUFDM0M7WUFFQSxJQUFJQyxTQUFTUSxFQUFFLEVBQUU7Z0JBQ2Ysd0NBQXdDO2dCQUN4Q3BDLFlBQVlxQyxDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLFVBQzNCQSxRQUFRQyxFQUFFLEtBQUtkLFlBQ1g7NEJBQUUsR0FBR2EsT0FBTzs0QkFBRUosUUFBUVI7d0JBQWlCLElBQ3ZDWTtZQUVSLE9BQU87Z0JBQ0xFLE1BQU07WUFDUjtRQUNGLEVBQUUsT0FBT3RDLE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q3NDLE1BQU07UUFDUjtJQUNGO0lBRUEsbUNBQW1DO0lBQ25DLE1BQU1FLGdCQUFnQjtRQUNwQixJQUFJLENBQUN2RCxtQkFBbUIsQ0FBQ1EsY0FBYztRQUV2QyxJQUFJO1lBQ0YsTUFBTWdDLFdBQVcsTUFBTUMsTUFBTSwyQ0FBd0QsT0FBYmpDO1lBQ3hFLElBQUlnQyxTQUFTUSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTVEsT0FBTyxNQUFNaEIsU0FBU2lCLElBQUk7Z0JBQ2hDSCxRQUFRSSxHQUFHLENBQUMsa0JBQWtCRixPQUFPLFlBQVk7Z0JBQ2pEcEMsWUFBWW9DLEtBQUtBLElBQUksSUFBSSxFQUFFO1lBQzdCLE9BQU87Z0JBQ0xGLFFBQVF2QyxLQUFLLENBQUMsNkJBQTZCeUIsU0FBU08sTUFBTTtZQUM1RDtRQUNGLEVBQUUsT0FBT2hDLE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsNEJBQTRCQTtRQUM1QztJQUNGO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU00QyxvQkFBb0IsT0FBT0M7UUFDL0JBLEVBQUVDLGNBQWM7UUFDaEI3QixtQkFBbUI7UUFFbkIsSUFBSTtZQUNGLE1BQU04QixtQkFBbUIvRCxPQUFPZ0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUt6RDtZQUNyRCxJQUFJLENBQUNzRCxrQkFBa0I7Z0JBQ3JCVCxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxNQUFNYSxZQUFZO2dCQUNoQixHQUFHN0MsU0FBUztnQkFDWk0sVUFBVU4sVUFBVU0sUUFBUSxDQUFDd0MsS0FBSyxDQUFDLEtBQUtqQixHQUFHLENBQUNrQixDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLElBQUlDLE1BQU0sQ0FBQ0YsQ0FBQUEsSUFBS0E7Z0JBQ3ZFRyxRQUFRVCxpQkFBaUJWLEVBQUU7WUFDN0I7WUFFQSxNQUFNb0IsTUFBTXZDLGlCQUNSLHNDQUFxRCxPQUFmQSxrQkFDdEM7WUFFSixNQUFNUyxTQUFTVCxpQkFBaUIsUUFBUTtZQUV4QyxNQUFNTyxXQUFXLE1BQU1DLE1BQU0rQixLQUFLO2dCQUNoQzlCO2dCQUNBQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ29CO1lBQ3ZCO1lBRUEsSUFBSTFCLFNBQVNRLEVBQUUsRUFBRTtnQkFDZkssTUFBTSxXQUFnRCxPQUFyQ3BCLGlCQUFpQixZQUFZLFNBQVE7Z0JBQ3REd0M7Z0JBQ0EsZUFBZTtnQkFDZixNQUFNQyxhQUFhLE1BQU03RSxxREFBVUEsQ0FBQzhFLFNBQVM7Z0JBQzdDakUsVUFBVWdFO2dCQUNWbkI7WUFDRixPQUFPO2dCQUNMLE1BQU1xQixZQUFZLE1BQU1wQyxTQUFTaUIsSUFBSTtnQkFDckNKLE1BQU0sYUFBMkR1QixPQUE5QzNDLGlCQUFpQixXQUFXLE9BQU0sY0FBaUQsT0FBckMyQyxVQUFVQyxPQUFPLElBQUk7WUFDeEY7UUFDRixFQUFFLE9BQU85RCxPQUFPO1lBQ2R1QyxRQUFRdkMsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNzQyxNQUFNO1FBQ1IsU0FBVTtZQUNSckIsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNeUMsaUJBQWlCO1FBQ3JCbkQsYUFBYTtZQUNYQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjtRQUNBSSxrQkFBa0I7UUFDbEJFLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0wQyxvQkFBb0IsQ0FBQ0M7UUFDekJ6RCxhQUFhO1lBQ1hDLE1BQU13RCxRQUFReEQsSUFBSTtZQUNsQkMsVUFBVXVELFFBQVF2RCxRQUFRO1lBQzFCQyxVQUFVc0QsUUFBUXRELFFBQVE7WUFDMUJDLGFBQWFxRCxRQUFRckQsV0FBVztZQUNoQ0MsVUFBVW9ELFFBQVFwRCxRQUFRLENBQUNxRCxJQUFJLENBQUM7WUFDaENwRCxPQUFPbUQsUUFBUW5ELEtBQUssSUFBSTtZQUN4QkMsWUFBWWtELFFBQVFsRCxVQUFVO1lBQzlCQyxVQUFVaUQsUUFBUWpELFFBQVE7UUFDNUI7UUFDQUksa0JBQWtCNkMsUUFBUTNCLEVBQUU7UUFDNUJoQixpQkFBaUI7SUFDbkI7SUFFQSxNQUFNNkMsc0JBQXNCLE9BQU9DO1FBQ2pDLElBQUksQ0FBQ0MsUUFBUSxrREFBa0Q7WUFDN0Q7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNM0MsV0FBVyxNQUFNQyxNQUFNLHNDQUFnRCxPQUFWeUMsWUFBYTtnQkFDOUV4QyxRQUFRO1lBQ1Y7WUFFQSxJQUFJRixTQUFTUSxFQUFFLEVBQUU7Z0JBQ2ZLLE1BQU07Z0JBQ04sZUFBZTtnQkFDZixNQUFNcUIsYUFBYSxNQUFNN0UscURBQVVBLENBQUM4RSxTQUFTO2dCQUM3Q2pFLFVBQVVnRTtnQkFDVm5CO1lBQ0YsT0FBTztnQkFDTCxNQUFNcUIsWUFBWSxNQUFNcEMsU0FBU2lCLElBQUk7Z0JBQ3JDSixNQUFNLDZCQUFrRSxPQUFyQ3VCLFVBQVVDLE9BQU8sSUFBSTtZQUMxRDtRQUNGLEVBQUUsT0FBTzlELE9BQU87WUFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q3NDLE1BQU07UUFDUjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDekQsZ0RBQVNBOytCQUFDO1lBQ1IsTUFBTXdGLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztZQUN4QyxJQUFJRixlQUFlLFFBQVE7Z0JBQ3pCbkYsbUJBQW1CO1lBQ3JCO1FBQ0Y7OEJBQUcsRUFBRTtJQUVMLE1BQU1zRixjQUFjLENBQUMzQjtRQUNuQkEsRUFBRUMsY0FBYztRQUNoQnRELGNBQWM7UUFFZCxJQUFJTCxlQUFlLDRCQUE0QkUsa0JBQWtCLFNBQVM7WUFDeEVILG1CQUFtQjtZQUNuQm9GLGFBQWFHLE9BQU8sQ0FBQyxzQkFBc0I7WUFDM0NyRixjQUFjO1lBQ2RFLGlCQUFpQjtRQUNuQixPQUFPO1lBQ0xFLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU1rRixlQUFlO1FBQ25CeEYsbUJBQW1CO1FBQ25Cb0YsYUFBYUssVUFBVSxDQUFDO0lBQzFCO0lBRUE5RixnREFBU0E7K0JBQUM7WUFDUixJQUFJLENBQUNJLGlCQUFpQjtZQUN0QixNQUFNMkY7bURBQWM7b0JBQ2xCLElBQUk7d0JBQ0YsTUFBTWpCLGFBQWEsTUFBTTdFLHFEQUFVQSxDQUFDOEUsU0FBUzt3QkFDN0NqRSxVQUFVZ0U7b0JBQ1osRUFBRSxPQUFPM0QsT0FBTzt3QkFDZHVDLFFBQVF2QyxLQUFLLENBQUMsMEJBQTBCQTt3QkFDeENDLFNBQVM7b0JBQ1g7Z0JBQ0Y7O1lBRUEyRTtRQUNGOzhCQUFHLEVBQUU7SUFFTC9GLGdEQUFTQTsrQkFBQztZQUNSLE1BQU1nRztxREFBZ0I7b0JBQ3BCLElBQUk7d0JBQ0Y5RSxXQUFXO3dCQUNYRSxTQUFTO3dCQUVULE1BQU13QixXQUFXLE1BQU1DLE1BQU0sR0FBbURqQyxPQUFoRHFGLDJCQUErQixFQUFDLGtCQUE2QixPQUFickY7d0JBQ2hGLE1BQU1nRCxPQUFPLE1BQU1oQixTQUFTaUIsSUFBSTt3QkFFaEMsSUFBSUQsS0FBS3dDLE9BQU8sRUFBRTs0QkFDaEJwRixZQUFZNEMsS0FBS0EsSUFBSTt3QkFDdkIsT0FBTzs0QkFDTHhDLFNBQVM7d0JBQ1g7b0JBQ0YsRUFBRSxPQUFPRCxPQUFPO3dCQUNkdUMsUUFBUXZDLEtBQUssQ0FBQyw0QkFBNEJBO3dCQUMxQ0MsU0FBUztvQkFDWCxTQUFVO3dCQUNSRixXQUFXO29CQUNiO2dCQUNGOztZQUVBLElBQUlOLGNBQWM7Z0JBQ2hCb0Y7WUFDRjtRQUNGOzhCQUFHO1FBQUNwRjtLQUFhO0lBRWpCLGtDQUFrQztJQUNsQ1osZ0RBQVNBOytCQUFDO1lBQ1IsSUFBSUksbUJBQW1CUSxjQUFjO2dCQUNuQytDO1lBQ0Y7UUFDRjs4QkFBRztRQUFDL0M7UUFBY1I7S0FBZ0I7SUFFbEMsTUFBTWlHLGlCQUFpQixDQUFDbEQ7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQWUsT0FBTztZQUMzQixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBYSxPQUFPO1lBQ3pCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1tRCxhQUFhLENBQUNDO1FBQ2xCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCLENBQUMsU0FBUztZQUN0REMsS0FBSztZQUNMQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxRQUFRO1FBQ1Y7SUFDRjtJQUVBLHVDQUF1QztJQUN2QyxJQUFJLENBQUMxRyxpQkFBaUI7UUFDcEIscUJBQ0UsOERBQUMyRztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNDO29DQUFLRCxXQUFVOzhDQUFnQzs7Ozs7Ozs7Ozs7MENBRWxELDhEQUFDRTtnQ0FBR0YsV0FBVTswQ0FBc0Q7Ozs7OzswQ0FDcEUsOERBQUNHO2dDQUFFSCxXQUFVOzBDQUFtQjs7Ozs7Ozs7Ozs7O2tDQUdsQyw4REFBQ0k7d0JBQUtDLFVBQVUxQjt3QkFBYXFCLFdBQVU7OzBDQUNyQyw4REFBQ0Q7O2tEQUNDLDhEQUFDTzt3Q0FBTU4sV0FBVTtrREFBa0Q7Ozs7OztrREFHbkUsOERBQUNPO3dDQUNDQyxNQUFLO3dDQUNMQyxPQUFPbkg7d0NBQ1BvSCxVQUFVLENBQUMxRCxJQUFNekQsY0FBY3lELEVBQUUyRCxNQUFNLENBQUNGLEtBQUs7d0NBQzdDVCxXQUFVO3dDQUNWWSxhQUFZO3dDQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7MENBSVosOERBQUNkOztrREFDQyw4REFBQ087d0NBQU1OLFdBQVU7a0RBQWtEOzs7Ozs7a0RBR25FLDhEQUFDTzt3Q0FDQ0MsTUFBSzt3Q0FDTEMsT0FBT2pIO3dDQUNQa0gsVUFBVSxDQUFDMUQsSUFBTXZELGlCQUFpQnVELEVBQUUyRCxNQUFNLENBQUNGLEtBQUs7d0NBQ2hEVCxXQUFVO3dDQUNWWSxhQUFZO3dDQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7NEJBSVhuSCw0QkFDQyw4REFBQ3FHO2dDQUFJQyxXQUFVOzBDQUNadEc7Ozs7OzswQ0FJTCw4REFBQ29IO2dDQUNDTixNQUFLO2dDQUNMUixXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU9YO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDZTtnQkFBT2YsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFLRCxXQUFVO3NEQUErQjs7Ozs7Ozs7Ozs7a0RBRWpELDhEQUFDRDs7MERBQ0MsOERBQUNHO2dEQUFHRixXQUFVOzBEQUFnRDs7Ozs7OzBEQUM5RCw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQTJCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBRzVDLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFLRCxXQUFVO2tEQUEyQjs7Ozs7O2tEQUMzQyw4REFBQ2M7d0NBQ0NFLFNBQVNuQzt3Q0FDVG1CLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVQsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ2lCO3dCQUFNakIsV0FBVTtrQ0FDZiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDYztnREFDQ0UsU0FBUyxJQUFNMUcsYUFBYTtnREFDNUIwRixXQUFXLHFFQUlWLE9BSEMzRixjQUFjLGFBQ1Ysd0NBQ0E7MERBRVA7Ozs7OzswREFHRCw4REFBQ3lHO2dEQUNDRSxTQUFTLElBQU0xRyxhQUFhO2dEQUM1QjBGLFdBQVcscUVBSVYsT0FIQzNGLGNBQWMsVUFDVix3Q0FDQTswREFFUDs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTUwsOERBQUM2RztvQ0FBR2xCLFdBQVU7OENBQThDOzs7Ozs7OENBQzVELDhEQUFDbUI7b0NBQUluQixXQUFVOzhDQUNaN0csT0FBT2lJLE1BQU0sS0FBSyxrQkFDakIsOERBQUNyQjt3Q0FBSUMsV0FBVTtrREFBMkI7Ozs7OytDQUUxQzdHLE9BQU9tRCxHQUFHLENBQUMsQ0FBQytFLHFCQUNaLDhEQUFDUDs0Q0FFQ0UsU0FBUyxJQUFNbkgsZ0JBQWdCd0gsS0FBS2hFLElBQUk7NENBQ3hDMkMsV0FBVywyREFJVixPQUhDcEcsaUJBQWlCeUgsS0FBS2hFLElBQUksR0FDdEIsdUNBQ0E7c0RBR04sNEVBQUMwQztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFLRCxXQUFVO2tFQUFlcUIsS0FBSzFHLElBQUk7Ozs7OztrRUFDeEMsOERBQUNzRjt3REFBS0QsV0FBVTs7NERBQ2JxQixLQUFLQyxNQUFNLENBQUMvRyxRQUFROzREQUFDOzs7Ozs7Ozs7Ozs7OzJDQVhyQjhHLEtBQUs3RSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBc0J0Qiw4REFBQytFO3dCQUFLdkIsV0FBVTtrQ0FDZCw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1ozRixjQUFjLDJCQUNiOztrREFDRSw4REFBQzBGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUdGLFdBQVU7O29EQUFzRDtxREFDOUM3RyxlQUFBQSxPQUFPZ0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUt6RCwyQkFBNUJULG1DQUFBQSxhQUEyQ3dCLElBQUk7Ozs7Ozs7MERBRXJFLDhEQUFDd0Y7Z0RBQUVILFdBQVU7MERBQW1COzs7Ozs7Ozs7Ozs7b0NBS3JDL0Ysd0JBQ0MsOERBQUM4Rjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNHO2dEQUFFSCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7K0NBRXJDN0Ysc0JBQ0YsOERBQUM0Rjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUE0Qjs7Ozs7OzBEQUMzQyw4REFBQ0c7Z0RBQUVILFdBQVU7MERBQWdCN0Y7Ozs7Ozs7Ozs7OytDQUU3QkosU0FBU3FILE1BQU0sS0FBSyxrQkFDdEIsOERBQUNyQjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUFnQjs7Ozs7OzBEQUMvQiw4REFBQ3dCO2dEQUFHeEIsV0FBVTswREFBOEM7Ozs7OzswREFDNUQsOERBQUNHO2dEQUFFSCxXQUFVOzBEQUFtQjs7Ozs7Ozs7Ozs7NkRBR2xDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUN5QjtnREFBTXpCLFdBQVU7O2tFQUNmLDhEQUFDMEI7d0RBQU0xQixXQUFVO2tFQUNmLDRFQUFDMkI7OzhFQUNDLDhEQUFDQztvRUFBRzVCLFdBQVU7OEVBQXNGOzs7Ozs7OEVBR3BHLDhEQUFDNEI7b0VBQUc1QixXQUFVOzhFQUFzRjs7Ozs7OzhFQUdwRyw4REFBQzRCO29FQUFHNUIsV0FBVTs4RUFBc0Y7Ozs7Ozs4RUFHcEcsOERBQUM0QjtvRUFBRzVCLFdBQVU7OEVBQXNGOzs7Ozs7OEVBR3BHLDhEQUFDNEI7b0VBQUc1QixXQUFVOzhFQUFzRjs7Ozs7OzhFQUdwRyw4REFBQzRCO29FQUFHNUIsV0FBVTs4RUFBc0Y7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQUt4Ryw4REFBQzZCO3dEQUFNN0IsV0FBVTtrRUFDZGpHLFNBQVN1QyxHQUFHLENBQUMsQ0FBQ0Msd0JBQ2IsOERBQUNvRjtnRUFBb0IzQixXQUFVOztrRkFDN0IsOERBQUM4Qjt3RUFBRzlCLFdBQVU7a0ZBQ1osNEVBQUNEOzs4RkFDQyw4REFBQ0E7b0ZBQUlDLFdBQVU7O3dGQUNaekQsUUFBUXdGLFNBQVM7d0ZBQUM7d0ZBQUV4RixRQUFReUYsUUFBUTs7Ozs7Ozs4RkFFdkMsOERBQUNqQztvRkFBSUMsV0FBVTs4RkFBNEJ6RCxRQUFRMEYsS0FBSzs7Ozs7OzhGQUN4RCw4REFBQ2xDO29GQUFJQyxXQUFVOzhGQUE0QnpELFFBQVEyRixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztrRkFHNUQsOERBQUNKO3dFQUFHOUIsV0FBVTtrRkFDWiw0RUFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7d0ZBQWM7d0ZBQU96RCxRQUFRNEYsY0FBYzs7Ozs7Ozs4RkFDMUQsOERBQUNwQzs7d0ZBQUk7d0ZBQUt4RCxRQUFRNkYsZUFBZTs7Ozs7Ozs4RkFDakMsOERBQUNyQztvRkFBSUMsV0FBVTs7d0ZBQ1p6RCxRQUFROEYsVUFBVTt3RkFBQzt3RkFBVzlGLFFBQVE4RixVQUFVLEdBQUcsSUFBSSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBSXBFLDhEQUFDUDt3RUFBRzlCLFdBQVU7a0ZBQ1osNEVBQUNEOzRFQUFJQyxXQUFVO3NGQUNaekQsUUFBUTRCLE9BQU8saUJBQ2Q7O2tHQUNFLDhEQUFDNEI7d0ZBQUlDLFdBQVU7a0dBQWV6RCxRQUFRNEIsT0FBTyxDQUFDeEQsSUFBSTs7Ozs7O2tHQUNsRCw4REFBQ29GO3dGQUFJQyxXQUFVO2tHQUFvQnpELFFBQVE0QixPQUFPLENBQUN2RCxRQUFROzs7Ozs7OzZHQUc3RCw4REFBQ3FGO2dGQUFLRCxXQUFVOzBGQUFtQjs7Ozs7Ozs7Ozs7Ozs7OztrRkFJekMsOERBQUM4Qjt3RUFBRzlCLFdBQVU7a0ZBQ1osNEVBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7OEZBQUssSUFBSVAsS0FBS2pELFFBQVErRixVQUFVLEVBQUU3QyxrQkFBa0IsQ0FBQzs7Ozs7OzhGQUN0RCw4REFBQ007b0ZBQUlDLFdBQVU7OEZBQW9CekQsUUFBUWdHLFVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQUd6RCw4REFBQ1Q7d0VBQUc5QixXQUFVO2tGQUNaLDRFQUFDd0M7NEVBQ0MvQixPQUFPbEUsUUFBUUosTUFBTTs0RUFDckJ1RSxVQUFVLENBQUMxRCxJQUFNdkIsb0JBQW9CYyxRQUFRQyxFQUFFLEVBQUVRLEVBQUUyRCxNQUFNLENBQUNGLEtBQUs7NEVBQy9EVCxXQUFXLDRGQUEySCxPQUEvQlgsZUFBZTlDLFFBQVFKLE1BQU07OzhGQUVwSSw4REFBQ3NHO29GQUFPaEMsT0FBTTs4RkFBVTs7Ozs7OzhGQUN4Qiw4REFBQ2dDO29GQUFPaEMsT0FBTTs4RkFBWTs7Ozs7OzhGQUMxQiw4REFBQ2dDO29GQUFPaEMsT0FBTTs4RkFBYzs7Ozs7OzhGQUM1Qiw4REFBQ2dDO29GQUFPaEMsT0FBTTs4RkFBWTs7Ozs7OzhGQUMxQiw4REFBQ2dDO29GQUFPaEMsT0FBTTs4RkFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBRzlCLDhEQUFDcUI7d0VBQUc5QixXQUFVO2tGQUNYVixXQUFXL0MsUUFBUW1HLFNBQVM7Ozs7Ozs7K0RBbkR4Qm5HLFFBQVFDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs2REE4RDdCOztrREFDRSw4REFBQ3VEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ0c7NERBQUdGLFdBQVU7O2dFQUFzRDtpRUFDOUM3RyxnQkFBQUEsT0FBT2dFLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsSUFBSSxLQUFLekQsMkJBQTVCVCxvQ0FBQUEsY0FBMkN3QixJQUFJOzs7Ozs7O3NFQUVyRSw4REFBQ3dGOzREQUFFSCxXQUFVO3NFQUFtQjs7Ozs7Ozs7Ozs7OzhEQUlsQyw4REFBQ2M7b0RBQ0NFLFNBQVMsSUFBTXhGLGlCQUFpQixDQUFDRDtvREFDakN5RSxXQUFVOzhEQUVUekUsZ0JBQWdCLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU1sQyw4REFBQ3dFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNrQjtvREFBR2xCLFdBQVU7OERBQXlDOzs7Ozs7Ozs7OzswREFFekQsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDeUI7b0RBQU16QixXQUFVOztzRUFDZiw4REFBQzBCOzREQUFNMUIsV0FBVTtzRUFDZiw0RUFBQzJCOztrRkFDQyw4REFBQ0M7d0VBQUc1QixXQUFVO2tGQUFvRjs7Ozs7O2tGQUdsRyw4REFBQzRCO3dFQUFHNUIsV0FBVTtrRkFBb0Y7Ozs7OztrRkFHbEcsOERBQUM0Qjt3RUFBRzVCLFdBQVU7a0ZBQW9GOzs7Ozs7a0ZBR2xHLDhEQUFDNEI7d0VBQUc1QixXQUFVO2tGQUFvRjs7Ozs7O2tGQUdsRyw4REFBQzRCO3dFQUFHNUIsV0FBVTtrRkFBb0Y7Ozs7OztrRkFHbEcsOERBQUM0Qjt3RUFBRzVCLFdBQVU7a0ZBQW9GOzs7Ozs7Ozs7Ozs7Ozs7OztzRUFLdEcsOERBQUM2Qjs0REFBTTdCLFdBQVU7O2dFQUNkekYsU0FBUytCLEdBQUcsQ0FBQyxDQUFDNkIsd0JBQ2IsOERBQUN3RDt3RUFBb0IzQixXQUFVOzswRkFDN0IsOERBQUM4QjtnRkFBRzlCLFdBQVU7MEZBQ1osNEVBQUNEO29GQUFJQyxXQUFVOzt3RkFDWjdCLFFBQVFuRCxLQUFLLGtCQUNaLDhEQUFDMkg7NEZBQUkzQyxXQUFVOzRGQUF5QzRDLEtBQUt6RSxRQUFRbkQsS0FBSzs0RkFBRTZILEtBQUsxRSxRQUFReEQsSUFBSTs7Ozs7O3NHQUUvRiw4REFBQ29GOzs4R0FDQyw4REFBQ0E7b0dBQUlDLFdBQVU7OEdBQXdDN0IsUUFBUXhELElBQUk7Ozs7Ozs4R0FDbkUsOERBQUNvRjtvR0FBSUMsV0FBVTs4R0FBOEM3QixRQUFRckQsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBSXRGLDhEQUFDZ0g7Z0ZBQUc5QixXQUFVOzBGQUNaLDRFQUFDQztvRkFBS0QsV0FBVTs4RkFDYjdCLFFBQVF2RCxRQUFRLENBQUNrSSxPQUFPLENBQUMsS0FBSzs7Ozs7Ozs7Ozs7MEZBR25DLDhEQUFDaEI7Z0ZBQUc5QixXQUFVOzBGQUNYN0IsUUFBUXRELFFBQVE7Ozs7OzswRkFFbkIsOERBQUNpSDtnRkFBRzlCLFdBQVU7MEZBQ1g3QixRQUFRbEQsVUFBVTs7Ozs7OzBGQUVyQiw4REFBQzZHO2dGQUFHOUIsV0FBVTswRkFDWiw0RUFBQ0M7b0ZBQUtELFdBQVcsNERBSWhCLE9BSEM3QixRQUFRakQsUUFBUSxHQUNaLGdDQUNBOzhGQUVIaUQsUUFBUWpELFFBQVEsR0FBRyxXQUFXOzs7Ozs7Ozs7OzswRkFHbkMsOERBQUM0RztnRkFBRzlCLFdBQVU7O2tHQUNaLDhEQUFDYzt3RkFDQ0UsU0FBUyxJQUFNOUMsa0JBQWtCQzt3RkFDakM2QixXQUFVO2tHQUNYOzs7Ozs7a0dBR0QsOERBQUNjO3dGQUNDRSxTQUFTLElBQU0zQyxvQkFBb0JGLFFBQVEzQixFQUFFO3dGQUM3Q3dELFdBQVU7a0dBQ1g7Ozs7Ozs7Ozs7Ozs7dUVBMUNJN0IsUUFBUTNCLEVBQUU7Ozs7O2dFQWdEcEJqQyxTQUFTNkcsTUFBTSxLQUFLLG1CQUNuQiw4REFBQ087OEVBQ0MsNEVBQUNHO3dFQUFHaUIsU0FBUzt3RUFBRy9DLFdBQVU7a0ZBQXlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQVc5RXpFLCtCQUNDLDhEQUFDd0U7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNrQjt3REFBR2xCLFdBQVU7a0VBQ1gzRSxpQkFBaUIsaUJBQWlCOzs7Ozs7a0VBRXJDLDhEQUFDOEU7d0RBQUVILFdBQVU7a0VBQ1YzRSxpQkFBaUIsK0JBQStCOzs7Ozs7Ozs7Ozs7MERBR3JELDhEQUFDK0U7Z0RBQUtDLFVBQVV0RDtnREFBbUJpRCxXQUFVOztrRUFDN0MsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7O2tGQUNDLDhEQUFDTzt3RUFBTU4sV0FBVTtrRkFBa0Q7Ozs7OztrRkFHbkUsOERBQUNPO3dFQUNDQyxNQUFLO3dFQUNMQyxPQUFPaEcsVUFBVUUsSUFBSTt3RUFDckIrRixVQUFVLENBQUMxRCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRTFCLE1BQU1xQyxFQUFFMkQsTUFBTSxDQUFDRixLQUFLO2dGQUFDO3dFQUN2RVQsV0FBVTt3RUFDVlksYUFBWTt3RUFDWkMsUUFBUTs7Ozs7Ozs7Ozs7OzBFQUlaLDhEQUFDZDs7a0ZBQ0MsOERBQUNPO3dFQUFNTixXQUFVO2tGQUFrRDs7Ozs7O2tGQUduRSw4REFBQ3dDO3dFQUNDL0IsT0FBT2hHLFVBQVVHLFFBQVE7d0VBQ3pCOEYsVUFBVSxDQUFDMUQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTO29GQUFFLEdBQUdBLElBQUk7b0ZBQUV6QixVQUFVb0MsRUFBRTJELE1BQU0sQ0FBQ0YsS0FBSztnRkFBQzt3RUFDM0VULFdBQVU7d0VBQ1ZhLFFBQVE7OzBGQUVSLDhEQUFDNEI7Z0ZBQU9oQyxPQUFNOzBGQUFjOzs7Ozs7MEZBQzVCLDhEQUFDZ0M7Z0ZBQU9oQyxPQUFNOzBGQUFNOzs7Ozs7NEVBQ25CN0csaUJBQWlCLDBCQUNoQjs7a0dBQ0UsOERBQUM2STt3RkFBT2hDLE9BQU07a0dBQWE7Ozs7OztrR0FDM0IsOERBQUNnQzt3RkFBT2hDLE9BQU07a0dBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBFQU1wQyw4REFBQ1Y7O2tGQUNDLDhEQUFDTzt3RUFBTU4sV0FBVTtrRkFBa0Q7Ozs7OztrRkFHbkUsOERBQUNPO3dFQUNDQyxNQUFLO3dFQUNMQyxPQUFPaEcsVUFBVUksUUFBUTt3RUFDekI2RixVQUFVLENBQUMxRCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7b0ZBQUUsR0FBR0EsSUFBSTtvRkFBRXhCLFVBQVVtQyxFQUFFMkQsTUFBTSxDQUFDRixLQUFLO2dGQUFDO3dFQUMzRVQsV0FBVTt3RUFDVlksYUFBWTt3RUFDWkMsUUFBUTs7Ozs7Ozs7Ozs7OzBFQUlaLDhEQUFDZDs7a0ZBQ0MsOERBQUNPO3dFQUFNTixXQUFVO2tGQUFrRDs7Ozs7O2tGQUduRSw4REFBQ087d0VBQ0NDLE1BQUs7d0VBQ0xDLE9BQU9oRyxVQUFVUSxVQUFVO3dFQUMzQnlGLFVBQVUsQ0FBQzFELElBQU10QyxhQUFhMkIsQ0FBQUEsT0FBUztvRkFBRSxHQUFHQSxJQUFJO29GQUFFcEIsWUFBWStCLEVBQUUyRCxNQUFNLENBQUNGLEtBQUs7Z0ZBQUM7d0VBQzdFVCxXQUFVO3dFQUNWWSxhQUFZO3dFQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS2QsOERBQUNkOzswRUFDQyw4REFBQ087Z0VBQU1OLFdBQVU7MEVBQWtEOzs7Ozs7MEVBR25FLDhEQUFDZ0Q7Z0VBQ0N2QyxPQUFPaEcsVUFBVUssV0FBVztnRUFDNUI0RixVQUFVLENBQUMxRCxJQUFNdEMsYUFBYTJCLENBQUFBLE9BQVM7NEVBQUUsR0FBR0EsSUFBSTs0RUFBRXZCLGFBQWFrQyxFQUFFMkQsTUFBTSxDQUFDRixLQUFLO3dFQUFDO2dFQUM5RXdDLE1BQU07Z0VBQ05qRCxXQUFVO2dFQUNWWSxhQUFZO2dFQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7a0VBSVosOERBQUNkOzswRUFDQyw4REFBQ087Z0VBQU1OLFdBQVU7MEVBQWtEOzs7Ozs7MEVBR25FLDhEQUFDTztnRUFDQ0MsTUFBSztnRUFDTEMsT0FBT2hHLFVBQVVNLFFBQVE7Z0VBQ3pCMkYsVUFBVSxDQUFDMUQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUV0QixVQUFVaUMsRUFBRTJELE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztnRUFDM0VULFdBQVU7Z0VBQ1ZZLGFBQVk7Ozs7Ozs7Ozs7OztrRUFJaEIsOERBQUNiOzswRUFDQyw4REFBQ087Z0VBQU1OLFdBQVU7MEVBQWtEOzs7Ozs7MEVBR25FLDhEQUFDTztnRUFDQ0MsTUFBSztnRUFDTEMsT0FBT2hHLFVBQVVPLEtBQUs7Z0VBQ3RCMEYsVUFBVSxDQUFDMUQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUVyQixPQUFPZ0MsRUFBRTJELE1BQU0sQ0FBQ0YsS0FBSzt3RUFBQztnRUFDeEVULFdBQVU7Z0VBQ1ZZLGFBQVk7Ozs7Ozs7Ozs7OztrRUFJaEIsOERBQUNiO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ087Z0VBQ0NDLE1BQUs7Z0VBQ0xoRSxJQUFHO2dFQUNIMEcsU0FBU3pJLFVBQVVTLFFBQVE7Z0VBQzNCd0YsVUFBVSxDQUFDMUQsSUFBTXRDLGFBQWEyQixDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUVuQixVQUFVOEIsRUFBRTJELE1BQU0sQ0FBQ3VDLE9BQU87d0VBQUM7Z0VBQzdFbEQsV0FBVTs7Ozs7OzBFQUVaLDhEQUFDTTtnRUFBTTZDLFNBQVE7Z0VBQVduRCxXQUFVOzBFQUFzQzs7Ozs7Ozs7Ozs7O2tFQUs1RSw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDYztnRUFDQ04sTUFBSztnRUFDTFEsU0FBU25EO2dFQUNUbUMsV0FBVTswRUFDWDs7Ozs7OzBFQUdELDhEQUFDYztnRUFDQ04sTUFBSztnRUFDTDRDLFVBQVVqSTtnRUFDVjZFLFdBQVcsaUZBSVYsT0FIQzdFLGtCQUNJLGtDQUNBOzBFQUdMQSxrQkFDSUUsaUJBQWlCLGdCQUFnQixjQUNqQ0EsaUJBQWlCLG1CQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBY2pFO0dBanpCd0JuQztLQUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL3RhaGFmYXJvb3F1aS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9HZW8tVHJhdmVscy9mcm9udGVuZC9zcmMvYXBwL2FkbWluL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGFwaVNlcnZpY2UsIENpdHkgfSBmcm9tICdAL3NlcnZpY2VzL2FwaSc7XG5cbmludGVyZmFjZSBCb29raW5nUmVxdWVzdCB7XG4gIGlkOiBzdHJpbmc7XG4gIGZpcnN0TmFtZTogc3RyaW5nO1xuICBsYXN0TmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBwaG9uZTogc3RyaW5nO1xuICBwaWNrdXBMb2NhdGlvbjogc3RyaW5nO1xuICBkcm9wb2ZmTG9jYXRpb246IHN0cmluZztcbiAgcGlja3VwRGF0ZTogc3RyaW5nO1xuICBwaWNrdXBUaW1lOiBzdHJpbmc7XG4gIHBhc3NlbmdlcnM6IG51bWJlcjtcbiAgc3BlY2lhbFJlcXVlc3RzPzogc3RyaW5nO1xuICBzdGF0dXM6ICdQRU5ESU5HJyB8ICdDT05GSVJNRUQnIHwgJ0lOX1BST0dSRVNTJyB8ICdDT01QTEVURUQnIHwgJ0NBTkNFTExFRCc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICBjaXR5OiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgc2x1Zzogc3RyaW5nO1xuICB9O1xuICB2ZWhpY2xlPzoge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIGNhdGVnb3J5OiBzdHJpbmc7XG4gIH07XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluUGFnZSgpIHtcbiAgY29uc3QgW2lzQXV0aGVudGljYXRlZCwgc2V0SXNBdXRoZW50aWNhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2xvZ2luRW1haWwsIHNldExvZ2luRW1haWxdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbG9naW5QYXNzd29yZCwgc2V0TG9naW5QYXNzd29yZF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtsb2dpbkVycm9yLCBzZXRMb2dpbkVycm9yXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2l0eSwgc2V0U2VsZWN0ZWRDaXR5XSA9IHVzZVN0YXRlKCdsb25kb24nKTtcbiAgY29uc3QgW2NpdGllcywgc2V0Q2l0aWVzXSA9IHVzZVN0YXRlPENpdHlbXT4oW10pO1xuICBjb25zdCBbYm9va2luZ3MsIHNldEJvb2tpbmdzXSA9IHVzZVN0YXRlPEJvb2tpbmdSZXF1ZXN0W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZTwnYm9va2luZ3MnIHwgJ2ZsZWV0Jz4oJ2Jvb2tpbmdzJyk7XG5cbiAgLy8gRmxlZXQgbWFuYWdlbWVudCBzdGF0ZVxuICBjb25zdCBbdmVoaWNsZXMsIHNldFZlaGljbGVzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtmbGVldEZvcm0sIHNldEZsZWV0Rm9ybV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogJycsXG4gICAgY2F0ZWdvcnk6ICdQUklWQVRFX0NBUicsXG4gICAgY2FwYWNpdHk6ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBmZWF0dXJlczogJycsXG4gICAgaW1hZ2U6ICcnLFxuICAgIHByaWNlUmFuZ2U6ICcnLFxuICAgIGlzQWN0aXZlOiB0cnVlXG4gIH0pO1xuICBjb25zdCBbc3VibWl0dGluZ0ZsZWV0LCBzZXRTdWJtaXR0aW5nRmxlZXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdGluZ1ZlaGljbGUsIHNldEVkaXRpbmdWZWhpY2xlXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2hvd0ZsZWV0Rm9ybSwgc2V0U2hvd0ZsZWV0Rm9ybV0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gU3RhdHVzIHVwZGF0ZSBmdW5jdGlvbmFsaXR5XG4gIGNvbnN0IHVwZGF0ZUJvb2tpbmdTdGF0dXMgPSBhc3luYyAoYm9va2luZ0lkOiBzdHJpbmcsIG5ld1N0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvYm9va2luZy8ke2Jvb2tpbmdJZH0vc3RhdHVzYCwge1xuICAgICAgICBtZXRob2Q6ICdQQVRDSCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1czogbmV3U3RhdHVzIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAvLyBVcGRhdGUgdGhlIGJvb2tpbmcgaW4gdGhlIGxvY2FsIHN0YXRlXG4gICAgICAgIHNldEJvb2tpbmdzKHByZXYgPT4gcHJldi5tYXAoYm9va2luZyA9PlxuICAgICAgICAgIGJvb2tpbmcuaWQgPT09IGJvb2tpbmdJZFxuICAgICAgICAgICAgPyB7IC4uLmJvb2tpbmcsIHN0YXR1czogbmV3U3RhdHVzIGFzIGFueSB9XG4gICAgICAgICAgICA6IGJvb2tpbmdcbiAgICAgICAgKSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbGVydCgnRmFpbGVkIHRvIHVwZGF0ZSBib29raW5nIHN0YXR1cycpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm9yIHVwZGF0aW5nIGJvb2tpbmcgc3RhdHVzJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZldGNoIHZlaGljbGVzIGZvciBzZWxlY3RlZCBjaXR5XG4gIGNvbnN0IGZldGNoVmVoaWNsZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQgfHwgIXNlbGVjdGVkQ2l0eSkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdmVoaWNsZXM/Y2l0eT0ke3NlbGVjdGVkQ2l0eX1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBjb25zb2xlLmxvZygnVmVoaWNsZXMgZGF0YTonLCBkYXRhKTsgLy8gRGVidWcgbG9nXG4gICAgICAgIHNldFZlaGljbGVzKGRhdGEuZGF0YSB8fCBbXSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggdmVoaWNsZXM6JywgcmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdmVoaWNsZXM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGbGVldCBtYW5hZ2VtZW50IGZ1bmN0aW9uc1xuICBjb25zdCBoYW5kbGVGbGVldFN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0U3VibWl0dGluZ0ZsZWV0KHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNlbGVjdGVkQ2l0eURhdGEgPSBjaXRpZXMuZmluZChjID0+IGMuc2x1ZyA9PT0gc2VsZWN0ZWRDaXR5KTtcbiAgICAgIGlmICghc2VsZWN0ZWRDaXR5RGF0YSkge1xuICAgICAgICBhbGVydCgnUGxlYXNlIHNlbGVjdCBhIGNpdHkgZmlyc3QnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBmbGVldERhdGEgPSB7XG4gICAgICAgIC4uLmZsZWV0Rm9ybSxcbiAgICAgICAgZmVhdHVyZXM6IGZsZWV0Rm9ybS5mZWF0dXJlcy5zcGxpdCgnLCcpLm1hcChmID0+IGYudHJpbSgpKS5maWx0ZXIoZiA9PiBmKSxcbiAgICAgICAgY2l0eUlkOiBzZWxlY3RlZENpdHlEYXRhLmlkXG4gICAgICB9O1xuXG4gICAgICBjb25zdCB1cmwgPSBlZGl0aW5nVmVoaWNsZVxuICAgICAgICA/IGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzLyR7ZWRpdGluZ1ZlaGljbGV9YFxuICAgICAgICA6ICdodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzJztcblxuICAgICAgY29uc3QgbWV0aG9kID0gZWRpdGluZ1ZlaGljbGUgPyAnUFVUJyA6ICdQT1NUJztcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgbWV0aG9kLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZmxlZXREYXRhKSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYWxlcnQoYFZlaGljbGUgJHtlZGl0aW5nVmVoaWNsZSA/ICd1cGRhdGVkJyA6ICdhZGRlZCd9IHN1Y2Nlc3NmdWxseSFgKTtcbiAgICAgICAgcmVzZXRGbGVldEZvcm0oKTtcbiAgICAgICAgLy8gUmVmcmVzaCBkYXRhXG4gICAgICAgIGNvbnN0IGNpdGllc0RhdGEgPSBhd2FpdCBhcGlTZXJ2aWNlLmdldENpdGllcygpO1xuICAgICAgICBzZXRDaXRpZXMoY2l0aWVzRGF0YSk7XG4gICAgICAgIGZldGNoVmVoaWNsZXMoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgYWxlcnQoYEZhaWxlZCB0byAke2VkaXRpbmdWZWhpY2xlID8gJ3VwZGF0ZScgOiAnYWRkJ30gdmVoaWNsZTogJHtlcnJvckRhdGEubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcid9YCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyB2ZWhpY2xlOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvciBhZGRpbmcgdmVoaWNsZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0U3VibWl0dGluZ0ZsZWV0KGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVzZXRGbGVldEZvcm0gPSAoKSA9PiB7XG4gICAgc2V0RmxlZXRGb3JtKHtcbiAgICAgIG5hbWU6ICcnLFxuICAgICAgY2F0ZWdvcnk6ICdQUklWQVRFX0NBUicsXG4gICAgICBjYXBhY2l0eTogJycsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBmZWF0dXJlczogJycsXG4gICAgICBpbWFnZTogJycsXG4gICAgICBwcmljZVJhbmdlOiAnJyxcbiAgICAgIGlzQWN0aXZlOiB0cnVlXG4gICAgfSk7XG4gICAgc2V0RWRpdGluZ1ZlaGljbGUobnVsbCk7XG4gICAgc2V0U2hvd0ZsZWV0Rm9ybShmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdFZlaGljbGUgPSAodmVoaWNsZTogYW55KSA9PiB7XG4gICAgc2V0RmxlZXRGb3JtKHtcbiAgICAgIG5hbWU6IHZlaGljbGUubmFtZSxcbiAgICAgIGNhdGVnb3J5OiB2ZWhpY2xlLmNhdGVnb3J5LFxuICAgICAgY2FwYWNpdHk6IHZlaGljbGUuY2FwYWNpdHksXG4gICAgICBkZXNjcmlwdGlvbjogdmVoaWNsZS5kZXNjcmlwdGlvbixcbiAgICAgIGZlYXR1cmVzOiB2ZWhpY2xlLmZlYXR1cmVzLmpvaW4oJywgJyksXG4gICAgICBpbWFnZTogdmVoaWNsZS5pbWFnZSB8fCAnJyxcbiAgICAgIHByaWNlUmFuZ2U6IHZlaGljbGUucHJpY2VSYW5nZSxcbiAgICAgIGlzQWN0aXZlOiB2ZWhpY2xlLmlzQWN0aXZlXG4gICAgfSk7XG4gICAgc2V0RWRpdGluZ1ZlaGljbGUodmVoaWNsZS5pZCk7XG4gICAgc2V0U2hvd0ZsZWV0Rm9ybSh0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVWZWhpY2xlID0gYXN5bmMgKHZlaGljbGVJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgdmVoaWNsZT8nKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGBodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL3ZlaGljbGVzLyR7dmVoaWNsZUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgYWxlcnQoJ1ZlaGljbGUgZGVsZXRlZCBzdWNjZXNzZnVsbHkhJyk7XG4gICAgICAgIC8vIFJlZnJlc2ggZGF0YVxuICAgICAgICBjb25zdCBjaXRpZXNEYXRhID0gYXdhaXQgYXBpU2VydmljZS5nZXRDaXRpZXMoKTtcbiAgICAgICAgc2V0Q2l0aWVzKGNpdGllc0RhdGEpO1xuICAgICAgICBmZXRjaFZlaGljbGVzKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGFsZXJ0KGBGYWlsZWQgdG8gZGVsZXRlIHZlaGljbGU6ICR7ZXJyb3JEYXRhLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InfWApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyB2ZWhpY2xlOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvciBkZWxldGluZyB2ZWhpY2xlLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH1cbiAgfTtcblxuICAvLyBDaGVjayBpZiB1c2VyIGlzIGFscmVhZHkgYXV0aGVudGljYXRlZFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGF1dGhTdGF0dXMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaW5BdXRoZW50aWNhdGVkJyk7XG4gICAgaWYgKGF1dGhTdGF0dXMgPT09ICd0cnVlJykge1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZUxvZ2luID0gKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRMb2dpbkVycm9yKCcnKTtcblxuICAgIGlmIChsb2dpbkVtYWlsID09PSAnYWRtaW5AZ29nZW90cmF2ZWxzLmNvbScgJiYgbG9naW5QYXNzd29yZCA9PT0gJ2FkbWluJykge1xuICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKHRydWUpO1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FkbWluQXV0aGVudGljYXRlZCcsICd0cnVlJyk7XG4gICAgICBzZXRMb2dpbkVtYWlsKCcnKTtcbiAgICAgIHNldExvZ2luUGFzc3dvcmQoJycpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRMb2dpbkVycm9yKCdJbnZhbGlkIGVtYWlsIG9yIHBhc3N3b3JkJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcbiAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoZmFsc2UpO1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhZG1pbkF1dGhlbnRpY2F0ZWQnKTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNBdXRoZW50aWNhdGVkKSByZXR1cm47XG4gICAgY29uc3QgZmV0Y2hDaXRpZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBjaXRpZXNEYXRhID0gYXdhaXQgYXBpU2VydmljZS5nZXRDaXRpZXMoKTtcbiAgICAgICAgc2V0Q2l0aWVzKGNpdGllc0RhdGEpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2l0aWVzOicsIGVycm9yKTtcbiAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGNpdGllcycpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBmZXRjaENpdGllcygpO1xuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBmZXRjaEJvb2tpbmdzID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMfS9ib29raW5nP2NpdHk9JHtzZWxlY3RlZENpdHl9YCk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAgIHNldEJvb2tpbmdzKGRhdGEuZGF0YSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGJvb2tpbmdzJyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGJvb2tpbmdzOicsIGVycm9yKTtcbiAgICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGJvb2tpbmdzJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaWYgKHNlbGVjdGVkQ2l0eSkge1xuICAgICAgZmV0Y2hCb29raW5ncygpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkQ2l0eV0pO1xuXG4gIC8vIFNlcGFyYXRlIHVzZUVmZmVjdCBmb3IgdmVoaWNsZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNBdXRoZW50aWNhdGVkICYmIHNlbGVjdGVkQ2l0eSkge1xuICAgICAgZmV0Y2hWZWhpY2xlcygpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkQ2l0eSwgaXNBdXRoZW50aWNhdGVkXSk7XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnUEVORElORyc6IHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnO1xuICAgICAgY2FzZSAnQ09ORklSTUVEJzogcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJztcbiAgICAgIGNhc2UgJ0lOX1BST0dSRVNTJzogcmV0dXJuICdiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMCc7XG4gICAgICBjYXNlICdDT01QTEVURUQnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCc7XG4gICAgICBjYXNlICdDQU5DRUxMRUQnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVTdHJpbmcpLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tR0InLCB7XG4gICAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgeWVhcjogJ251bWVyaWMnLFxuICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgbWludXRlOiAnMi1kaWdpdCdcbiAgICB9KTtcbiAgfTtcblxuICAvLyBTaG93IGxvZ2luIGZvcm0gaWYgbm90IGF1dGhlbnRpY2F0ZWRcbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LTUwIHRvLWFjY2VudC01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtMnhsIHNoYWRvdy1lbGVnYW50LXhsIHAtOCB3LWZ1bGwgbWF4LXctbWRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLWFjY2VudC1ncmFkaWVudCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC0yeGxcIj5HPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZXJpZiBmb250LWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi0yXCI+QWRtaW4gUG9ydGFsPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5Hb0dlbyBUcmF2ZWxzIExvbmRvbjwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVMb2dpbn0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBFbWFpbCBBZGRyZXNzXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2xvZ2luRW1haWx9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRMb2dpbkVtYWlsKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiYWRtaW5AZ29nZW90cmF2ZWxzLmNvbVwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBQYXNzd29yZFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtsb2dpblBhc3N3b3JkfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TG9naW5QYXNzd29yZChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgcGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge2xvZ2luRXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgdGV4dC1yZWQtNzAwIHB4LTQgcHktMyByb3VuZGVkLWxnIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7bG9naW5FcnJvcn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctYWNjZW50LTUwMCB0ZXh0LXdoaXRlIHB5LTMgcHgtNCByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctYWNjZW50LTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBTaWduIEluXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctbmV1dHJhbC01MFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LWxnIGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctYWNjZW50LWdyYWRpZW50IHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LWxnXCI+Rzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZXJpZiBmb250LWJvbGQgdGV4dC1wcmltYXJ5LTkwMFwiPkdvR2VvIFRyYXZlbHM8L2gxPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMFwiPkFkbWluIFBvcnRhbDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTcwMFwiPldlbGNvbWUsIEFkbWluPC9zcGFuPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1hY2NlbnQtNjAwIGhvdmVyOnRleHQtYWNjZW50LTcwMCBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBMb2dvdXRcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2hlYWRlcj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XG4gICAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgICA8YXNpZGUgY2xhc3NOYW1lPVwidy02NCBiZy13aGl0ZSBzaGFkb3ctbGcgaC1zY3JlZW4gc3RpY2t5IHRvcC0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIFRhYnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMSBiZy1uZXV0cmFsLTEwMCByb3VuZGVkLWxnIHAtMVwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYignYm9va2luZ3MnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBweS0yIHB4LTMgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gJ2Jvb2tpbmdzJ1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXdoaXRlIHRleHQtcHJpbWFyeS05MDAgc2hhZG93LXNtJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcHJpbWFyeS02MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTkwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEJvb2tpbmdzXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0QWN0aXZlVGFiKCdmbGVldCcpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleC0xIHB5LTIgcHgtMyByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSAnZmxlZXQnXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctd2hpdGUgdGV4dC1wcmltYXJ5LTkwMCBzaGFkb3ctc20nXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1wcmltYXJ5LTYwMCBob3Zlcjp0ZXh0LXByaW1hcnktOTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgRmxlZXRcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIG1iLTZcIj5DaXRpZXM8L2gyPlxuICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge2NpdGllcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwIHRleHQtc21cIj5Mb2FkaW5nIGNpdGllcy4uLjwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIGNpdGllcy5tYXAoKGNpdHkpID0+IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2NpdHkuaWR9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZENpdHkoY2l0eS5zbHVnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0zIHJvdW5kZWQteGwgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRDaXR5ID09PSBjaXR5LnNsdWdcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1hY2NlbnQtNTAwIHRleHQtd2hpdGUgc2hhZG93LW1kJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtcHJpbWFyeS03MDAgaG92ZXI6YmctcHJpbWFyeS01MCBob3Zlcjp0ZXh0LXByaW1hcnktOTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57Y2l0eS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBvcGFjaXR5LTc1XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2NpdHkuX2NvdW50LnZlaGljbGVzfSB2ZWhpY2xlc1xuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9uYXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvYXNpZGU+XG5cbiAgICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIHAtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIHthY3RpdmVUYWIgPT09ICdib29raW5ncycgPyAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1zZXJpZiBmb250LWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIEJvb2tpbmcgUmVxdWVzdHMgLSB7Y2l0aWVzLmZpbmQoYyA9PiBjLnNsdWcgPT09IHNlbGVjdGVkQ2l0eSk/Lm5hbWV9XG4gICAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICBNYW5hZ2UgYW5kIHRyYWNrIGFsbCBib29raW5nIHJlcXVlc3RzIGZvciB0aGlzIGNpdHlcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWFjY2VudC01MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDAgbXQtNFwiPkxvYWRpbmcgYm9va2luZ3MuLi48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IGVycm9yID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC14bCBtYi00XCI+4pqg77iPPC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogYm9va2luZ3MubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi00XCI+8J+TizwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCBtYi0yXCI+Tm8gYm9va2luZ3MgZm91bmQ8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5ObyBib29raW5nIHJlcXVlc3RzIGZvciB0aGlzIGNpdHkgeWV0LjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtM3hsIGVsZWdhbnQtc2hhZG93LWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ3VzdG9tZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgSm91cm5leVxuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIERhdGUgJiBUaW1lXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LWxlZnQgdGV4dC14cyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1zZW1pYm9sZCB0ZXh0LXByaW1hcnktOTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBDcmVhdGVkXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Ym9va2luZ3MubWFwKChib29raW5nKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8dHIga2V5PXtib29raW5nLmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwIHNtb290aC10cmFuc2l0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Ym9va2luZy5maXJzdE5hbWV9IHtib29raW5nLmxhc3ROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMFwiPntib29raW5nLmVtYWlsfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS02MDBcIj57Ym9va2luZy5waG9uZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RnJvbToge2Jvb2tpbmcucGlja3VwTG9jYXRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlRvOiB7Ym9va2luZy5kcm9wb2ZmTG9jYXRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Ym9va2luZy5wYXNzZW5nZXJzfSBwYXNzZW5nZXJ7Ym9va2luZy5wYXNzZW5nZXJzID4gMSA/ICdzJyA6ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Jvb2tpbmcudmVoaWNsZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Jvb2tpbmcudmVoaWNsZS5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTYwMFwiPntib29raW5nLnZlaGljbGUuY2F0ZWdvcnl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5LTUwMFwiPk5vdCBzcGVjaWZpZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+e25ldyBEYXRlKGJvb2tpbmcucGlja3VwRGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1HQicpfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwXCI+e2Jvb2tpbmcucGlja3VwVGltZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtib29raW5nLnN0YXR1c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlQm9va2luZ1N0YXR1cyhib29raW5nLmlkLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1zZW1pYm9sZCBib3JkZXItMCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwICR7Z2V0U3RhdHVzQ29sb3IoYm9va2luZy5zdGF0dXMpfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBFTkRJTkdcIj5QRU5ESU5HPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ09ORklSTUVEXCI+Q09ORklSTUVEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSU5fUFJPR1JFU1NcIj5JTl9QUk9HUkVTUzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNPTVBMRVRFRFwiPkNPTVBMRVRFRDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkNBTkNFTExFRFwiPkNBTkNFTExFRDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGJvb2tpbmcuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtc2VyaWYgZm9udC1ib2xkIHRleHQtcHJpbWFyeS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgRmxlZXQgTWFuYWdlbWVudCAtIHtjaXRpZXMuZmluZChjID0+IGMuc2x1ZyA9PT0gc2VsZWN0ZWRDaXR5KT8ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIE1hbmFnZSBwcmVtaXVtIHZlaGljbGVzIGZvciB0aGlzIGNpdHlcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0ZsZWV0Rm9ybSghc2hvd0ZsZWV0Rm9ybSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYWNjZW50LTUwMCB0ZXh0LXdoaXRlIHB4LTYgcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctYWNjZW50LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2hvd0ZsZWV0Rm9ybSA/ICdDYW5jZWwnIDogJ0FkZCBOZXcgVmVoaWNsZSd9XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVmVoaWNsZSBMaXN0ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LWxnIG1iLThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDBcIj5DdXJyZW50IEZsZWV0PC9oMj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgZGl2aWRlLXkgZGl2aWRlLW5ldXRyYWwtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLW5ldXRyYWwtNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFZlaGljbGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENhdGVnb3J5XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTcwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYXBhY2l0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUHJpY2UgUmFuZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktNzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN0YXR1c1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcHJpbWFyeS03MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQWN0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtbmV1dHJhbC0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt2ZWhpY2xlcy5tYXAoKHZlaGljbGUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17dmVoaWNsZS5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctbmV1dHJhbC01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUuaW1hZ2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHJvdW5kZWQtbGcgb2JqZWN0LWNvdmVyIG1yLTNcIiBzcmM9e3ZlaGljbGUuaW1hZ2V9IGFsdD17dmVoaWNsZS5uYW1lfSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktOTAwXCI+e3ZlaGljbGUubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1wcmltYXJ5LTYwMCB0cnVuY2F0ZSBtYXgtdy14c1wiPnt2ZWhpY2xlLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS0xMDAgdGV4dC1wcmltYXJ5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZS5jYXRlZ29yeS5yZXBsYWNlKCdfJywgJyAnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZS5jYXBhY2l0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LXByaW1hcnktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZS5wcmljZVJhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVoaWNsZS5pc0FjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtMTAwIHRleHQtcmVkLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZlaGljbGUuaXNBY3RpdmUgPyAnQWN0aXZlJyA6ICdJbmFjdGl2ZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdFZlaGljbGUodmVoaWNsZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYWNjZW50LTYwMCBob3Zlcjp0ZXh0LWFjY2VudC05MDAgbXItM1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEVkaXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVWZWhpY2xlKHZlaGljbGUuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICB7dmVoaWNsZXMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjb2xTcGFuPXs2fSBjbGFzc05hbWU9XCJweC02IHB5LTggdGV4dC1jZW50ZXIgdGV4dC1wcmltYXJ5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTm8gdmVoaWNsZXMgZm91bmQgZm9yIHRoaXMgY2l0eS4gQWRkIHlvdXIgZmlyc3QgdmVoaWNsZSB0byBnZXQgc3RhcnRlZC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogVmVoaWNsZSBGb3JtICovfVxuICAgICAgICAgICAgICAgIHtzaG93RmxlZXRGb3JtICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC0yeGwgc2hhZG93LWxnIHAtOFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtlZGl0aW5nVmVoaWNsZSA/ICdFZGl0IFZlaGljbGUnIDogJ0FkZCBOZXcgVmVoaWNsZSd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnktNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtlZGl0aW5nVmVoaWNsZSA/ICdVcGRhdGUgdmVoaWNsZSBpbmZvcm1hdGlvbicgOiAnQWRkIGEgbmV3IHZlaGljbGUgdG8gdGhlIGZsZWV0J31cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlRmxlZXRTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFZlaGljbGUgTmFtZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZsZWV0Rm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCBNZXJjZWRlcyBTLUNsYXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFZlaGljbGUgQ2F0ZWdvcnkgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBjYXRlZ29yeTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUFJJVkFURV9DQVJcIj5Qcml2YXRlIENhcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQlVTXCI+UHJpdmF0ZSBCdXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkQ2l0eSA9PT0gJ2xvbmRvbicgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiSEVMSUNPUFRFUlwiPkhlbGljb3B0ZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQUklWQVRFX0pFVFwiPlByaXZhdGUgSmV0PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ2FwYWNpdHkgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0uY2FwYWNpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgY2FwYWNpdHk6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJlLmcuLCA0IHBhc3NlbmdlcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUHJpY2UgUmFuZ2UgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmbGVldEZvcm0ucHJpY2VSYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBwcmljZVJhbmdlOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFjY2VudC01MDAgZm9jdXM6Ym9yZGVyLWFjY2VudC01MDAgdGV4dC1wcmltYXJ5LTkwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgwqMyMDAtMzAwL2hvdXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXByaW1hcnktODAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIERlc2NyaXB0aW9uICpcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZsZWV0Rm9ybS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmxlZXRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hY2NlbnQtNTAwIGZvY3VzOmJvcmRlci1hY2NlbnQtNTAwIHRleHQtcHJpbWFyeS05MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmliZSB0aGUgdmVoaWNsZSBhbmQgaXRzIGx1eHVyeSBmZWF0dXJlcy4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBGZWF0dXJlcyAoY29tbWEtc2VwYXJhdGVkKVxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmxlZXRGb3JtLmZlYXR1cmVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBmZWF0dXJlczogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZS5nLiwgTGVhdGhlciBzZWF0cywgV2lGaSwgQ2hhbXBhZ25lIHNlcnZpY2UsIFByb2Zlc3Npb25hbCBjaGF1ZmZldXJcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5LTgwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBJbWFnZSBVUkxcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInVybFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmxlZXRGb3JtLmltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBpbWFnZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTQgcHktMyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYWNjZW50LTUwMCBmb2N1czpib3JkZXItYWNjZW50LTUwMCB0ZXh0LXByaW1hcnktOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9leGFtcGxlLmNvbS92ZWhpY2xlLWltYWdlLmpwZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiaXNBY3RpdmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17ZmxlZXRGb3JtLmlzQWN0aXZlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGbGVldEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBpc0FjdGl2ZTogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYWNjZW50LTYwMCBmb2N1czpyaW5nLWFjY2VudC01MDAgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJpc0FjdGl2ZVwiIGNsYXNzTmFtZT1cIm1sLTIgYmxvY2sgdGV4dC1zbSB0ZXh0LXByaW1hcnktODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBWZWhpY2xlIGlzIGFjdGl2ZSBhbmQgYXZhaWxhYmxlIGZvciBib29raW5nXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17cmVzZXRGbGVldEZvcm19XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1uZXV0cmFsLTIwMCB0ZXh0LW5ldXRyYWwtNzAwIHB4LTYgcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctbmV1dHJhbC0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3N1Ym1pdHRpbmdGbGVldH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGJnLWFjY2VudC01MDAgdGV4dC13aGl0ZSBweC04IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN1Ym1pdHRpbmdGbGVldFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWFjY2VudC02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICB7c3VibWl0dGluZ0ZsZWV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gKGVkaXRpbmdWZWhpY2xlID8gJ1VwZGF0aW5nLi4uJyA6ICdBZGRpbmcuLi4nKVxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IChlZGl0aW5nVmVoaWNsZSA/ICdVcGRhdGUgVmVoaWNsZScgOiAnQWRkIFZlaGljbGUnKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImFwaVNlcnZpY2UiLCJBZG1pblBhZ2UiLCJjaXRpZXMiLCJpc0F1dGhlbnRpY2F0ZWQiLCJzZXRJc0F1dGhlbnRpY2F0ZWQiLCJsb2dpbkVtYWlsIiwic2V0TG9naW5FbWFpbCIsImxvZ2luUGFzc3dvcmQiLCJzZXRMb2dpblBhc3N3b3JkIiwibG9naW5FcnJvciIsInNldExvZ2luRXJyb3IiLCJzZWxlY3RlZENpdHkiLCJzZXRTZWxlY3RlZENpdHkiLCJzZXRDaXRpZXMiLCJib29raW5ncyIsInNldEJvb2tpbmdzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwidmVoaWNsZXMiLCJzZXRWZWhpY2xlcyIsImZsZWV0Rm9ybSIsInNldEZsZWV0Rm9ybSIsIm5hbWUiLCJjYXRlZ29yeSIsImNhcGFjaXR5IiwiZGVzY3JpcHRpb24iLCJmZWF0dXJlcyIsImltYWdlIiwicHJpY2VSYW5nZSIsImlzQWN0aXZlIiwic3VibWl0dGluZ0ZsZWV0Iiwic2V0U3VibWl0dGluZ0ZsZWV0IiwiZWRpdGluZ1ZlaGljbGUiLCJzZXRFZGl0aW5nVmVoaWNsZSIsInNob3dGbGVldEZvcm0iLCJzZXRTaG93RmxlZXRGb3JtIiwidXBkYXRlQm9va2luZ1N0YXR1cyIsImJvb2tpbmdJZCIsIm5ld1N0YXR1cyIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdGF0dXMiLCJvayIsInByZXYiLCJtYXAiLCJib29raW5nIiwiaWQiLCJhbGVydCIsImNvbnNvbGUiLCJmZXRjaFZlaGljbGVzIiwiZGF0YSIsImpzb24iLCJsb2ciLCJoYW5kbGVGbGVldFN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInNlbGVjdGVkQ2l0eURhdGEiLCJmaW5kIiwiYyIsInNsdWciLCJmbGVldERhdGEiLCJzcGxpdCIsImYiLCJ0cmltIiwiZmlsdGVyIiwiY2l0eUlkIiwidXJsIiwicmVzZXRGbGVldEZvcm0iLCJjaXRpZXNEYXRhIiwiZ2V0Q2l0aWVzIiwiZXJyb3JEYXRhIiwibWVzc2FnZSIsImhhbmRsZUVkaXRWZWhpY2xlIiwidmVoaWNsZSIsImpvaW4iLCJoYW5kbGVEZWxldGVWZWhpY2xlIiwidmVoaWNsZUlkIiwiY29uZmlybSIsImF1dGhTdGF0dXMiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiaGFuZGxlTG9naW4iLCJzZXRJdGVtIiwiaGFuZGxlTG9nb3V0IiwicmVtb3ZlSXRlbSIsImZldGNoQ2l0aWVzIiwiZmV0Y2hCb29raW5ncyIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwic3VjY2VzcyIsImdldFN0YXR1c0NvbG9yIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZGF5IiwibW9udGgiLCJ5ZWFyIiwiaG91ciIsIm1pbnV0ZSIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJoMSIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIiwiYnV0dG9uIiwiaGVhZGVyIiwib25DbGljayIsImFzaWRlIiwiaDIiLCJuYXYiLCJsZW5ndGgiLCJjaXR5IiwiX2NvdW50IiwibWFpbiIsImgzIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJlbWFpbCIsInBob25lIiwicGlja3VwTG9jYXRpb24iLCJkcm9wb2ZmTG9jYXRpb24iLCJwYXNzZW5nZXJzIiwicGlja3VwRGF0ZSIsInBpY2t1cFRpbWUiLCJzZWxlY3QiLCJvcHRpb24iLCJjcmVhdGVkQXQiLCJpbWciLCJzcmMiLCJhbHQiLCJyZXBsYWNlIiwiY29sU3BhbiIsInRleHRhcmVhIiwicm93cyIsImNoZWNrZWQiLCJodG1sRm9yIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nclass ApiService {\n    async fetchApi(endpoint) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint));\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (!data.success) {\n                throw new Error(data.message || 'API request failed');\n            }\n            return data.data;\n        } catch (error) {\n            console.error(\"API Error for \".concat(endpoint, \":\"), error);\n            throw error;\n        }\n    }\n    async getVehicles(city, category) {\n        const params = new URLSearchParams();\n        if (city) params.append('city', city);\n        if (category) params.append('category', category);\n        const queryString = params.toString();\n        const endpoint = \"/vehicles\".concat(queryString ? \"?\".concat(queryString) : '');\n        return this.fetchApi(endpoint);\n    }\n    async getVehicle(id) {\n        return this.fetchApi(\"/vehicles/\".concat(id));\n    }\n    async getCities() {\n        return this.fetchApi('/cities');\n    }\n    async getCity(slug) {\n        return this.fetchApi(\"/cities/\".concat(slug));\n    }\n    async getVehicleCategories(city) {\n        return this.fetchApi(\"/vehicles/categories/\".concat(city));\n    }\n    async submitBooking(bookingData) {\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/booking\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(bookingData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.message || 'Booking submission failed');\n            }\n            return data;\n        } catch (error) {\n            console.error('Booking submission error:', error);\n            throw error;\n        }\n    }\n}\nconst apiService = new ApiService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9hcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxNQUFNQSxlQUFlQywyQkFBK0IsSUFBSSxDQUEyQjtBQXNDbkYsTUFBTUc7SUFDSixNQUFjQyxTQUFZQyxRQUFnQixFQUFjO1FBQ3RELElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU0sR0FBa0JGLE9BQWZOLGNBQXdCLE9BQVRNO1lBRS9DLElBQUksQ0FBQ0MsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0sdUJBQXVDLE9BQWhCSCxTQUFTSSxNQUFNO1lBQ3hEO1lBRUEsTUFBTUMsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBRWhDLElBQUksQ0FBQ0QsS0FBS0UsT0FBTyxFQUFFO2dCQUNqQixNQUFNLElBQUlKLE1BQU1FLEtBQUtHLE9BQU8sSUFBSTtZQUNsQztZQUVBLE9BQU9ILEtBQUtBLElBQUk7UUFDbEIsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBMEIsT0FBVFYsVUFBUyxNQUFJVTtZQUM1QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNRSxZQUFZQyxJQUFhLEVBQUVDLFFBQWlCLEVBQXNCO1FBQ3RFLE1BQU1DLFNBQVMsSUFBSUM7UUFDbkIsSUFBSUgsTUFBTUUsT0FBT0UsTUFBTSxDQUFDLFFBQVFKO1FBQ2hDLElBQUlDLFVBQVVDLE9BQU9FLE1BQU0sQ0FBQyxZQUFZSDtRQUV4QyxNQUFNSSxjQUFjSCxPQUFPSSxRQUFRO1FBQ25DLE1BQU1uQixXQUFXLFlBQWlELE9BQXJDa0IsY0FBYyxJQUFnQixPQUFaQSxlQUFnQjtRQUUvRCxPQUFPLElBQUksQ0FBQ25CLFFBQVEsQ0FBWUM7SUFDbEM7SUFFQSxNQUFNb0IsV0FBV0MsRUFBVSxFQUFvQjtRQUM3QyxPQUFPLElBQUksQ0FBQ3RCLFFBQVEsQ0FBVSxhQUFnQixPQUFIc0I7SUFDN0M7SUFFQSxNQUFNQyxZQUE2QjtRQUNqQyxPQUFPLElBQUksQ0FBQ3ZCLFFBQVEsQ0FBUztJQUMvQjtJQUVBLE1BQU13QixRQUFRQyxJQUFZLEVBQWlCO1FBQ3pDLE9BQU8sSUFBSSxDQUFDekIsUUFBUSxDQUFPLFdBQWdCLE9BQUx5QjtJQUN4QztJQUVBLE1BQU1DLHFCQUFxQlosSUFBWSxFQUFxQjtRQUMxRCxPQUFPLElBQUksQ0FBQ2QsUUFBUSxDQUFXLHdCQUE2QixPQUFMYztJQUN6RDtJQUVBLE1BQU1hLGNBQWNDLFdBQWdCLEVBQWdCO1FBQ2xELElBQUk7WUFDRixNQUFNMUIsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWJSLGNBQWEsYUFBVztnQkFDdERrQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ0w7WUFDdkI7WUFFQSxNQUFNckIsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBRWhDLElBQUksQ0FBQ04sU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU1FLEtBQUtHLE9BQU8sSUFBSTtZQUNsQztZQUVBLE9BQU9IO1FBQ1QsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE1BQU1BO1FBQ1I7SUFDRjtBQUNGO0FBRU8sTUFBTXVCLGFBQWEsSUFBSW5DLGFBQWE7QUFDM0MsaUVBQWVtQyxVQUFVQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvdGFoYWZhcm9vcXVpL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL0dlby1UcmF2ZWxzL2Zyb250ZW5kL3NyYy9zZXJ2aWNlcy9hcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo1MDAwL2FwaSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVmVoaWNsZSB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgY2F0ZWdvcnk6ICdIRUxJQ09QVEVSJyB8ICdQUklWQVRFX0pFVCcgfCAnQlVTJyB8ICdQUklWQVRFX0NBUic7XG4gIGNhcGFjaXR5OiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGZlYXR1cmVzOiBzdHJpbmdbXTtcbiAgaW1hZ2U6IHN0cmluZztcbiAgcHJpY2VSYW5nZTogc3RyaW5nO1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIHVwZGF0ZWRBdDogc3RyaW5nO1xuICBjaXR5OiB7XG4gICAgaWQ6IHN0cmluZztcbiAgICBuYW1lOiBzdHJpbmc7XG4gICAgc2x1Zzogc3RyaW5nO1xuICB9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENpdHkge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIHNsdWc6IHN0cmluZztcbiAgY291bnRyeTogc3RyaW5nO1xuICBfY291bnQ6IHtcbiAgICB2ZWhpY2xlczogbnVtYmVyO1xuICB9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFwaVJlc3BvbnNlPFQ+IHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgZGF0YTogVDtcbiAgbWVzc2FnZT86IHN0cmluZztcbiAgY291bnQ/OiBudW1iZXI7XG59XG5cbmNsYXNzIEFwaVNlcnZpY2Uge1xuICBwcml2YXRlIGFzeW5jIGZldGNoQXBpPFQ+KGVuZHBvaW50OiBzdHJpbmcpOiBQcm9taXNlPFQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9JHtlbmRwb2ludH1gKTtcbiAgICAgIFxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIGlmICghZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihkYXRhLm1lc3NhZ2UgfHwgJ0FQSSByZXF1ZXN0IGZhaWxlZCcpO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gZGF0YS5kYXRhO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBBUEkgRXJyb3IgZm9yICR7ZW5kcG9pbnR9OmAsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGdldFZlaGljbGVzKGNpdHk/OiBzdHJpbmcsIGNhdGVnb3J5Pzogc3RyaW5nKTogUHJvbWlzZTxWZWhpY2xlW10+IHtcbiAgICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKGNpdHkpIHBhcmFtcy5hcHBlbmQoJ2NpdHknLCBjaXR5KTtcbiAgICBpZiAoY2F0ZWdvcnkpIHBhcmFtcy5hcHBlbmQoJ2NhdGVnb3J5JywgY2F0ZWdvcnkpO1xuICAgIFxuICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zLnRvU3RyaW5nKCk7XG4gICAgY29uc3QgZW5kcG9pbnQgPSBgL3ZlaGljbGVzJHtxdWVyeVN0cmluZyA/IGA/JHtxdWVyeVN0cmluZ31gIDogJyd9YDtcbiAgICBcbiAgICByZXR1cm4gdGhpcy5mZXRjaEFwaTxWZWhpY2xlW10+KGVuZHBvaW50KTtcbiAgfVxuXG4gIGFzeW5jIGdldFZlaGljbGUoaWQ6IHN0cmluZyk6IFByb21pc2U8VmVoaWNsZT4ge1xuICAgIHJldHVybiB0aGlzLmZldGNoQXBpPFZlaGljbGU+KGAvdmVoaWNsZXMvJHtpZH1gKTtcbiAgfVxuXG4gIGFzeW5jIGdldENpdGllcygpOiBQcm9taXNlPENpdHlbXT4ge1xuICAgIHJldHVybiB0aGlzLmZldGNoQXBpPENpdHlbXT4oJy9jaXRpZXMnKTtcbiAgfVxuXG4gIGFzeW5jIGdldENpdHkoc2x1Zzogc3RyaW5nKTogUHJvbWlzZTxDaXR5PiB7XG4gICAgcmV0dXJuIHRoaXMuZmV0Y2hBcGk8Q2l0eT4oYC9jaXRpZXMvJHtzbHVnfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0VmVoaWNsZUNhdGVnb3JpZXMoY2l0eTogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xuICAgIHJldHVybiB0aGlzLmZldGNoQXBpPHN0cmluZ1tdPihgL3ZlaGljbGVzL2NhdGVnb3JpZXMvJHtjaXR5fWApO1xuICB9XG5cbiAgYXN5bmMgc3VibWl0Qm9va2luZyhib29raW5nRGF0YTogYW55KTogUHJvbWlzZTxhbnk+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L2Jvb2tpbmdgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoYm9va2luZ0RhdGEpLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGRhdGEubWVzc2FnZSB8fCAnQm9va2luZyBzdWJtaXNzaW9uIGZhaWxlZCcpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gZGF0YTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQm9va2luZyBzdWJtaXNzaW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgY29uc3QgYXBpU2VydmljZSA9IG5ldyBBcGlTZXJ2aWNlKCk7XG5leHBvcnQgZGVmYXVsdCBhcGlTZXJ2aWNlO1xuIl0sIm5hbWVzIjpbIkFQSV9CQVNFX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiQXBpU2VydmljZSIsImZldGNoQXBpIiwiZW5kcG9pbnQiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJFcnJvciIsInN0YXR1cyIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJlcnJvciIsImNvbnNvbGUiLCJnZXRWZWhpY2xlcyIsImNpdHkiLCJjYXRlZ29yeSIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsImFwcGVuZCIsInF1ZXJ5U3RyaW5nIiwidG9TdHJpbmciLCJnZXRWZWhpY2xlIiwiaWQiLCJnZXRDaXRpZXMiLCJnZXRDaXR5Iiwic2x1ZyIsImdldFZlaGljbGVDYXRlZ29yaWVzIiwic3VibWl0Qm9va2luZyIsImJvb2tpbmdEYXRhIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYXBpU2VydmljZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ftahafarooqui%2FDocuments%2Faugment-projects%2FGeo-Travels%2Ffrontend%2Fsrc%2Fapp%2Fadmin%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);