import { Router } from 'express';
import { query, validationResult } from 'express-validator';
import prisma from '../services/database';

const router = Router();

// GET /api/vehicles - Get all vehicles with optional city filter
router.get('/', [
  query('city').optional().isString().trim(),
  query('category').optional().isIn(['HELICOPTER', 'PRIVATE_JET', 'BUS', 'PRIVATE_CAR']),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { city, category } = req.query;

    // Build where clause
    const where: any = {
      isActive: true,
    };

    if (city) {
      where.city = {
        slug: city.toString().toLowerCase()
      };
    }

    if (category) {
      where.category = category;
    }

    const vehicles = await prisma.vehicle.findMany({
      where,
      include: {
        city: {
          select: {
            id: true,
            name: true,
            slug: true,
          }
        }
      },
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    });

    res.json({
      success: true,
      data: vehicles,
      count: vehicles.length
    });

  } catch (error) {
    console.error('Error fetching vehicles:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/vehicles/:id - Get single vehicle by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const vehicle = await prisma.vehicle.findUnique({
      where: { id },
      include: {
        city: {
          select: {
            id: true,
            name: true,
            slug: true,
          }
        }
      }
    });

    if (!vehicle) {
      return res.status(404).json({
        success: false,
        message: 'Vehicle not found'
      });
    }

    res.json({
      success: true,
      data: vehicle
    });

  } catch (error) {
    console.error('Error fetching vehicle:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// GET /api/vehicles/categories/:city - Get available vehicle categories for a city
router.get('/categories/:city', async (req, res) => {
  try {
    const { city } = req.params;

    const categories = await prisma.vehicle.findMany({
      where: {
        isActive: true,
        city: {
          slug: city.toLowerCase()
        }
      },
      select: {
        category: true
      },
      distinct: ['category']
    });

    const categoryList = categories.map(c => c.category);

    res.json({
      success: true,
      data: categoryList
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

export default router;
