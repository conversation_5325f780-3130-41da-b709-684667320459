// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model City {
  id        String   @id @default(cuid())
  name      String   @unique
  slug      String   @unique
  country   String   @default("UK")
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  vehicles        Vehicle[]
  bookingRequests BookingRequest[]

  @@map("cities")
}

model Vehicle {
  id          String      @id @default(cuid())
  name        String
  category    VehicleType
  capacity    String
  description String
  features    String[]
  image       String
  priceRange  String
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  cityId String
  city   City   @relation(fields: [cityId], references: [id], onDelete: Cascade)

  bookingRequests BookingRequest[]

  @@map("vehicles")
}

model BookingRequest {
  id              String        @id @default(cuid())
  firstName       String
  lastName        String
  email           String
  phone           String
  transportMode   VehicleType
  pickupLocation  String
  dropoffLocation String
  pickupDate      DateTime
  pickupTime      String
  passengers      String
  specialRequests String?
  status          BookingStatus @default(PENDING)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  cityId    String
  city      City     @relation(fields: [cityId], references: [id], onDelete: Cascade)
  vehicleId String?
  vehicle   Vehicle? @relation(fields: [vehicleId], references: [id], onDelete: SetNull)

  @@map("booking_requests")
}

enum VehicleType {
  HELICOPTER
  PRIVATE_JET
  BUS
  PRIVATE_CAR
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}
