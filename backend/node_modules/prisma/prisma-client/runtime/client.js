"use strict";var Nu=Object.create;var zr=Object.defineProperty;var Mu=Object.getOwnPropertyDescriptor;var Fu=Object.getOwnPropertyNames;var Lu=Object.getPrototypeOf,$u=Object.prototype.hasOwnProperty;var Jo=(e,t)=>()=>(e&&(t=e(e=0)),t);var G=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),ut=(e,t)=>{for(var r in t)zr(e,r,{get:t[r],enumerable:!0})},Ko=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Fu(t))!$u.call(e,i)&&i!==r&&zr(e,i,{get:()=>t[i],enumerable:!(n=Mu(t,i))||n.enumerable});return e};var j=(e,t,r)=>(r=e!=null?Nu(Lu(e)):{},Ko(t||!e||!e.__esModule?zr(r,"default",{value:e,enumerable:!0}):r,e)),Vu=e=>Ko(zr({},"__esModule",{value:!0}),e);var cs=G((vh,ap)=>{ap.exports={name:"@prisma/internals",version:"6.12.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.5","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc","@prisma/schema-engine-wasm":"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var yi=G((Ch,pp)=>{pp.exports={name:"@prisma/engines-version",version:"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"8047c96bbd92db98a2abc7c9323ce77c02c89dbc"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var us=G(Zr=>{"use strict";Object.defineProperty(Zr,"__esModule",{value:!0});Zr.enginesVersion=void 0;Zr.enginesVersion=yi().prisma.enginesVersion});var ds=G((Rh,ps)=>{"use strict";ps.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var hs=G((Oh,gs)=>{"use strict";gs.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var Es=G((_h,ws)=>{"use strict";ws.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var bi=G((Nh,xs)=>{"use strict";var hp=Es();xs.exports=e=>typeof e=="string"?e.replace(hp(),""):e});var bs=G((Lh,yp)=>{yp.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var Cs=G(($h,ke)=>{"use strict";var vi=require("node:fs"),Ti=require("node:path"),wp=require("node:os"),Ep=require("node:crypto"),xp=bs(),vs=xp.version,bp=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function Pp(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=bp.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function vp(e){let t=As(e),r=Q.configDotenv({path:t});if(!r.parsed){let s=new Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw s.code="MISSING_DATA",s}let n=Ts(e).split(","),i=n.length,o;for(let s=0;s<i;s++)try{let a=n[s].trim(),l=Ap(r,a);o=Q.decrypt(l.ciphertext,l.key);break}catch(a){if(s+1>=i)throw a}return Q.parse(o)}function Tp(e){console.log(`[dotenv@${vs}][WARN] ${e}`)}function Zt(e){console.log(`[dotenv@${vs}][DEBUG] ${e}`)}function Ts(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function Ap(e,t){let r;try{r=new URL(t)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let n=r.password;if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let i=r.searchParams.get("environment");if(!i){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:s,key:n}}function As(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)vi.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=Ti.resolve(process.cwd(),".env.vault");return vi.existsSync(t)?t:null}function Ps(e){return e[0]==="~"?Ti.join(wp.homedir(),e.slice(1)):e}function Cp(e){!!(e&&e.debug)&&Zt("Loading env from encrypted .env.vault");let r=Q._parseVault(e),n=process.env;return e&&e.processEnv!=null&&(n=e.processEnv),Q.populate(n,r,e),{parsed:r}}function Sp(e){let t=Ti.resolve(process.cwd(),".env"),r="utf8",n=!!(e&&e.debug);e&&e.encoding?r=e.encoding:n&&Zt("No encoding is specified. UTF-8 is used by default");let i=[t];if(e&&e.path)if(!Array.isArray(e.path))i=[Ps(e.path)];else{i=[];for(let l of e.path)i.push(Ps(l))}let o,s={};for(let l of i)try{let c=Q.parse(vi.readFileSync(l,{encoding:r}));Q.populate(s,c,e)}catch(c){n&&Zt(`Failed to load ${l} ${c.message}`),o=c}let a=process.env;return e&&e.processEnv!=null&&(a=e.processEnv),Q.populate(a,s,e),o?{parsed:s,error:o}:{parsed:s}}function Rp(e){if(Ts(e).length===0)return Q.configDotenv(e);let t=As(e);return t?Q._configVault(e):(Tp(`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`),Q.configDotenv(e))}function Ip(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let s=Ep.createDecipheriv("aes-256-gcm",r,i);return s.setAuthTag(o),`${s.update(n)}${s.final()}`}catch(s){let a=s instanceof RangeError,l=s.message==="Invalid key length",c=s.message==="Unsupported state or unable to authenticate data";if(a||l){let u=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw u.code="INVALID_DOTENV_KEY",u}else if(c){let u=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw u.code="DECRYPTION_FAILED",u}else throw s}}function kp(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if(typeof t!="object"){let o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(let o of Object.keys(t))Object.prototype.hasOwnProperty.call(e,o)?(i===!0&&(e[o]=t[o]),n&&Zt(i===!0?`"${o}" is already defined and WAS overwritten`:`"${o}" is already defined and was NOT overwritten`)):e[o]=t[o]}var Q={configDotenv:Sp,_configVault:Cp,_parseVault:vp,config:Rp,decrypt:Ip,parse:Pp,populate:kp};ke.exports.configDotenv=Q.configDotenv;ke.exports._configVault=Q._configVault;ke.exports._parseVault=Q._parseVault;ke.exports.config=Q.config;ke.exports.decrypt=Q.decrypt;ke.exports.parse=Q.parse;ke.exports.populate=Q.populate;ke.exports=Q});var ks=G((Qh,rn)=>{"use strict";rn.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};rn.exports.default=rn.exports});var Mi=G((Ew,ea)=>{"use strict";ea.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,c,u,p,d,m,g,h,C,A,S,w,k=[];for(l=0;l<i;l++)k.push(l+1),k.push(t.charCodeAt(s+l));for(var me=k.length-1;a<o-3;)for(C=r.charCodeAt(s+(c=a)),A=r.charCodeAt(s+(u=a+1)),S=r.charCodeAt(s+(p=a+2)),w=r.charCodeAt(s+(d=a+3)),m=a+=4,l=0;l<me;l+=2)g=k[l],h=k[l+1],c=e(g,c,u,C,h),u=e(c,u,p,A,h),p=e(u,p,d,S,h),m=e(p,d,m,w,h),k[l]=m,d=p,p=u,u=c,c=g;for(;a<o;)for(C=r.charCodeAt(s+(c=a)),m=++a,l=0;l<me;l+=2)g=k[l],k[l]=m=e(g,c,m,C,k[l+1]),c=g;return m}}()});var oa=Jo(()=>{"use strict"});var sa=Jo(()=>{"use strict"});var ao=G(et=>{"use strict";Object.defineProperty(et,"__esModule",{value:!0});et.anumber=so;et.abytes=Sl;et.ahash=Af;et.aexists=Cf;et.aoutput=Sf;function so(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function Tf(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Sl(e,...t){if(!Tf(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Af(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");so(e.outputLen),so(e.blockLen)}function Cf(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function Sf(e,t){Sl(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var Kl=G(P=>{"use strict";Object.defineProperty(P,"__esModule",{value:!0});P.add5L=P.add5H=P.add4H=P.add4L=P.add3H=P.add3L=P.rotlBL=P.rotlBH=P.rotlSL=P.rotlSH=P.rotr32L=P.rotr32H=P.rotrBL=P.rotrBH=P.rotrSL=P.rotrSH=P.shrSL=P.shrSH=P.toBig=void 0;P.fromBig=co;P.split=Rl;P.add=jl;var Vn=BigInt(2**32-1),lo=BigInt(32);function co(e,t=!1){return t?{h:Number(e&Vn),l:Number(e>>lo&Vn)}:{h:Number(e>>lo&Vn)|0,l:Number(e&Vn)|0}}function Rl(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=co(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var Il=(e,t)=>BigInt(e>>>0)<<lo|BigInt(t>>>0);P.toBig=Il;var kl=(e,t,r)=>e>>>r;P.shrSH=kl;var Ol=(e,t,r)=>e<<32-r|t>>>r;P.shrSL=Ol;var Dl=(e,t,r)=>e>>>r|t<<32-r;P.rotrSH=Dl;var _l=(e,t,r)=>e<<32-r|t>>>r;P.rotrSL=_l;var Nl=(e,t,r)=>e<<64-r|t>>>r-32;P.rotrBH=Nl;var Ml=(e,t,r)=>e>>>r-32|t<<64-r;P.rotrBL=Ml;var Fl=(e,t)=>t;P.rotr32H=Fl;var Ll=(e,t)=>e;P.rotr32L=Ll;var $l=(e,t,r)=>e<<r|t>>>32-r;P.rotlSH=$l;var Vl=(e,t,r)=>t<<r|e>>>32-r;P.rotlSL=Vl;var ql=(e,t,r)=>t<<r-32|e>>>64-r;P.rotlBH=ql;var Ul=(e,t,r)=>e<<r-32|t>>>64-r;P.rotlBL=Ul;function jl(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var Bl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);P.add3L=Bl;var Ql=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;P.add3H=Ql;var Hl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);P.add4L=Hl;var Gl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;P.add4H=Gl;var Wl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);P.add5L=Wl;var Jl=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;P.add5H=Jl;var Rf={fromBig:co,split:Rl,toBig:Il,shrSH:kl,shrSL:Ol,rotrSH:Dl,rotrSL:_l,rotrBH:Nl,rotrBL:Ml,rotr32H:Fl,rotr32L:Ll,rotlSH:$l,rotlSL:Vl,rotlBH:ql,rotlBL:Ul,add:jl,add3L:Bl,add3H:Ql,add4L:Hl,add4H:Gl,add5H:Jl,add5L:Wl};P.default=Rf});var zl=G(qn=>{"use strict";Object.defineProperty(qn,"__esModule",{value:!0});qn.crypto=void 0;var Qe=require("node:crypto");qn.crypto=Qe&&typeof Qe=="object"&&"webcrypto"in Qe?Qe.webcrypto:Qe&&typeof Qe=="object"&&"randomBytes"in Qe?Qe:void 0});var Xl=G(I=>{"use strict";Object.defineProperty(I,"__esModule",{value:!0});I.Hash=I.nextTick=I.byteSwapIfBE=I.isLE=void 0;I.isBytes=If;I.u8=kf;I.u32=Of;I.createView=Df;I.rotr=_f;I.rotl=Nf;I.byteSwap=mo;I.byteSwap32=Mf;I.bytesToHex=Lf;I.hexToBytes=$f;I.asyncLoop=qf;I.utf8ToBytes=Zl;I.toBytes=Un;I.concatBytes=Uf;I.checkOpts=jf;I.wrapConstructor=Bf;I.wrapConstructorWithOpts=Qf;I.wrapXOFConstructorWithOpts=Hf;I.randomBytes=Gf;var kt=zl(),po=ao();function If(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function kf(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Of(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Df(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function _f(e,t){return e<<32-t|e>>>t}function Nf(e,t){return e<<t|e>>>32-t>>>0}I.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function mo(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}I.byteSwapIfBE=I.isLE?e=>e:e=>mo(e);function Mf(e){for(let t=0;t<e.length;t++)e[t]=mo(e[t])}var Ff=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Lf(e){(0,po.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=Ff[e[r]];return t}var _e={_0:48,_9:57,A:65,F:70,a:97,f:102};function Yl(e){if(e>=_e._0&&e<=_e._9)return e-_e._0;if(e>=_e.A&&e<=_e.F)return e-(_e.A-10);if(e>=_e.a&&e<=_e.f)return e-(_e.a-10)}function $f(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=Yl(e.charCodeAt(o)),a=Yl(e.charCodeAt(o+1));if(s===void 0||a===void 0){let l=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+l+'" at index '+o)}n[i]=s*16+a}return n}var Vf=async()=>{};I.nextTick=Vf;async function qf(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,I.nextTick)(),n+=o)}}function Zl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Un(e){return typeof e=="string"&&(e=Zl(e)),(0,po.abytes)(e),e}function Uf(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,po.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var uo=class{clone(){return this._cloneInto()}};I.Hash=uo;function jf(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function Bf(e){let t=n=>e().update(Un(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function Qf(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Hf(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Gf(e=32){if(kt.crypto&&typeof kt.crypto.getRandomValues=="function")return kt.crypto.getRandomValues(new Uint8Array(e));if(kt.crypto&&typeof kt.crypto.randomBytes=="function")return kt.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var ac=G($=>{"use strict";Object.defineProperty($,"__esModule",{value:!0});$.shake256=$.shake128=$.keccak_512=$.keccak_384=$.keccak_256=$.keccak_224=$.sha3_512=$.sha3_384=$.sha3_256=$.sha3_224=$.Keccak=void 0;$.keccakP=oc;var Ot=ao(),vr=Kl(),Ne=Xl(),rc=[],nc=[],ic=[],Wf=BigInt(0),Pr=BigInt(1),Jf=BigInt(2),Kf=BigInt(7),zf=BigInt(256),Yf=BigInt(113);for(let e=0,t=Pr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],rc.push(2*(5*n+r)),nc.push((e+1)*(e+2)/2%64);let i=Wf;for(let o=0;o<7;o++)t=(t<<Pr^(t>>Kf)*Yf)%zf,t&Jf&&(i^=Pr<<(Pr<<BigInt(o))-Pr);ic.push(i)}var[Zf,Xf]=(0,vr.split)(ic,!0),ec=(e,t,r)=>r>32?(0,vr.rotlBH)(e,t,r):(0,vr.rotlSH)(e,t,r),tc=(e,t,r)=>r>32?(0,vr.rotlBL)(e,t,r):(0,vr.rotlSL)(e,t,r);function oc(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,l=(s+2)%10,c=r[l],u=r[l+1],p=ec(c,u,1)^r[a],d=tc(c,u,1)^r[a+1];for(let m=0;m<50;m+=10)e[s+m]^=p,e[s+m+1]^=d}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=nc[s],l=ec(i,o,a),c=tc(i,o,a),u=rc[s];i=e[u],o=e[u+1],e[u]=l,e[u+1]=c}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=Zf[n],e[1]^=Xf[n]}r.fill(0)}var Tr=class e extends Ne.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Ot.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Ne.u32)(this.state)}keccak(){Ne.isLE||(0,Ne.byteSwap32)(this.state32),oc(this.state32,this.rounds),Ne.isLE||(0,Ne.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Ot.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Ne.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,Ot.aexists)(this,!1),(0,Ot.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Ot.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Ot.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};$.Keccak=Tr;var He=(e,t,r)=>(0,Ne.wrapConstructor)(()=>new Tr(t,e,r));$.sha3_224=He(6,144,224/8);$.sha3_256=He(6,136,256/8);$.sha3_384=He(6,104,384/8);$.sha3_512=He(6,72,512/8);$.keccak_224=He(1,144,224/8);$.keccak_256=He(1,136,256/8);$.keccak_384=He(1,104,384/8);$.keccak_512=He(1,72,512/8);var sc=(e,t,r)=>(0,Ne.wrapXOFConstructorWithOpts)((n={})=>new Tr(t,e,n.dkLen===void 0?r:n.dkLen,!0));$.shake128=sc(31,168,128/8);$.shake256=sc(31,136,256/8)});var gc=G((TP,Ge)=>{"use strict";var{sha3_512:eg}=ac(),cc=24,Ar=32,fo=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function uc(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var pc=(e="")=>uc(eg(e)).toString(36).slice(1),lc=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),tg=e=>lc[Math.floor(e()*lc.length)],dc=({globalObj:e=typeof global<"u"?global:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+fo(Ar,t):fo(Ar,t);return pc(n).substring(0,Ar)},mc=e=>()=>e++,rg=476782367,fc=({random:e=Math.random,counter:t=mc(Math.floor(e()*rg)),length:r=cc,fingerprint:n=dc({random:e})}={})=>function(){let o=tg(e),s=Date.now().toString(36),a=t().toString(36),l=fo(r,e),c=`${s+l+a+n}`;return`${o+pc(c).substring(1,r)}`},ng=fc(),ig=(e,{minLength:t=2,maxLength:r=Ar}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};Ge.exports.getConstants=()=>({defaultLength:cc,bigLength:Ar});Ge.exports.init=fc;Ge.exports.createId=ng;Ge.exports.bufToBigInt=uc;Ge.exports.createCounter=mc;Ge.exports.createFingerprint=dc;Ge.exports.isCuid=ig});var hc=G((AP,Cr)=>{"use strict";var{createId:og,init:sg,getConstants:ag,isCuid:lg}=gc();Cr.exports.createId=og;Cr.exports.init=sg;Cr.exports.getConstants=ag;Cr.exports.isCuid=lg});var ph={};ut(ph,{DMMF:()=>nr,Debug:()=>V,Decimal:()=>K,Extensions:()=>di,MetricsClient:()=>Ct,PrismaClientInitializationError:()=>O,PrismaClientKnownRequestError:()=>U,PrismaClientRustPanicError:()=>ne,PrismaClientUnknownRequestError:()=>X,PrismaClientValidationError:()=>ee,Public:()=>mi,Sql:()=>le,createParam:()=>Pa,defineDmmfProperty:()=>Ra,deserializeJsonResponse:()=>qe,deserializeRawResult:()=>li,dmmfToRuntimeDataModel:()=>Js,empty:()=>Oa,getPrismaClient:()=>Ou,getRuntime:()=>ll,join:()=>ka,makeStrictEnum:()=>Du,makeTypedQueryFactory:()=>Ia,objectEnumValues:()=>Pn,raw:()=>Qi,serializeJsonQuery:()=>In,skip:()=>Rn,sqltag:()=>Hi,warnEnvConflicts:()=>_u,warnOnce:()=>er});module.exports=Vu(ph);var di={};ut(di,{defineExtension:()=>zo,getExtensionContext:()=>Yo});function zo(e){return typeof e=="function"?e:t=>t.$extends(e)}function Yo(e){return e}var mi={};ut(mi,{validator:()=>Zo});function Zo(...e){return t=>t}var Yr={};ut(Yr,{$:()=>ns,bgBlack:()=>Ku,bgBlue:()=>Xu,bgCyan:()=>tp,bgGreen:()=>Yu,bgMagenta:()=>ep,bgRed:()=>zu,bgWhite:()=>rp,bgYellow:()=>Zu,black:()=>Hu,blue:()=>ze,bold:()=>re,cyan:()=>Ie,dim:()=>Je,gray:()=>Gt,green:()=>Ht,grey:()=>Ju,hidden:()=>Bu,inverse:()=>ju,italic:()=>Uu,magenta:()=>Gu,red:()=>Re,reset:()=>qu,strikethrough:()=>Qu,underline:()=>se,white:()=>Wu,yellow:()=>Ke});var fi,Xo,es,ts,rs=!0;typeof process<"u"&&({FORCE_COLOR:fi,NODE_DISABLE_COLORS:Xo,NO_COLOR:es,TERM:ts}=process.env||{},rs=process.stdout&&process.stdout.isTTY);var ns={enabled:!Xo&&es==null&&ts!=="dumb"&&(fi!=null&&fi!=="0"||rs)};function M(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!ns.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var qu=M(0,0),re=M(1,22),Je=M(2,22),Uu=M(3,23),se=M(4,24),ju=M(7,27),Bu=M(8,28),Qu=M(9,29),Hu=M(30,39),Re=M(31,39),Ht=M(32,39),Ke=M(33,39),ze=M(34,39),Gu=M(35,39),Ie=M(36,39),Wu=M(37,39),Gt=M(90,39),Ju=M(90,39),Ku=M(40,49),zu=M(41,49),Yu=M(42,49),Zu=M(43,49),Xu=M(44,49),ep=M(45,49),tp=M(46,49),rp=M(47,49);var np=100,is=["green","yellow","blue","magenta","cyan","red"],Wt=[],os=Date.now(),ip=0,gi=typeof process<"u"?process.env:{};globalThis.DEBUG??=gi.DEBUG??"";globalThis.DEBUG_COLORS??=gi.DEBUG_COLORS?gi.DEBUG_COLORS==="true":!0;var Jt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function op(e){let t={color:is[ip++%is.length],enabled:Jt.enabled(e),namespace:e,log:Jt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Wt.push([o,...n]),Wt.length>np&&Wt.shift(),Jt.enabled(o)||i){let l=n.map(u=>typeof u=="string"?u:sp(u)),c=`+${Date.now()-os}ms`;os=Date.now(),globalThis.DEBUG_COLORS?a(Yr[s](re(o)),...l,Yr[s](c)):a(o,...l,c)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var V=new Proxy(op,{get:(e,t)=>Jt[t],set:(e,t,r)=>Jt[t]=r});function sp(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function ss(e=7500){let t=Wt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function as(){Wt.length=0}var ls=V;var lp=cs(),hi=lp.version;function pt(e){let t=cp();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":up(e))}function cp(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function up(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}var ms=j(ds(),1);function wi(e){let t=(0,ms.default)(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}var fs="prisma+postgres",Xr=`${fs}:`;function en(e){return e?.toString().startsWith(`${Xr}//`)??!1}function Ei(e){if(!en(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")||t.includes("[::1]")}var zt={};ut(zt,{error:()=>fp,info:()=>mp,log:()=>dp,query:()=>gp,should:()=>ys,tags:()=>Kt,warn:()=>xi});var Kt={error:Re("prisma:error"),warn:Ke("prisma:warn"),info:Ie("prisma:info"),query:ze("prisma:query")},ys={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function dp(...e){console.log(...e)}function xi(e,...t){ys.warn()&&console.warn(`${Kt.warn} ${e}`,...t)}function mp(e,...t){console.info(`${Kt.info} ${e}`,...t)}function fp(e,...t){console.error(`${Kt.error} ${e}`,...t)}function gp(e,...t){console.log(`${Kt.query} ${e}`,...t)}function fe(e,t){throw new Error(t)}var Yt=j(require("node:path"));function Pi(e){return Yt.default.sep===Yt.default.posix.sep?e:e.split(Yt.default.sep).join(Yt.default.posix.sep)}var Ci=j(Cs()),tn=j(require("node:fs"));var dt=j(require("node:path"));function Ss(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],c,u;if(l==="\\")u=a[0],c=u.replace("\\$","$");else{let p=a[2];u=a[0].substring(l.length),c=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",c=r(c)}return o.replace(u,c)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var Ai=ls("prisma:tryLoadEnv");function Xt({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=Rs(e);r.conflictCheck!=="none"&&Op(n,t,r.conflictCheck);let i=null;return Is(n?.path,t)||(i=Rs(t)),!n&&!i&&Ai("No Environment variables loaded"),i?.dotenvResult.error?console.error(Re(re("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function Op(e,t,r){let n=e?.dotenvResult.parsed,i=!Is(e?.path,t);if(n&&t&&i&&tn.default.existsSync(t)){let o=Ci.default.parse(tn.default.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=dt.default.relative(process.cwd(),e.path),l=dt.default.relative(process.cwd(),t);if(r==="error"){let c=`There is a conflict between env var${s.length>1?"s":""} in ${se(a)} and ${se(l)}
Conflicting env vars:
${s.map(u=>`  ${re(u)}`).join(`
`)}

We suggest to move the contents of ${se(l)} to ${se(a)} to consolidate your env vars.
`;throw new Error(c)}else if(r==="warn"){let c=`Conflict for env var${s.length>1?"s":""} ${s.map(u=>re(u)).join(", ")} in ${se(a)} and ${se(l)}
Env vars from ${se(l)} overwrite the ones from ${se(a)}
      `;console.warn(`${Ke("warn(prisma)")} ${c}`)}}}}function Rs(e){if(Dp(e)){Ai(`Environment variables loaded from ${e}`);let t=Ci.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:Ss(t),message:Je(`Environment variables loaded from ${dt.default.relative(process.cwd(),e)}`),path:e}}else Ai(`Environment variables not found at ${e}`);return null}function Is(e,t){return e&&t&&dt.default.resolve(e)===dt.default.resolve(t)}function Dp(e){return!!(e&&tn.default.existsSync(e))}function Si(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function mt(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function Ri(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function b(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Os=new Set,er=(e,t,...r)=>{Os.has(e)||(Os.add(e),xi(t,...r))};var O=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};b(O,"PrismaClientInitializationError");var U=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};b(U,"PrismaClientKnownRequestError");var ne=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};b(ne,"PrismaClientRustPanicError");var X=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};b(X,"PrismaClientUnknownRequestError");var ee=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};b(ee,"PrismaClientValidationError");var ft=9e15,Ve=1e9,Ii="0123456789abcdef",sn="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",an="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",ki={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-ft,maxE:ft,crypto:!1},Fs,Oe,x=!0,cn="[DecimalError] ",$e=cn+"Invalid argument: ",Ls=cn+"Precision limit exceeded",$s=cn+"crypto unavailable",Vs="[object Decimal]",te=Math.floor,H=Math.pow,_p=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,Np=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,Mp=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,qs=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ge=1e7,E=7,Fp=9007199254740991,Lp=sn.length-1,Oi=an.length-1,f={toStringTag:Vs};f.absoluteValue=f.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),y(e)};f.ceil=function(){return y(new this.constructor(this),this.e+1,2)};f.clampedTo=f.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error($e+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};f.comparedTo=f.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,c=e.s;if(!s||!a)return!l||!c?NaN:l!==c?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-c:0;if(l!==c)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};f.cosine=f.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=$p(n,Hs(n,r)),n.precision=e,n.rounding=t,y(Oe==2||Oe==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};f.cubeRoot=f.cbrt=function(){var e,t,r,n,i,o,s,a,l,c,u=this,p=u.constructor;if(!u.isFinite()||u.isZero())return new p(u);for(x=!1,o=u.s*H(u.s*u,1/3),!o||Math.abs(o)==1/0?(r=J(u.d),e=u.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=H(r,1/3),e=te((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=u.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),c=l.plus(u),n=N(c.plus(u).times(a),c.plus(l),s+2,1),J(a.d).slice(0,s)===(r=J(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(y(a,e+1,0),a.times(a).times(a).eq(u))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(y(n,e+1,1),t=!n.times(n).times(n).eq(u));break}return x=!0,y(n,e,p.rounding,t)};f.decimalPlaces=f.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-te(this.e/E))*E,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};f.dividedBy=f.div=function(e){return N(this,new this.constructor(e))};f.dividedToIntegerBy=f.divToInt=function(e){var t=this,r=t.constructor;return y(N(t,new r(e),0,1,1),r.precision,r.rounding)};f.equals=f.eq=function(e){return this.cmp(e)===0};f.floor=function(){return y(new this.constructor(this),this.e+1,3)};f.greaterThan=f.gt=function(e){return this.cmp(e)>0};f.greaterThanOrEqualTo=f.gte=function(e){var t=this.cmp(e);return t==1||t===0};f.hyperbolicCosine=f.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/pn(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=gt(s,1,o.times(t),new s(1),!0);for(var l,c=e,u=new s(8);c--;)l=o.times(o),o=a.minus(l.times(u.minus(l.times(u))));return y(o,s.precision=r,s.rounding=n,!0)};f.hyperbolicSine=f.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=gt(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/pn(5,e)),i=gt(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),c=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(c))))}return o.precision=t,o.rounding=r,y(i,t,r,!0)};f.hyperbolicTangent=f.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,N(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};f.inverseCosine=f.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return r!==-1?r===0?e.isNeg()?Ee(t,n,i):new t(0):new t(NaN):e.isZero()?Ee(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))};f.inverseHyperbolicCosine=f.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,x=!1,r=r.times(r).minus(1).sqrt().plus(r),x=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};f.inverseHyperbolicSine=f.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,x=!1,r=r.times(r).plus(1).sqrt().plus(r),x=!0,n.precision=e,n.rounding=t,r.ln())};f.inverseHyperbolicTangent=f.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?y(new o(i),e,t,!0):(o.precision=r=n-i.e,i=N(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};f.inverseSine=f.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=Ee(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};f.inverseTangent=f.atan=function(){var e,t,r,n,i,o,s,a,l,c=this,u=c.constructor,p=u.precision,d=u.rounding;if(c.isFinite()){if(c.isZero())return new u(c);if(c.abs().eq(1)&&p+4<=Oi)return s=Ee(u,p+4,d).times(.25),s.s=c.s,s}else{if(!c.s)return new u(NaN);if(p+4<=Oi)return s=Ee(u,p+4,d).times(.5),s.s=c.s,s}for(u.precision=a=p+10,u.rounding=1,r=Math.min(28,a/E+2|0),e=r;e;--e)c=c.div(c.times(c).plus(1).sqrt().plus(1));for(x=!1,t=Math.ceil(a/E),n=1,l=c.times(c),s=new u(c),i=c;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),x=!0,y(s,u.precision=p,u.rounding=d,!0)};f.isFinite=function(){return!!this.d};f.isInteger=f.isInt=function(){return!!this.d&&te(this.e/E)>this.d.length-2};f.isNaN=function(){return!this.s};f.isNegative=f.isNeg=function(){return this.s<0};f.isPositive=f.isPos=function(){return this.s>0};f.isZero=function(){return!!this.d&&this.d[0]===0};f.lessThan=f.lt=function(e){return this.cmp(e)<0};f.lessThanOrEqualTo=f.lte=function(e){return this.cmp(e)<1};f.logarithm=f.log=function(e){var t,r,n,i,o,s,a,l,c=this,u=c.constructor,p=u.precision,d=u.rounding,m=5;if(e==null)e=new u(10),t=!0;else{if(e=new u(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new u(NaN);t=e.eq(10)}if(r=c.d,c.s<0||!r||!r[0]||c.eq(1))return new u(r&&!r[0]?-1/0:c.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(x=!1,a=p+m,s=Le(c,a),n=t?ln(u,a+10):Le(e,a),l=N(s,n,a,1),tr(l.d,i=p,d))do if(a+=10,s=Le(c,a),n=t?ln(u,a+10):Le(e,a),l=N(s,n,a,1),!o){+J(l.d).slice(i+1,i+15)+1==1e14&&(l=y(l,p+1,0));break}while(tr(l.d,i+=10,d));return x=!0,y(l,p,d)};f.minus=f.sub=function(e){var t,r,n,i,o,s,a,l,c,u,p,d,m=this,g=m.constructor;if(e=new g(e),!m.d||!e.d)return!m.s||!e.s?e=new g(NaN):m.d?e.s=-e.s:e=new g(e.d||m.s!==e.s?m:NaN),e;if(m.s!=e.s)return e.s=-e.s,m.plus(e);if(c=m.d,d=e.d,a=g.precision,l=g.rounding,!c[0]||!d[0]){if(d[0])e.s=-e.s;else if(c[0])e=new g(m);else return new g(l===3?-0:0);return x?y(e,a,l):e}if(r=te(e.e/E),u=te(m.e/E),c=c.slice(),o=u-r,o){for(p=o<0,p?(t=c,o=-o,s=d.length):(t=d,r=u,s=c.length),n=Math.max(Math.ceil(a/E),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=c.length,s=d.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(c[n]!=d[n]){p=c[n]<d[n];break}o=0}for(p&&(t=c,c=d,d=t,e.s=-e.s),s=c.length,n=d.length-s;n>0;--n)c[s++]=0;for(n=d.length;n>o;){if(c[--n]<d[n]){for(i=n;i&&c[--i]===0;)c[i]=ge-1;--c[i],c[n]+=ge}c[n]-=d[n]}for(;c[--s]===0;)c.pop();for(;c[0]===0;c.shift())--r;return c[0]?(e.d=c,e.e=un(c,r),x?y(e,a,l):e):new g(l===3?-0:0)};f.modulo=f.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?y(new n(r),n.precision,n.rounding):(x=!1,n.modulo==9?(t=N(r,e.abs(),0,3,1),t.s*=e.s):t=N(r,e,0,n.modulo,1),t=t.times(e),x=!0,r.minus(t))};f.naturalExponential=f.exp=function(){return Di(this)};f.naturalLogarithm=f.ln=function(){return Le(this)};f.negated=f.neg=function(){var e=new this.constructor(this);return e.s=-e.s,y(e)};f.plus=f.add=function(e){var t,r,n,i,o,s,a,l,c,u,p=this,d=p.constructor;if(e=new d(e),!p.d||!e.d)return!p.s||!e.s?e=new d(NaN):p.d||(e=new d(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(c=p.d,u=e.d,a=d.precision,l=d.rounding,!c[0]||!u[0])return u[0]||(e=new d(p)),x?y(e,a,l):e;if(o=te(p.e/E),n=te(e.e/E),c=c.slice(),i=o-n,i){for(i<0?(r=c,i=-i,s=u.length):(r=u,n=o,s=c.length),o=Math.ceil(a/E),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=c.length,i=u.length,s-i<0&&(i=s,r=u,u=c,c=r),t=0;i;)t=(c[--i]=c[i]+u[i]+t)/ge|0,c[i]%=ge;for(t&&(c.unshift(t),++n),s=c.length;c[--s]==0;)c.pop();return e.d=c,e.e=un(c,n),x?y(e,a,l):e};f.precision=f.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error($e+e);return r.d?(t=Us(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};f.round=function(){var e=this,t=e.constructor;return y(new t(e),e.e+1,t.rounding)};f.sine=f.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+E,n.rounding=1,r=qp(n,Hs(n,r)),n.precision=e,n.rounding=t,y(Oe>2?r.neg():r,e,t,!0)):new n(NaN)};f.squareRoot=f.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,c=s.s,u=s.constructor;if(c!==1||!a||!a[0])return new u(!c||c<0&&(!a||a[0])?NaN:a?s:1/0);for(x=!1,c=Math.sqrt(+s),c==0||c==1/0?(t=J(a),(t.length+l)%2==0&&(t+="0"),c=Math.sqrt(t),l=te((l+1)/2)-(l<0||l%2),c==1/0?t="5e"+l:(t=c.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new u(t)):n=new u(c.toString()),r=(l=u.precision)+3;;)if(o=n,n=o.plus(N(s,o,r+2,1)).times(.5),J(o.d).slice(0,r)===(t=J(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(y(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(y(n,l+1,1),e=!n.times(n).eq(s));break}return x=!0,y(n,l,u.rounding,e)};f.tangent=f.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=N(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,y(Oe==2||Oe==4?r.neg():r,e,t,!0)):new n(NaN)};f.times=f.mul=function(e){var t,r,n,i,o,s,a,l,c,u=this,p=u.constructor,d=u.d,m=(e=new p(e)).d;if(e.s*=u.s,!d||!d[0]||!m||!m[0])return new p(!e.s||d&&!d[0]&&!m||m&&!m[0]&&!d?NaN:!d||!m?e.s/0:e.s*0);for(r=te(u.e/E)+te(e.e/E),l=d.length,c=m.length,l<c&&(o=d,d=m,m=o,s=l,l=c,c=s),o=[],s=l+c,n=s;n--;)o.push(0);for(n=c;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+m[n]*d[i-n-1]+t,o[i--]=a%ge|0,t=a/ge|0;o[i]=(o[i]+t)%ge|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=un(o,r),x?y(e,p.precision,p.rounding):e};f.toBinary=function(e,t){return _i(this,2,e,t)};f.toDecimalPlaces=f.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ae(e,0,Ve),t===void 0?t=n.rounding:ae(t,0,8),y(r,e+r.e+1,t))};f.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=xe(n,!0):(ae(e,0,Ve),t===void 0?t=i.rounding:ae(t,0,8),n=y(new i(n),e+1,t),r=xe(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=xe(i):(ae(e,0,Ve),t===void 0?t=o.rounding:ae(t,0,8),n=y(new o(i),e+i.e+1,t),r=xe(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};f.toFraction=function(e){var t,r,n,i,o,s,a,l,c,u,p,d,m=this,g=m.d,h=m.constructor;if(!g)return new h(m);if(c=r=new h(1),n=l=new h(0),t=new h(n),o=t.e=Us(g)-m.e-1,s=o%E,t.d[0]=H(10,s<0?E+s:s),e==null)e=o>0?t:c;else{if(a=new h(e),!a.isInt()||a.lt(c))throw Error($e+a);e=a.gt(t)?o>0?t:c:a}for(x=!1,a=new h(J(g)),u=h.precision,h.precision=o=g.length*E*2;p=N(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=c,c=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=N(e.minus(r),n,0,1,1),l=l.plus(i.times(c)),r=r.plus(i.times(n)),l.s=c.s=m.s,d=N(c,n,o,1).minus(m).abs().cmp(N(l,r,o,1).minus(m).abs())<1?[c,n]:[l,r],h.precision=u,x=!0,d};f.toHexadecimal=f.toHex=function(e,t){return _i(this,16,e,t)};f.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:ae(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(x=!1,r=N(r,e,0,t,1).times(e),x=!0,y(r)):(e.s=r.s,r=e),r};f.toNumber=function(){return+this};f.toOctal=function(e,t){return _i(this,8,e,t)};f.toPower=f.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,c=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l(H(+a,c));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return y(a,n,o);if(t=te(e.e/E),t>=e.d.length-1&&(r=c<0?-c:c)<=Fp)return i=js(l,a,r,n),e.s<0?new l(1).div(i):y(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if((e.d[t]&1)==0&&(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=H(+a,c),t=r==0||!isFinite(r)?te(c*(Math.log("0."+J(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(x=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=Di(e.times(Le(a,n+r)),n),i.d&&(i=y(i,n+5,1),tr(i.d,n,o)&&(t=n+10,i=y(Di(e.times(Le(a,t+r)),t),t+5,1),+J(i.d).slice(n+1,n+15)+1==1e14&&(i=y(i,n+1,0)))),i.s=s,x=!0,l.rounding=o,y(i,n,o))};f.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=xe(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(ae(e,1,Ve),t===void 0?t=i.rounding:ae(t,0,8),n=y(new i(n),e,t),r=xe(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};f.toSignificantDigits=f.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ae(e,1,Ve),t===void 0?t=n.rounding:ae(t,0,8)),y(new n(r),e,t)};f.toString=function(){var e=this,t=e.constructor,r=xe(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};f.truncated=f.trunc=function(){return y(new this.constructor(this),this.e+1,1)};f.valueOf=f.toJSON=function(){var e=this,t=e.constructor,r=xe(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function J(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=E-n.length,r&&(o+=Fe(r)),o+=n;s=e[t],n=s+"",r=E-n.length,r&&(o+=Fe(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function ae(e,t,r){if(e!==~~e||e<t||e>r)throw Error($e+e)}function tr(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=E,i=0):(i=Math.ceil((t+1)/E),t%=E),o=H(10,E-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==H(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==H(10,t-3)-1,s}function nn(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=Ii.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function $p(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/pn(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=gt(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var N=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var c,u,p,d,m,g,h,C,A,S,w,k,me,ue,Qt,B,oe,Se,Y,ct,Kr=n.constructor,pi=n.s==i.s?1:-1,Z=n.d,_=i.d;if(!Z||!Z[0]||!_||!_[0])return new Kr(!n.s||!i.s||(Z?_&&Z[0]==_[0]:!_)?NaN:Z&&Z[0]==0||!_?pi*0:pi/0);for(l?(m=1,u=n.e-i.e):(l=ge,m=E,u=te(n.e/m)-te(i.e/m)),Y=_.length,oe=Z.length,A=new Kr(pi),S=A.d=[],p=0;_[p]==(Z[p]||0);p++);if(_[p]>(Z[p]||0)&&u--,o==null?(ue=o=Kr.precision,s=Kr.rounding):a?ue=o+(n.e-i.e)+1:ue=o,ue<0)S.push(1),g=!0;else{if(ue=ue/m+2|0,p=0,Y==1){for(d=0,_=_[0],ue++;(p<oe||d)&&ue--;p++)Qt=d*l+(Z[p]||0),S[p]=Qt/_|0,d=Qt%_|0;g=d||p<oe}else{for(d=l/(_[0]+1)|0,d>1&&(_=e(_,d,l),Z=e(Z,d,l),Y=_.length,oe=Z.length),B=Y,w=Z.slice(0,Y),k=w.length;k<Y;)w[k++]=0;ct=_.slice(),ct.unshift(0),Se=_[0],_[1]>=l/2&&++Se;do d=0,c=t(_,w,Y,k),c<0?(me=w[0],Y!=k&&(me=me*l+(w[1]||0)),d=me/Se|0,d>1?(d>=l&&(d=l-1),h=e(_,d,l),C=h.length,k=w.length,c=t(h,w,C,k),c==1&&(d--,r(h,Y<C?ct:_,C,l))):(d==0&&(c=d=1),h=_.slice()),C=h.length,C<k&&h.unshift(0),r(w,h,k,l),c==-1&&(k=w.length,c=t(_,w,Y,k),c<1&&(d++,r(w,Y<k?ct:_,k,l))),k=w.length):c===0&&(d++,w=[0]),S[p++]=d,c&&w[0]?w[k++]=Z[B]||0:(w=[Z[B]],k=1);while((B++<oe||w[0]!==void 0)&&ue--);g=w[0]!==void 0}S[0]||S.shift()}if(m==1)A.e=u,Fs=g;else{for(p=1,d=S[0];d>=10;d/=10)p++;A.e=p+u*m-1,y(A,a?o+A.e+1:o,s,g)}return A}}();function y(e,t,r,n){var i,o,s,a,l,c,u,p,d,m=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=E,s=t,u=p[d=0],l=u/H(10,i-s-1)%10|0;else if(d=Math.ceil((o+1)/E),a=p.length,d>=a)if(n){for(;a++<=d;)p.push(0);u=l=0,i=1,o%=E,s=o-E+1}else break e;else{for(u=a=p[d],i=1;a>=10;a/=10)i++;o%=E,s=o-E+i,l=s<0?0:u/H(10,i-s-1)%10|0}if(n=n||t<0||p[d+1]!==void 0||(s<0?u:u%H(10,i-s-1)),c=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?u/H(10,i-s):0:p[d-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,c?(t-=e.e+1,p[0]=H(10,(E-t%E)%E),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=d,a=1,d--):(p.length=d+1,a=H(10,E-o),p[d]=s>0?(u/H(10,i-s)%H(10,s)|0)*a:0),c)for(;;)if(d==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==ge&&(p[0]=1));break}else{if(p[d]+=a,p[d]!=ge)break;p[d--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return x&&(e.e>m.maxE?(e.d=null,e.e=NaN):e.e<m.minE&&(e.e=0,e.d=[0])),e}function xe(e,t,r){if(!e.isFinite())return Qs(e);var n,i=e.e,o=J(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Fe(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Fe(-i-1)+o,r&&(n=r-s)>0&&(o+=Fe(n))):i>=s?(o+=Fe(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Fe(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Fe(n))),o}function un(e,t){var r=e[0];for(t*=E;r>=10;r/=10)t++;return t}function ln(e,t,r){if(t>Lp)throw x=!0,r&&(e.precision=r),Error(Ls);return y(new e(sn),t,1,!0)}function Ee(e,t,r){if(t>Oi)throw Error(Ls);return y(new e(an),t,r,!0)}function Us(e){var t=e.length-1,r=t*E+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function Fe(e){for(var t="";e--;)t+="0";return t}function js(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/E+4);for(x=!1;;){if(r%2&&(o=o.times(t),Ns(o.d,s)&&(i=!0)),r=te(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),Ns(t.d,s)}return x=!0,o}function _s(e){return e.d[e.d.length-1]&1}function Bs(e,t,r){for(var n,i,o=new e(t[0]),s=0;++s<t.length;){if(i=new e(t[s]),!i.s){o=i;break}n=o.cmp(i),(n===r||n===0&&o.s===r)&&(o=i)}return o}function Di(e,t){var r,n,i,o,s,a,l,c=0,u=0,p=0,d=e.constructor,m=d.rounding,g=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(x=!1,l=g):l=t,a=new d(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log(H(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new d(1),d.precision=l;;){if(o=y(o.times(e),l,1),r=r.times(++u),a=s.plus(N(o,r,l,1)),J(a.d).slice(0,l)===J(s.d).slice(0,l)){for(i=p;i--;)s=y(s.times(s),l,1);if(t==null)if(c<3&&tr(s.d,l-n,m,c))d.precision=l+=10,r=o=a=new d(1),u=0,c++;else return y(s,d.precision=g,m,x=!0);else return d.precision=g,s}s=a}}function Le(e,t){var r,n,i,o,s,a,l,c,u,p,d,m=1,g=10,h=e,C=h.d,A=h.constructor,S=A.rounding,w=A.precision;if(h.s<0||!C||!C[0]||!h.e&&C[0]==1&&C.length==1)return new A(C&&!C[0]?-1/0:h.s!=1?NaN:C?0:h);if(t==null?(x=!1,u=w):u=t,A.precision=u+=g,r=J(C),n=r.charAt(0),Math.abs(o=h.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=J(h.d),n=r.charAt(0),m++;o=h.e,n>1?(h=new A("0."+r),o++):h=new A(n+"."+r.slice(1))}else return c=ln(A,u+2,w).times(o+""),h=Le(new A(n+"."+r.slice(1)),u-g).plus(c),A.precision=w,t==null?y(h,w,S,x=!0):h;for(p=h,l=s=h=N(h.minus(1),h.plus(1),u,1),d=y(h.times(h),u,1),i=3;;){if(s=y(s.times(d),u,1),c=l.plus(N(s,new A(i),u,1)),J(c.d).slice(0,u)===J(l.d).slice(0,u))if(l=l.times(2),o!==0&&(l=l.plus(ln(A,u+2,w).times(o+""))),l=N(l,new A(m),u,1),t==null)if(tr(l.d,u-g,S,a))A.precision=u+=g,c=s=h=N(p.minus(1),p.plus(1),u,1),d=y(h.times(h),u,1),i=a=1;else return y(l,A.precision=w,S,x=!0);else return A.precision=w,l;l=c,i+=2}}function Qs(e){return String(e.s*e.s/0)}function on(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%E,r<0&&(n+=E),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=E;n<i;)e.d.push(+t.slice(n,n+=E));t=t.slice(n),n=E-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),x&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function Vp(e,t){var r,n,i,o,s,a,l,c,u;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),qs.test(t))return on(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(Np.test(t))r=16,t=t.toLowerCase();else if(_p.test(t))r=2;else if(Mp.test(t))r=8;else throw Error($e+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=js(n,new n(r),o,o*2)),c=nn(t,r,ge),u=c.length-1,o=u;c[o]===0;--o)c.pop();return o<0?new n(e.s*0):(e.e=un(c,u),e.d=c,x=!1,s&&(e=N(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?H(2,l):Ye.pow(2,l))),x=!0,e)}function qp(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:gt(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/pn(5,r)),t=gt(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function gt(e,t,r,n,i){var o,s,a,l,c=1,u=e.precision,p=Math.ceil(u/E);for(x=!1,l=r.times(r),a=new e(n);;){if(s=N(a.times(l),new e(t++*t++),u,1),a=i?n.plus(s):n.minus(s),n=N(s.times(l),new e(t++*t++),u,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,c++}return x=!0,s.d.length=p+1,s}function pn(e,t){for(var r=e;--t;)r*=e;return r}function Hs(e,t){var r,n=t.s<0,i=Ee(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return Oe=n?4:1,t;if(r=t.divToInt(i),r.isZero())Oe=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return Oe=_s(r)?n?2:3:n?4:1,t;Oe=_s(r)?n?1:4:n?3:2}return t.minus(i).abs()}function _i(e,t,r,n){var i,o,s,a,l,c,u,p,d,m=e.constructor,g=r!==void 0;if(g?(ae(r,1,Ve),n===void 0?n=m.rounding:ae(n,0,8)):(r=m.precision,n=m.rounding),!e.isFinite())u=Qs(e);else{for(u=xe(e),s=u.indexOf("."),g?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(u=u.replace(".",""),d=new m(1),d.e=u.length-s,d.d=nn(xe(d),10,i),d.e=d.d.length),p=nn(u,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])u=g?"0p+0":"0";else{if(s<0?o--:(e=new m(e),e.d=p,e.e=o,e=N(e,d,r,n,0,i),p=e.d,o=e.e,c=Fs),s=p[r],a=i/2,c=c||p[r+1]!==void 0,c=n<4?(s!==void 0||c)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||c||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,c)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,u="";s<l;s++)u+=Ii.charAt(p[s]);if(g){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)u+="0";for(p=nn(u,i,t),l=p.length;!p[l-1];--l);for(s=1,u="1.";s<l;s++)u+=Ii.charAt(p[s])}else u=u.charAt(0)+"."+u.slice(1);u=u+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)u="0"+u;u="0."+u}else if(++o>l)for(o-=l;o--;)u+="0";else o<l&&(u=u.slice(0,o)+"."+u.slice(o))}u=(t==16?"0x":t==2?"0b":t==8?"0o":"")+u}return e.s<0?"-"+u:u}function Ns(e,t){if(e.length>t)return e.length=t,!0}function Up(e){return new this(e).abs()}function jp(e){return new this(e).acos()}function Bp(e){return new this(e).acosh()}function Qp(e,t){return new this(e).plus(t)}function Hp(e){return new this(e).asin()}function Gp(e){return new this(e).asinh()}function Wp(e){return new this(e).atan()}function Jp(e){return new this(e).atanh()}function Kp(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=Ee(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?Ee(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=Ee(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(N(e,t,o,1)),t=Ee(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(N(e,t,o,1)),r}function zp(e){return new this(e).cbrt()}function Yp(e){return y(e=new this(e),e.e+1,2)}function Zp(e,t,r){return new this(e).clamp(t,r)}function Xp(e){if(!e||typeof e!="object")throw Error(cn+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,Ve,"rounding",0,8,"toExpNeg",-ft,0,"toExpPos",0,ft,"maxE",0,ft,"minE",-ft,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=ki[r]),(n=e[r])!==void 0)if(te(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error($e+r+": "+n);if(r="crypto",i&&(this[r]=ki[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error($s);else this[r]=!1;else throw Error($e+r+": "+n);return this}function ed(e){return new this(e).cos()}function td(e){return new this(e).cosh()}function Gs(e){var t,r,n;function i(o){var s,a,l,c=this;if(!(c instanceof i))return new i(o);if(c.constructor=i,Ms(o)){c.s=o.s,x?!o.d||o.e>i.maxE?(c.e=NaN,c.d=null):o.e<i.minE?(c.e=0,c.d=[0]):(c.e=o.e,c.d=o.d.slice()):(c.e=o.e,c.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){c.s=1/o<0?-1:1,c.e=0,c.d=[0];return}if(o<0?(o=-o,c.s=-1):c.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;x?s>i.maxE?(c.e=NaN,c.d=null):s<i.minE?(c.e=0,c.d=[0]):(c.e=s,c.d=[o]):(c.e=s,c.d=[o]);return}if(o*0!==0){o||(c.s=NaN),c.e=NaN,c.d=null;return}return on(c,o.toString())}if(l==="string")return(a=o.charCodeAt(0))===45?(o=o.slice(1),c.s=-1):(a===43&&(o=o.slice(1)),c.s=1),qs.test(o)?on(c,o):Vp(c,o);if(l==="bigint")return o<0?(o=-o,c.s=-1):c.s=1,on(c,o.toString());throw Error($e+o)}if(i.prototype=f,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=Xp,i.clone=Gs,i.isDecimal=Ms,i.abs=Up,i.acos=jp,i.acosh=Bp,i.add=Qp,i.asin=Hp,i.asinh=Gp,i.atan=Wp,i.atanh=Jp,i.atan2=Kp,i.cbrt=zp,i.ceil=Yp,i.clamp=Zp,i.cos=ed,i.cosh=td,i.div=rd,i.exp=nd,i.floor=id,i.hypot=od,i.ln=sd,i.log=ad,i.log10=cd,i.log2=ld,i.max=ud,i.min=pd,i.mod=dd,i.mul=md,i.pow=fd,i.random=gd,i.round=hd,i.sign=yd,i.sin=wd,i.sinh=Ed,i.sqrt=xd,i.sub=bd,i.sum=Pd,i.tan=vd,i.tanh=Td,i.trunc=Ad,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function rd(e,t){return new this(e).div(t)}function nd(e){return new this(e).exp()}function id(e){return y(e=new this(e),e.e+1,3)}function od(){var e,t,r=new this(0);for(x=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return x=!0,new this(1/0);r=t}return x=!0,r.sqrt()}function Ms(e){return e instanceof Ye||e&&e.toStringTag===Vs||!1}function sd(e){return new this(e).ln()}function ad(e,t){return new this(e).log(t)}function ld(e){return new this(e).log(2)}function cd(e){return new this(e).log(10)}function ud(){return Bs(this,arguments,-1)}function pd(){return Bs(this,arguments,1)}function dd(e,t){return new this(e).mod(t)}function md(e,t){return new this(e).mul(t)}function fd(e,t){return new this(e).pow(t)}function gd(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:ae(e,1,Ve),n=Math.ceil(e/E),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error($s);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=E,n&&e&&(i=H(10,E-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=E)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<E&&(r-=E-n)}return s.e=r,s.d=a,s}function hd(e){return y(e=new this(e),e.e+1,this.rounding)}function yd(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function wd(e){return new this(e).sin()}function Ed(e){return new this(e).sinh()}function xd(e){return new this(e).sqrt()}function bd(e,t){return new this(e).sub(t)}function Pd(){var e=0,t=arguments,r=new this(t[e]);for(x=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return x=!0,y(r,this.precision,this.rounding)}function vd(e){return new this(e).tan()}function Td(e){return new this(e).tanh()}function Ad(e){return y(e=new this(e),e.e+1,1)}f[Symbol.for("nodejs.util.inspect.custom")]=f.toString;f[Symbol.toStringTag]="Decimal";var Ye=f.constructor=Gs(ki);sn=new Ye(sn);an=new Ye(an);var K=Ye;function qe(e){return e===null?e:Array.isArray(e)?e.map(qe):typeof e=="object"?Cd(e)?Sd(e):e.constructor!==null&&e.constructor.name!=="Object"?e:mt(e,qe):e}function Cd(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Sd({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new K(t);case"Json":return JSON.parse(t);default:fe(t,"Unknown tagged value")}}var be=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function Ue(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function Ws(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}function rr(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function Js(e){return{models:Ni(e.models),enums:Ni(e.enums),types:Ni(e.types)}}function Ni(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function ht(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function dn(e){return e.toString()!=="Invalid Date"}function yt(e){return Ye.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var nr={};ut(nr,{ModelAction:()=>wt,datamodelEnumToSchemaEnum:()=>Rd});function Rd(e){return{name:e.name,values:e.values.map(t=>t.name)}}var wt=(w=>(w.findUnique="findUnique",w.findUniqueOrThrow="findUniqueOrThrow",w.findFirst="findFirst",w.findFirstOrThrow="findFirstOrThrow",w.findMany="findMany",w.create="create",w.createMany="createMany",w.createManyAndReturn="createManyAndReturn",w.update="update",w.updateMany="updateMany",w.updateManyAndReturn="updateManyAndReturn",w.upsert="upsert",w.delete="delete",w.deleteMany="deleteMany",w.groupBy="groupBy",w.count="count",w.aggregate="aggregate",w.findRaw="findRaw",w.aggregateRaw="aggregateRaw",w))(wt||{});var Xs=j(hs());var Zs=j(require("node:fs"));var Ks={keyword:Ie,entity:Ie,value:e=>re(ze(e)),punctuation:ze,directive:Ie,function:Ie,variable:e=>re(ze(e)),string:e=>re(Ht(e)),boolean:Ke,number:Ie,comment:Gt};var Id=e=>e,mn={},kd=0,v={manual:mn.Prism&&mn.Prism.manual,disableWorkerMessageHandler:mn.Prism&&mn.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof he){let t=e;return new he(t.type,v.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(v.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++kd}),e.__id},clone:function e(t,r){let n,i,o=v.util.type(t);switch(r=r||{},o){case"Object":if(i=v.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=v.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=v.util.clone(v.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||v.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,v.languages.DFS(v.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=v.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=v.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return v.hooks.run("before-tokenize",n),n.tokens=v.tokenize(n.code,n.grammar),v.hooks.run("after-tokenize",n),he.stringify(v.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let h in r){if(!r.hasOwnProperty(h)||!r[h])continue;if(h==s)return;let C=r[h];C=v.util.type(C)==="Array"?C:[C];for(let A=0;A<C.length;++A){let S=C[A],w=S.inside,k=!!S.lookbehind,me=!!S.greedy,ue=0,Qt=S.alias;if(me&&!S.pattern.global){let B=S.pattern.toString().match(/[imuy]*$/)[0];S.pattern=RegExp(S.pattern.source,B+"g")}S=S.pattern||S;for(let B=n,oe=i;B<t.length;oe+=t[B].length,++B){let Se=t[B];if(t.length>e.length)return;if(Se instanceof he)continue;if(me&&B!=t.length-1){S.lastIndex=oe;var p=S.exec(e);if(!p)break;var u=p.index+(k?p[1].length:0),d=p.index+p[0].length,a=B,l=oe;for(let _=t.length;a<_&&(l<d||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,u>=l&&(++B,oe=l);if(t[B]instanceof he)continue;c=a-B,Se=e.slice(oe,l),p.index-=oe}else{S.lastIndex=0;var p=S.exec(Se),c=1}if(!p){if(o)break;continue}k&&(ue=p[1]?p[1].length:0);var u=p.index+ue,p=p[0].slice(ue),d=u+p.length,m=Se.slice(0,u),g=Se.slice(d);let Y=[B,c];m&&(++B,oe+=m.length,Y.push(m));let ct=new he(h,w?v.tokenize(p,w):p,Qt,p,me);if(Y.push(ct),g&&Y.push(g),Array.prototype.splice.apply(t,Y),c!=1&&v.matchGrammar(e,t,r,B,oe,!0,h),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return v.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=v.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=v.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:he};v.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};v.languages.javascript=v.languages.extend("clike",{"class-name":[v.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});v.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;v.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:v.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:v.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:v.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:v.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});v.languages.markup&&v.languages.markup.tag.addInlined("script","javascript");v.languages.js=v.languages.javascript;v.languages.typescript=v.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});v.languages.ts=v.languages.typescript;function he(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}he.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return he.stringify(r,t)}).join(""):Od(e.type)(e.content)};function Od(e){return Ks[e]||Id}function zs(e){return Dd(e,v.languages.javascript)}function Dd(e,t){return v.tokenize(e,t).map(n=>he.stringify(n)).join("")}function Ys(e){return wi(e)}var fn=class e{firstLineNumber;lines;static read(t){let r;try{r=Zs.default.readFileSync(t,"utf-8")}catch{return null}return e.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new e(1,r)}constructor(t,r){this.firstLineNumber=t,this.lines=r}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new e(this.firstLineNumber,i)}mapLines(t){return new e(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new e(t,Ys(n).split(`
`))}highlight(){let t=zs(this.toString());return new e(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var _d={red:Re,gray:Gt,dim:Je,bold:re,underline:se,highlightSource:e=>e.highlight()},Nd={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function Md({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function Fd({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s=Md({message:t,originalMethod:r,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),c=fn.read(a.fileName)?.slice(l,a.lineNumber),u=c?.lineAt(a.lineNumber);if(c&&u){let p=$d(u),d=Ld(u);if(!d)return s;s.functionName=`${d.code})`,s.location=a,n||(c=c.mapLineAt(a.lineNumber,g=>g.slice(0,d.openingBraceIndex))),c=o.highlightSource(c);let m=String(c.lastLineNumber).length;if(s.contextLines=c.mapLines((g,h)=>o.gray(String(h).padStart(m))+" "+g).mapLines(g=>o.dim(g)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let g=p+m+1;g+=2,s.callArguments=(0,Xs.default)(i,g).slice(g)}}return s}function Ld(e){let t=Object.keys(wt).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function $d(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function Vd({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(qd(t))),i){a.push("");let c=[i.toString()];o&&(c.push(o),c.push(s.dim(")"))),a.push(c.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function qd(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function gn(e){let t=e.showColors?_d:Nd,r;return r=Fd(e,t),Vd(r,t)}var la=j(Mi());function na(e,t,r){let n=ia(e),i=Ud(n),o=Bd(i);o?hn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function ia(e){return e.errors.flatMap(t=>t.kind==="Union"?ia(t):[t])}function Ud(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:jd(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function jd(e,t){return[...new Set(e.concat(t))]}function Bd(e){return Ri(e,(t,r)=>{let n=ta(t),i=ta(r);return n!==i?n-i:ra(t)-ra(r)})}function ta(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function ra(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var pe=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};sa();var Et=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};oa();var yn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};var wn=e=>e,En={bold:wn,red:wn,green:wn,dim:wn,enabled:!1},aa={bold:re,red:Re,green:Ht,dim:Je,enabled:!0},xt={write(e){e.writeLine(",")}};var Pe=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var je=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var bt=class extends je{items=[];addItem(t){return this.items.push(new yn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Pe("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(xt,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Pt=class e extends je{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof bt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Pe("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(xt,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};var W=class extends je{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Pe(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};var ir=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(xt,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function hn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":Qd(e,t);break;case"IncludeOnScalar":Hd(e,t);break;case"EmptySelection":Gd(e,t,r);break;case"UnknownSelectionField":zd(e,t);break;case"InvalidSelectionValue":Yd(e,t);break;case"UnknownArgument":Zd(e,t);break;case"UnknownInputField":Xd(e,t);break;case"RequiredArgumentMissing":em(e,t);break;case"InvalidArgumentType":tm(e,t);break;case"InvalidArgumentValue":rm(e,t);break;case"ValueTooLarge":nm(e,t);break;case"SomeFieldsMissing":im(e,t);break;case"TooManyFieldsGiven":om(e,t);break;case"Union":na(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function Qd(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function Hd(e,t){let[r,n]=or(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new pe(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${sr(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function Gd(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){Wd(e,t,i);return}if(n.hasField("select")){Jd(e,t);return}}if(r?.[Ue(e.outputType.name)]){Kd(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function Wd(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new pe(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Jd(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),pa(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${sr(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function Kd(e,t){let r=new ir;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new pe("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=or(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let l=a?.value.asObject()??new Pt;l.addSuggestion(n),a.value=l}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function zd(e,t){let r=da(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":pa(n,e.outputType);break;case"include":sm(n,e.outputType);break;case"omit":am(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(sr(n)),i.join(" ")})}function Yd(e,t){let r=da(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Zd(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),lm(n,e.arguments)),t.addErrorMessage(i=>ca(i,r,e.arguments.map(o=>o.name)))}function Xd(e,t){let[r,n]=or(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&ma(o,e.inputType)}t.addErrorMessage(o=>ca(o,n,e.inputType.fields.map(s=>s.name)))}function ca(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=um(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(sr(e)),n.join(" ")}function em(e,t){let r;t.addErrorMessage(l=>r?.value instanceof W&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=or(e.argumentPath),s=new ir,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new pe(o,s).makeRequired())}else{let l=e.inputTypes.map(ua).join(" | ");a.addSuggestion(new pe(o,l).makeRequired())}}function ua(e){return e.kind==="list"?`${ua(e.elementType)}[]`:e.name}function tm(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=xn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function rm(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=xn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function nm(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof W&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function im(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&ma(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${xn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(sr(i)),o.join(" ")})}function om(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${xn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function pa(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new pe(r.name,"true"))}function sm(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new pe(r.name,"true"))}function am(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new pe(r.name,"true"))}function lm(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new pe(r.name,r.typeNames.join(" | ")))}function da(e,t){let[r,n]=or(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),l=o?.getField(n);return o&&l?{parentKind:"select",parent:o,field:l,fieldName:n}:(l=s?.getField(n),s&&l?{parentKind:"include",field:l,parent:s,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"omit",field:l,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function ma(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new pe(r.name,r.typeNames.join(" | ")))}function or(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function sr({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function xn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var cm=3;function um(e,t){let r=1/0,n;for(let i of t){let o=(0,la.default)(e,i);o>cm||o<r&&(r=o,n=i)}return n}var ar=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function vt(e){return e instanceof ar}var bn=Symbol(),Li=new WeakMap,De=class{constructor(t){t===bn?Li.set(this,`Prisma.${this._getName()}`):Li.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Li.get(this)}},lr=class extends De{_getNamespace(){return"NullTypes"}},cr=class extends lr{#e};$i(cr,"DbNull");var ur=class extends lr{#e};$i(ur,"JsonNull");var pr=class extends lr{#e};$i(pr,"AnyNull");var Pn={classes:{DbNull:cr,JsonNull:ur,AnyNull:pr},instances:{DbNull:new cr(bn),JsonNull:new ur(bn),AnyNull:new pr(bn)}};function $i(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var fa=": ",vn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+fa.length}write(t){let r=new Pe(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(fa).write(this.value)}};var Vi=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function Tt(e){return new Vi(ga(e))}function ga(e){let t=new Pt;for(let[r,n]of Object.entries(e)){let i=new vn(r,ha(n));t.addField(i)}return t}function ha(e){if(typeof e=="string")return new W(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new W(String(e));if(typeof e=="bigint")return new W(`${e}n`);if(e===null)return new W("null");if(e===void 0)return new W("undefined");if(yt(e))return new W(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new W(`Buffer.alloc(${e.byteLength})`):new W(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=dn(e)?e.toISOString():"Invalid Date";return new W(`new Date("${t}")`)}return e instanceof De?new W(`Prisma.${e._getName()}`):vt(e)?new W(`prisma.${Ue(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?pm(e):typeof e=="object"?ga(e):new W(Object.prototype.toString.call(e))}function pm(e){let t=new bt;for(let r of e)t.addItem(ha(r));return t}function Tn(e,t){let r=t==="pretty"?aa:En,n=e.renderAllMessages(r),i=new Et(0,{colors:r}).write(e).toString();return{message:n,args:i}}function An({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=Tt(e);for(let p of t)hn(p,a,s);let{message:l,args:c}=Tn(a,r),u=gn({message:l,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:c});throw new ee(u,{clientVersion:o})}function ve(e){return e.replace(/^./,t=>t.toLowerCase())}function wa(e,t,r){let n=ve(r);return!t.result||!(t.result.$allModels||t.result[n])?e:dm({...e,...ya(t.name,e,t.result.$allModels),...ya(t.name,e,t.result[n])})}function dm(e){let t=new be,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return mt(e,n=>({...n,needs:r(n.name,new Set)}))}function ya(e,t,r){return r?mt(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:mm(t,o,i)})):{}}function mm(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Ea(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function xa(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var Cn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new be;modelExtensionsCache=new be;queryCallbacksCache=new be;clientExtensions=rr(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=rr(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>wa(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=ve(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},At=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new Cn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new Cn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var Sn=class{constructor(t){this.name=t}};function ba(e){return e instanceof Sn}function Pa(e){return new Sn(e)}var va=Symbol(),dr=class{constructor(t){if(t!==va)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?Rn:t}},Rn=new dr(va);function Te(e){return e instanceof dr}var fm={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},Ta="explicitly `undefined` values are not allowed";function In({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=At.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:l,previewFeatures:c,globalOmit:u}){let p=new qi({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l,previewFeatures:c,globalOmit:u});return{modelName:e,action:fm[t],query:mr(r,p)}}function mr({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:Ca(r,n),selection:gm(e,t,i,n)}}function gm(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Em(e,n)):hm(n,t,r)}function hm(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&ym(n,t,e),wm(n,r,e),n}function ym(e,t,r){for(let[n,i]of Object.entries(t)){if(Te(i))continue;let o=r.nestSelection(n);if(Ui(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=mr(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=mr(i,o)}}function wm(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=xa(i,n);for(let[s,a]of Object.entries(o)){if(Te(a))continue;Ui(a,r.nestSelection(s));let l=r.findField(s);n?.[s]&&!l||(e[s]=!a)}}function Em(e,t){let r={},n=t.getComputedFields(),i=Ea(e,n);for(let[o,s]of Object.entries(i)){if(Te(s))continue;let a=t.nestSelection(o);Ui(s,a);let l=t.findField(o);if(!(n?.[o]&&!l)){if(s===!1||s===void 0||Te(s)){r[o]=!1;continue}if(s===!0){l?.kind==="object"?r[o]=mr({},a):r[o]=!0;continue}r[o]=mr(s,a)}}return r}function Aa(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(ht(e)){if(dn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(ba(e))return{$type:"Param",value:e.name};if(vt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return xm(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:Buffer.from(r,n,i).toString("base64")}}if(bm(e))return e.values;if(yt(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof De){if(e!==Pn.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Pm(e))return e.toJSON();if(typeof e=="object")return Ca(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function Ca(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);Te(i)||(i!==void 0?r[n]=Aa(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:Ta}))}return r}function xm(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||Te(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(Aa(o,i))}return r}function bm(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Pm(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function Ui(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:Ta})}var qi=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){An({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Ue(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:fe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function Sa(e){if(!e._hasPreviewFlag("metrics"))throw new ee("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var Ct=class{_client;constructor(t){this._client=t}prometheus(t){return Sa(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return Sa(this._client),this._client._engine.metrics({format:"json",...t})}};function Ra(e,t){let r=rr(()=>vm(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function vm(e){return{datamodel:{models:ji(e.models),enums:ji(e.enums),types:ji(e.types)}}}function ji(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}var Bi=new WeakMap,kn="$$PrismaTypedSql",fr=class{constructor(t,r){Bi.set(this,{sql:t,values:r}),Object.defineProperty(this,kn,{value:kn})}get sql(){return Bi.get(this).sql}get values(){return Bi.get(this).values}};function Ia(e){return(...t)=>new fr(e,t)}function On(e){return e!=null&&e[kn]===kn}var Su=j(yi());var Ru=require("node:async_hooks"),Iu=require("node:events"),ku=j(require("node:fs")),ui=j(require("node:path"));var le=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function ka(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new le([r,...Array(e.length-1).fill(t),n],e)}function Qi(e){return new le([e],[])}var Oa=Qi("");function Hi(e,...t){return new le(e,t)}function gr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function ie(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}function Ze(e){let t=new be;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var Dn={enumerable:!0,configurable:!0,writable:!0};function _n(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>Dn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var Da=Symbol.for("nodejs.util.inspect.custom");function ye(e,t){let r=Tm(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=_a(Reflect.ownKeys(o),r),a=_a(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...Dn,...l?.getPropertyDescriptor(s)}:Dn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[Da]=function(){let o={...this};return delete o[Da],o},i}function Tm(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function _a(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function St(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function Rt(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function Na(e){if(e===void 0)return"";let t=Tt(e);return new Et(0,{colors:En}).write(t).toString()}var Am="P2037";function Nn({error:e,user_facing_error:t},r,n){return t.error_code?new U(Cm(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new X(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function Cm(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Am&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}var hr="<unknown>";function Ma(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=Im(n)||Om(n)||Nm(n)||$m(n)||Fm(n);return i&&r.push(i),r},[])}var Sm=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Rm=/\((\S*)(?::(\d+))(?::(\d+))\)/;function Im(e){var t=Sm.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=Rm.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||hr,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var km=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function Om(e){var t=km.exec(e);return t?{file:t[2],methodName:t[1]||hr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Dm=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,_m=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function Nm(e){var t=Dm.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=_m.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||hr,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var Mm=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function Fm(e){var t=Mm.exec(e);return t?{file:t[3],methodName:t[1]||hr,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var Lm=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function $m(e){var t=Lm.exec(e);return t?{file:t[2],methodName:t[1]||hr,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Gi=class{getLocation(){return null}},Wi=class{_error;constructor(){this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=Ma(t).find(i=>{if(!i.file)return!1;let o=Pi(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function Be(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Gi:new Wi}var Fa={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function It(e={}){let t=qm(e);return Object.entries(t).reduce((n,[i,o])=>(Fa[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function qm(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Mn(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function La(e,t){let r=Mn(e);return t({action:"aggregate",unpacker:r,argsMapper:It})(e)}function Um(e={}){let{select:t,...r}=e;return typeof t=="object"?It({...r,_count:t}):It({...r,_count:{_all:!0}})}function jm(e={}){return typeof e.select=="object"?t=>Mn(e)(t)._count:t=>Mn(e)(t)._count._all}function $a(e,t){return t({action:"count",unpacker:jm(e),argsMapper:Um})(e)}function Bm(e={}){let t=It(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function Qm(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function Va(e,t){return t({action:"groupBy",unpacker:Qm(e),argsMapper:Bm})(e)}function qa(e,t,r){if(t==="aggregate")return n=>La(n,r);if(t==="count")return n=>$a(n,r);if(t==="groupBy")return n=>Va(n,r)}function Ua(e,t){let r=t.fields.filter(i=>!i.relationName),n=Ws(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new ar(e,o,s.type,s.isList,s.kind==="enum")},..._n(Object.keys(n))})}var ja=e=>Array.isArray(e)?e:e.split("."),Ji=(e,t)=>ja(t).reduce((r,n)=>r&&r[n],e),Ba=(e,t,r)=>ja(t).reduceRight((n,i,o,s)=>Object.assign({},Ji(e,s.slice(0,o)),{[i]:n}),r);function Hm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function Gm(e,t,r){return t===void 0?e??{}:Ba(t,r,e||!0)}function Ki(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,c)=>({...l,[c.name]:c}),{});return l=>{let c=Be(e._errorFormat),u=Hm(n,i),p=Gm(l,o,u),d=r({dataPath:u,callsite:c})(p),m=Wm(e,t);return new Proxy(d,{get(g,h){if(!m.includes(h))return g[h];let A=[a[h].type,r,h],S=[u,p];return Ki(e,...A,...S)},..._n([...m,...Object.getOwnPropertyNames(d)])})}}function Wm(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var Jm=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Km=["aggregate","count","groupBy"];function zi(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[zm(e,t),Zm(e,t),gr(r),ie("name",()=>t),ie("$name",()=>t),ie("$parent",()=>e._appliedParent)];return ye({},n)}function zm(e,t){let r=ve(t),n=Object.keys(wt).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>l=>{let c=Be(e._errorFormat);return e._createPrismaPromise(u=>{let p={args:l,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:u,callsite:c};return e._request({...p,...a})},{action:o,args:l,model:t})};return Jm.includes(o)?Ki(e,t,s):Ym(i)?qa(e,i,s):s({})}}}function Ym(e){return Km.includes(e)}function Zm(e,t){return Ze(ie("fields",()=>{let r=e._runtimeDataModel.models[t];return Ua(t,r)}))}function Qa(e){return e.replace(/^./,t=>t.toUpperCase())}var Yi=Symbol();function yr(e){let t=[Xm(e),ef(e),ie(Yi,()=>e),ie("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(gr(r)),ye(e,t)}function Xm(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function ef(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(ve),n=[...new Set(t.concat(r))];return Ze({getKeys(){return n},getPropertyValue(i){let o=Qa(i);if(e._runtimeDataModel.models[o]!==void 0)return zi(e,o);if(e._runtimeDataModel.models[i]!==void 0)return zi(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function Ha(e){return e[Yi]?e[Yi]:e}function Ga(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return yr(t)}function Wa({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let l of Object.values(o)){if(n){if(n[l.name])continue;let c=l.needs.filter(u=>n[u]);c.length>0&&a.push(St(c))}else if(r){if(!r[l.name])continue;let c=l.needs.filter(u=>!r[u]);c.length>0&&a.push(St(c))}tf(e,l.needs)&&s.push(rf(l,ye(e,s)))}return s.length>0||a.length>0?ye(e,[...s,...a]):e}function tf(e,t){return t.every(r=>Si(e,r))}function rf(e,t){return Ze(ie(e.name,()=>e.compute(t)))}function Fn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Fn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Ja({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Ja({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Ja({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||Te(s))continue;let l=n.models[r].fields.find(u=>u.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let c=typeof s=="object"?s:{};t[o]=Fn({visitor:i,result:t[o],args:c,modelName:l.type,runtimeDataModel:n})}}function Ka({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Fn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,l,c)=>{let u=ve(l);return Wa({result:a,modelName:u,select:c.select,omit:c.select?void 0:{...o?.[u],...c.omit},extensions:n})}})}var nf=["$connect","$disconnect","$on","$transaction","$use","$extends"],za=nf;function Ya(e){if(e instanceof le)return of(e);if(On(e))return sf(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=wr(e[n]);return r}let t={};for(let r in e)t[r]=wr(e[r]);return t}function of(e){return new le(e.strings,e.values)}function sf(e){return new fr(e.sql,e.values)}function wr(e){if(typeof e!="object"||e==null||e instanceof De||vt(e))return e;if(yt(e))return new K(e.toFixed());if(ht(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=wr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:wr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=wr(e[r]);return t}fe(e,"Unknown value")}function Xa(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Ya(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=nl(o,l),a.args=s,Xa(e,a,r,n+1)}})})}function el(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return Xa(e,t,s)}function tl(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?rl(r,n,0,e):e(r)}}function rl(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=nl(i,l),rl(a,t,r+1,n)}})}var Za=e=>e;function nl(e=Za,t=Za){return r=>e(t(r))}var il=V("prisma:client"),ol={Vercel:"vercel","Netlify CI":"netlify"};function sl({postinstall:e,ciName:t,clientVersion:r}){if(il("checkPlatformCaching:postinstall",e),il("checkPlatformCaching:ciName",t),e===!0&&t&&t in ol){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${ol[t]}-build`;throw console.error(n),new O(n,r)}}function al(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}var af=()=>globalThis.process?.release?.name==="node",lf=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,cf=()=>!!globalThis.Deno,uf=()=>typeof globalThis.Netlify=="object",pf=()=>typeof globalThis.EdgeRuntime=="object",df=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function mf(){return[[uf,"netlify"],[pf,"edge-light"],[df,"workerd"],[cf,"deno"],[lf,"bun"],[af,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var ff={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function ll(){let e=mf();return{id:e,prettyName:ff[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var Zi=j(bi());function cl(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}function ul(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var pl=j(ks());function dl({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,pl.default)({user:t,repo:r,template:n,title:e,body:i})}function ml({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=ss(6e3-(s?.length??0)),l=ul((0,Zi.default)(a)),c=n?`# Description
\`\`\`
${n}
\`\`\``:"",u=(0,Zi.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${c}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?cl(s):""}
\`\`\`
`),p=dl({title:r,body:u});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${se(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}function L(e,t){throw new Error(t)}function Xi(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Xi(e[r],t[r]))}function Er(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]==typeof t[o]&&typeof e[o]!="object")return e[o]===t[o];if(K.isDecimal(e[o])||K.isDecimal(t[o])){let s=fl(e[o]),a=fl(t[o]);return s&&a&&s.equals(a)}else if(e[o]instanceof Uint8Array||t[o]instanceof Uint8Array){let s=gl(e[o]),a=gl(t[o]);return s&&a&&s.equals(a)}else{if(e[o]instanceof Date||t[o]instanceof Date)return hl(e[o])?.getTime()===hl(t[o])?.getTime();if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return yl(e[o])===yl(t[o]);if(typeof e[o]=="number"||typeof t[o]=="number")return wl(e[o])===wl(t[o])}return Xi(e[o],t[o])})}function fl(e){return K.isDecimal(e)?e:typeof e=="number"||typeof e=="string"?new K(e):void 0}function gl(e){return Buffer.isBuffer(e)?e:e instanceof Uint8Array?Buffer.from(e.buffer,e.byteOffset,e.byteLength):typeof e=="string"?Buffer.from(e,"base64"):void 0}function hl(e){return e instanceof Date?e:typeof e=="string"||typeof e=="number"?new Date(e):void 0}function yl(e){return typeof e=="bigint"?e:typeof e=="number"||typeof e=="string"?BigInt(e):void 0}function wl(e){return typeof e=="number"?e:typeof e=="string"?Number(e):void 0}function xr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?Buffer.from(r).toString("base64"):r)}var q=class extends Error{name="DataMapperError"};function bl(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new q(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return eo(e,t.fields,r);case"Value":return to(e,"<result>",t.resultType,r);default:L(t,`Invalid data mapping type: '${t.type}'`)}}function eo(e,t,r){if(e===null)return null;if(Array.isArray(e))return e.map(i=>El(i,t,r));if(typeof e=="object")return El(e,t,r);if(typeof e=="string"){let n;try{n=JSON.parse(e)}catch(i){throw new q("Expected an array or object, got a string that is not valid JSON",{cause:i})}return eo(n,t,r)}throw new q(`Expected an array or an object, got: ${typeof e}`)}function El(e,t,r){if(typeof e!="object")throw new q(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new q(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(o.serializedName!==null&&!Object.hasOwn(e,o.serializedName))throw new q(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.serializedName!==null?e[o.serializedName]:e;n[i]=eo(s,o.fields,r);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=to(e[s],s,o.resultType,r);else throw new q(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:L(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function to(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new q(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new q(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new q(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new q(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new q(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new q(`Expected a float in column '${t}', got string: ${e}`);return i}throw new q(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new q(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}if(e instanceof Uint8Array){for(let i of e)if(i!==0)return!0;return!1}throw new q(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!K.isDecimal(e))throw new q(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:xl(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new q(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${xl(e)}`};throw new q(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>to(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:typeof e=="string"?e:xr(e)};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:Buffer.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:Buffer.from(e).toString("base64")};if(e instanceof Uint8Array)return{$type:"Bytes",value:Buffer.from(e).toString("base64")};throw new q(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new q(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new q(`Unknown enum value '${e}' for enum '${r.inner}'`);return o}default:L(r,`DataMapper: Unknown result type: ${r.type}`)}}var gf=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function xl(e){let t=gf.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}var br;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(br||(br={}));function hf(e){switch(e){case"postgresql":case"postgres":case"prisma+postgres":return"postgresql";case"sqlserver":return"mssql";case"mysql":case"sqlite":case"cockroachdb":case"mongodb":return e;default:L(e,`Unknown provider: ${e}`)}}async function Ln({query:e,tracingHelper:t,provider:r,onQuery:n,execute:i}){return await t.runInChildSpan({name:"db_query",kind:br.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":hf(r)}},async()=>{let o=new Date,s=performance.now(),a=await i(),l=performance.now();return n?.({timestamp:o,duration:l-s,query:e.sql,params:e.args}),a})}function ro(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}var T={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};var Ae=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function Pl(e){if(!ro(e))throw e;let t=yf(e),r=wf(e);throw!t||!r?e:new Ae(r,t,{driverAdapterError:e})}function yf(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:L(e.cause,`Unknown error: ${e.cause}`)}}function wf(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${no(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${no(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${no(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type: ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:L(e.cause,`Unknown error: ${e.cause}`)}}function no(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}function Xe(e,t){var r="000000000"+e;return r.substr(r.length-t)}var vl=j(require("node:os"),1);function Ef(){try{return vl.default.hostname()}catch{return process.env._CLUSTER_NETWORK_NAME_||process.env.COMPUTERNAME||"hostname"}}var Tl=2,xf=Xe(process.pid.toString(36),Tl),Al=Ef(),bf=Al.length,Pf=Xe(Al.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+bf+36).toString(36),Tl);function io(){return xf+Pf}function $n(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function oo(e){let n=Math.pow(36,4),i=0;function o(){return Xe((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var l="c",c=new Date().getTime().toString(36),u=Xe(s().toString(36),4),p=e(),d=o()+o();return l+c+u+p+d}return a.fingerprint=e,a.isCuid=$n,a}var vf=oo(io);var Cl=vf;var Ac=j(hc());var go=require("node:crypto");var yc="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var cg=128,tt,Dt;function ug(e){!tt||tt.length<e?(tt=Buffer.allocUnsafe(e*cg),go.webcrypto.getRandomValues(tt),Dt=0):Dt+e>tt.length&&(go.webcrypto.getRandomValues(tt),Dt=0),Dt+=e}function ho(e=21){ug(e|=0);let t="";for(let r=Dt-e;r<Dt;r++)t+=yc[tt[r]&63];return t}var Sr=j(require("node:crypto"),1);var Ec="0123456789ABCDEFGHJKMNPQRSTVWXYZ",Rr=32;var pg=16,xc=10,wc=0xffffffffffff;var rt;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(rt||(rt={}));var nt=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function dg(e){let t=Math.floor(e()*Rr);return t===Rr&&(t=Rr-1),Ec.charAt(t)}function mg(e){let t=fg(),r=t&&(t.crypto||t.msCrypto)||(typeof Sr.default<"u"?Sr.default:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(Sr.default?.randomBytes)return()=>Sr.default.randomBytes(1).readUInt8()/255;throw new nt(rt.PRNGDetectFailure,"Failed to find a reliable PRNG")}function fg(){return yg()?self:typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:null}function gg(e,t){let r="";for(;e>0;e--)r=dg(t)+r;return r}function hg(e,t=xc){if(isNaN(e))throw new nt(rt.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>wc)throw new nt(rt.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${wc}: ${e}`);if(e<0)throw new nt(rt.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new nt(rt.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%Rr,n=Ec.charAt(r)+n,e=(e-r)/Rr;return n}function yg(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function bc(e,t){let r=t||mg(),n=!e||isNaN(e)?Date.now():e;return hg(n,xc)+gg(pg,r)}var z=[];for(let e=0;e<256;++e)z.push((e+256).toString(16).slice(1));function jn(e,t=0){return(z[e[t+0]]+z[e[t+1]]+z[e[t+2]]+z[e[t+3]]+"-"+z[e[t+4]]+z[e[t+5]]+"-"+z[e[t+6]]+z[e[t+7]]+"-"+z[e[t+8]]+z[e[t+9]]+"-"+z[e[t+10]]+z[e[t+11]]+z[e[t+12]]+z[e[t+13]]+z[e[t+14]]+z[e[t+15]]).toLowerCase()}var Pc=require("node:crypto"),Qn=new Uint8Array(256),Bn=Qn.length;function _t(){return Bn>Qn.length-16&&((0,Pc.randomFillSync)(Qn),Bn=0),Qn.slice(Bn,Bn+=16)}var vc=require("node:crypto"),yo={randomUUID:vc.randomUUID};function wg(e,t,r){if(yo.randomUUID&&!t&&!e)return yo.randomUUID();e=e||{};let n=e.random??e.rng?.()??_t();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return jn(n)}var wo=wg;var Eo={};function Eg(e,t,r){let n;if(e)n=Tc(e.random??e.rng?.()??_t(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=_t();xg(Eo,i,o),n=Tc(o,Eo.msecs,Eo.seq,t,r)}return t??jn(n)}function xg(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Tc(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var xo=Eg;var Hn=class{#e={};constructor(){this.register("uuid",new vo),this.register("cuid",new To),this.register("ulid",new Ao),this.register("nanoid",new Co),this.register("product",new So)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new Po:new bo}})}register(t,r){this.#e[t]=r}},bo=class{#e=new Date;generate(){return this.#e.toISOString()}},Po=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},vo=class{generate(t){if(t===4)return wo();if(t===7)return xo();throw new Error("Invalid UUID generator arguments")}},To=class{generate(t){if(t===1)return Cl();if(t===2)return(0,Ac.createId)();throw new Error("Invalid CUID generator arguments")}},Ao=class{generate(){return bc()}},Co=class{generate(t){if(typeof t=="number")return ho(t);if(t===void 0)return ho();throw new Error("Invalid Nanoid generator arguments")}},So=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};function Ro(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function Io(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function Cc(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function Sc(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function Oo(e,t,r){let n=e.type;switch(n){case"rawSql":return Ic(e.sql,Rc(e.params,t,r));case"templateSql":return bg(e.fragments,e.placeholderFormat,Rc(e.params,t,r));default:L(n,"Invalid query type")}}function Rc(e,t,r){return e.map(n=>we(n,t,r))}function we(e,t,r){let n=e;for(;vg(n);)if(Ro(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(Io(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>we(a,t,r)))}else L(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>we(i,t,r)):Cc(n)?n=Buffer.from(n.prisma__value,"base64"):Sc(n)&&(n=BigInt(n.prisma__value)),n}function bg(e,t,r){let n=0,i=1,o=[],s=e.map(a=>{let l=a.type;switch(l){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),ko(t,i++);case"stringChunk":return a.chunk;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let c=r[n++],u=Array.isArray(c)?c:[c];return`(${u.length==0?"NULL":u.map(d=>(o.push(d),ko(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let c=r[n++];if(!Array.isArray(c))throw new Error("Malformed query template. Tuple list expected.");if(c.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return c.map(p=>{if(!Array.isArray(p))throw new Error("Malformed query template. Tuple expected.");let d=p.map(m=>(o.push(m),ko(t,i++))).join(a.itemSeparator);return`${a.itemPrefix}${d}${a.itemSuffix}`}).join(a.groupSeparator)}default:L(l,"Invalid fragment type")}}).join("");return Ic(s,o)}function ko(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function Ic(e,t){let r=t.map(n=>Pg(n));return{sql:e,args:t,argTypes:r}}function Pg(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":Buffer.isBuffer(e)?"Bytes":"Unknown"}function vg(e){return Ro(e)||Io(e)}function Oc(e){let t=e.columnTypes.map(r=>{switch(r){case T.Bytes:return n=>Array.isArray(n)?new Uint8Array(n):n;default:return n=>n}});return e.rows.map(r=>r.map((n,i)=>t[i](n)).reduce((n,i,o)=>{let s=e.columnNames[o].split("."),a=n;for(let l=0;l<s.length;l++){let c=s[l];l===s.length-1?a[c]=i:(a[c]===void 0&&(a[c]={}),a=a[c])}return n},{}))}function Dc(e){let r=e.columnTypes.map(n=>kc(n)).map(n=>{switch(n){case"bytes":return i=>Array.isArray(i)?new Uint8Array(i):i;case"int":return i=>i===null?null:typeof i=="number"?i:parseInt(`${i}`,10);case"bigint":return i=>i===null?null:typeof i=="bigint"?i:BigInt(`${i}`);case"json":return i=>typeof i=="string"?JSON.parse(i):i;case"bool":return i=>typeof i=="string"?i==="true"||i==="1":typeof i=="number"?i===1:i;default:return i=>i}});return{columns:e.columnNames,types:e.columnTypes.map(n=>kc(n)),rows:e.rows.map(n=>n.map((i,o)=>r[o](i)))}}function kc(e){switch(e){case T.Int32:return"int";case T.Int64:return"bigint";case T.Float:return"float";case T.Double:return"double";case T.Text:return"string";case T.Enum:return"enum";case T.Bytes:return"bytes";case T.Boolean:return"bool";case T.Character:return"char";case T.Numeric:return"decimal";case T.Json:return"json";case T.Uuid:return"uuid";case T.DateTime:return"datetime";case T.Date:return"date";case T.Time:return"time";case T.Int32Array:return"int-array";case T.Int64Array:return"bigint-array";case T.FloatArray:return"float-array";case T.DoubleArray:return"double-array";case T.TextArray:return"string-array";case T.EnumArray:return"string-array";case T.BytesArray:return"bytes-array";case T.BooleanArray:return"bool-array";case T.CharacterArray:return"char-array";case T.NumericArray:return"decimal-array";case T.JsonArray:return"json-array";case T.UuidArray:return"uuid-array";case T.DateTimeArray:return"datetime-array";case T.DateArray:return"date-array";case T.TimeArray:return"time-array";case T.UnknownNumber:return"unknown";case T.Set:return"string";default:L(e,`Unexpected column type: ${e}`)}}function _c(e,t,r){if(!t.every(n=>Do(e,n))){let n=Tg(e,r),i=Ag(r);throw new Ae(n,i,r.context)}}function Do(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:L(t,`Unknown rule type: ${t.type}`)}}function Tg(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:L(t,`Unknown error identifier: ${t}`)}}function Ag(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:L(e,`Unknown error identifier: ${e}`)}}var kr=class e{#e;#t;#r;#n=new Hn;#o;#i;#s;#a;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s,provider:a}){this.#e=t,this.#t=r,this.#r=n,this.#o=i,this.#i=o,this.#s=s??o,this.#a=a}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:Oc,rawSerializer:Dc,provider:t.provider})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#t,this.#n.snapshot(r.provider)).catch(i=>Pl(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:we(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!Nc(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(Ir(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>Ce(s)+Ce(a)):0}}case"execute":{let o=Oo(t.args,n,i);return this.#l(o,r,async()=>({value:await r.executeRaw(o)}))}case"query":{let o=Oo(t.args,n,i);return this.#l(o,r,async()=>{let s=await r.queryRaw(o);return t.args.type==="rawSql"?{value:this.#s(s),lastInsertId:s.lastInsertId}:{value:this.#i(s),lastInsertId:s.lastInsertId}})}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(Nc(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:Fc(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async l=>({joinExpr:l,childRecords:(await this.interpretNode(l.child,r,n,i)).value})));return{value:Cg(o,a),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let l=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),l}catch(l){throw await o.rollbackTransaction(s.id),l}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:bl(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return _c(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return Do(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(Ir(s));return{value:Ir(o).filter(l=>!a.has(l))}}case"distinctBy":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=new Set,l=[];for(let c of Ir(o)){let u=Gn(c,t.args.fields);a.has(u)||(a.add(u),l.push(c))}return{value:l,lastInsertId:s}}case"paginate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=Ir(o),l=t.args.pagination.linkingFields;if(l!==null){let c=new Map;for(let p of a){let d=Gn(p,l);c.has(d)||c.set(d,[]),c.get(d).push(p)}let u=Array.from(c.entries());return u.sort(([p],[d])=>p<d?-1:p>d?1:0),{value:u.flatMap(([,p])=>Mc(p,t.args.pagination)),lastInsertId:s}}return{value:Mc(a,t.args.pagination),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,l]of Object.entries(t.args.fields))s[a]=Sg(l,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:_o(o);for(let[l,c]of Object.entries(t.args.fields))a[l]=Rg(c,a[l],n,i);return{value:a,lastInsertId:s}}default:L(t,`Unexpected node type: ${t.type}`)}}#l(t,r,n){return Ln({query:t,execute:n,provider:this.#a??r.provider,tracingHelper:this.#o,onQuery:this.#r})}};function Nc(e){return Array.isArray(e)?e.length===0:e==null}function Ir(e){return Array.isArray(e)?e:[e]}function Ce(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function _o(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function Fc(e,t){return Array.isArray(e)?e.map(r=>Fc(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function Cg(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let l=_o(a),c=Gn(l,i);s[c]||(s[c]=[]),s[c].push(l),r.isRelationUnique?l[r.parentField]=null:l[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let l=Gn(_o(a),o);for(let c of s[l]??[])r.isRelationUnique?c[r.parentField]=a:c[r.parentField].push(a)}}return e}function Mc(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>Er(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function Gn(e,t){return JSON.stringify(t.map(r=>e[r]))}function Sg(e,t,r,n){switch(e.type){case"value":return we(e.value,r,n);case"lastInsertId":return t;default:L(e,`Unexpected field initializer type: ${e.type}`)}}function Rg(e,t,r,n){switch(e.type){case"set":return we(e.value,r,n);case"add":return Ce(t)+Ce(we(e.value,r,n));case"subtract":return Ce(t)-Ce(we(e.value,r,n));case"multiply":return Ce(t)*Ce(we(e.value,r,n));case"divide":{let i=Ce(t),o=Ce(we(e.value,r,n));return o===0?null:i/o}default:L(e,`Unexpected field operation type: ${e.type}`)}}async function Ig(){return globalThis.crypto??await import("node:crypto")}async function Lc(){return(await Ig()).randomUUID()}var de=class extends Ae{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Or=class extends de{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Wn=class extends de{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Jn=class extends de{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Kn=class extends de{constructor(){super("Unable to start a transaction in the given time.")}},zn=class extends de{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},Nt=class extends de{constructor(t){super(`Internal Consistency Error: ${t}`)}},Yn=class extends de{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var kg=100,Dr=V("prisma:client:transactionManager"),Og=()=>({sql:"COMMIT",args:[],argTypes:[]}),Dg=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),_g=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),Ng=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),_r=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;#t;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i,provider:o}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i,this.#t=o}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await Lc(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n);let i=setTimeout(()=>n.status="timed_out",r.maxWait);switch(n.transaction=await this.driverAdapter.startTransaction(r.isolationLevel),clearTimeout(i),n.status){case"waiting":return n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"timed_out":throw await this.closeTransaction(n,"timed_out"),new Kn;case"running":case"committed":case"rolled_back":throw new Nt(`Transaction in invalid state ${n.status} although it just finished startup.`);default:L(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Or;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Dr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"waiting":case"running":throw new Nt("Active transaction found in closed transactions list.");case"committed":throw new Wn(r);case"rolled_back":throw new Jn(r);case"timed_out":throw new zn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Dr("Transaction not found.",t),new Or}if(["committed","rolled_back","timed_out"].includes(n.status))throw new Nt("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Dr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Dr("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){Dr("Closing transaction.",{transactionId:t.id,status:r}),t.status=r;try{if(t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#n(_g(),t.transaction,()=>t.transaction.commit());else{let n=Og();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.commit()}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#n(Ng(),t.transaction,()=>t.transaction.rollback());else{let n=Dg();await this.#n(n,t.transaction,()=>t.transaction.executeRaw(n)),await t.transaction.rollback()}}finally{clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>kg&&this.closedTransactions.shift()}}validateOptions(t){if(!t.timeout)throw new de("timeout is required");if(!t.maxWait)throw new de("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new Yn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#n(t,r,n){return Ln({query:t,execute:n,provider:this.#t??r.provider,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var Zn="6.12.0";var Xn=class e{#e;#t;#r;constructor(t,r,n){this.#e=t,this.#t=r,this.#r=n}static async connect(t){let r,n;try{r=await t.driverAdapterFactory.connect(),n=new _r({driverAdapter:r,transactionOptions:t.transactionOptions,tracingHelper:t.tracingHelper,onQuery:t.onQuery,provider:t.provider})}catch(i){throw await r?.dispose(),i}return new e(t,r,n)}getConnectionInfo(){let t=this.#t.getConnectionInfo?.()??{supportsRelationJoins:!1};return Promise.resolve({provider:this.#t.provider,connectionInfo:t})}async execute({plan:t,placeholderValues:r,transaction:n,batchIndex:i}){let o=n?this.#r.getTransaction(n,i!==void 0?"batch query":"query"):this.#t;return await kr.forSql({transactionManager:n?{enabled:!1}:{enabled:!0,manager:this.#r},placeholderValues:r,onQuery:this.#e.onQuery,tracingHelper:this.#e.tracingHelper,provider:this.#e.provider}).run(t,o)}async startTransaction(t){return{...await this.#r.startTransaction(t),payload:void 0}}async commitTransaction(t){await this.#r.commitTransaction(t.id)}async rollbackTransaction(t){await this.#r.rollbackTransaction(t.id)}async disconnect(){try{await this.#r.cancelAllTransactions()}finally{await this.#t.dispose()}}};var ei=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function $c(e,t,r){let n=r||{},i=n.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!ei.test(e))throw new TypeError("argument name is invalid");let o=i(t);if(o&&!ei.test(o))throw new TypeError("argument val is invalid");let s=e+"="+o;if(n.maxAge!==void 0&&n.maxAge!==null){let a=n.maxAge-0;if(Number.isNaN(a)||!Number.isFinite(a))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(a)}if(n.domain){if(!ei.test(n.domain))throw new TypeError("option domain is invalid");s+="; Domain="+n.domain}if(n.path){if(!ei.test(n.path))throw new TypeError("option path is invalid");s+="; Path="+n.path}if(n.expires){if(!Fg(n.expires)||Number.isNaN(n.expires.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():n.priority){case"low":{s+="; Priority=Low";break}case"medium":{s+="; Priority=Medium";break}case"high":{s+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:{s+="; SameSite=Strict";break}case"lax":{s+="; SameSite=Lax";break}case"strict":{s+="; SameSite=Strict";break}case"none":{s+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return n.partitioned&&(s+="; Partitioned"),s}function Fg(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function Vc(e,t){let r=(e||"").split(";").filter(l=>typeof l=="string"&&!!l.trim()),n=r.shift()||"",i=Lg(n),o=i.name,s=i.value;try{s=t?.decode===!1?s:(t?.decode||decodeURIComponent)(s)}catch{}let a={name:o,value:s};for(let l of r){let c=l.split("="),u=(c.shift()||"").trimStart().toLowerCase(),p=c.join("=");switch(u){case"expires":{a.expires=new Date(p);break}case"max-age":{a.maxAge=Number.parseInt(p,10);break}case"secure":{a.secure=!0;break}case"httponly":{a.httpOnly=!0;break}case"samesite":{a.sameSite=p;break}default:a[u]=p}}return a}function Lg(e){let t="",r="",n=e.split("=");return n.length>1?(t=n.shift(),r=n.join("=")):r=e,{name:t,value:r}}function Mt({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new O(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new O("error: Missing URL environment variable, value, or override.",n);return i}var ti=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var ce=class extends ti{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};function R(e,t){return{...e,isRetryable:t}}var it=class extends ce{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,R(r,!1))}};b(it,"InvalidDatasourceError");function ri(e){let t={clientVersion:e.clientVersion},r=Object.keys(e.inlineDatasources)[0],n=Mt({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,clientVersion:e.clientVersion,env:{...e.env,...typeof process<"u"?process.env:{}}}),i;try{i=new URL(n)}catch{throw new it(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==Xr)throw new it(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new it(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);let l=Ei(i)?"http:":"https:",c=new URL(i.href.replace(o,l));return{apiKey:a,url:c}}var qc=j(us()),Ft=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,transactionId:r}={}){let n={Accept:"application/json",Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","Prisma-Engine-Hash":this.engineHash,"Prisma-Engine-Version":qc.enginesVersion};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-Transaction-Id"]=r);let i=this.#e();return i.length>0&&(n["X-Capture-Telemetry"]=i.join(", ")),n}#e(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}};function $g(e){return e[0]*1e3+e[1]/1e6}function Lt(e){return new Date($g(e))}var Uc=V("prisma:client:clientEngine:remoteExecutor"),ni=class{#e;#t;#r;#n;#o;constructor(t){this.#e=t.clientVersion,this.#n=t.logEmitter,this.#o=t.tracingHelper;let{url:r,apiKey:n}=ri({clientVersion:t.clientVersion,env:t.env,inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources});this.#r=new No(r),this.#t=new Ft({apiKey:n,engineHash:t.clientVersion,logLevel:t.logLevel,logQueries:t.logQueries,tracingHelper:t.tracingHelper})}async getConnectionInfo(){return await this.#i({path:"/connection-info",method:"GET"})}async execute({plan:t,placeholderValues:r,batchIndex:n,model:i,operation:o,transaction:s,customFetch:a}){return(await this.#i({path:s?`/transaction/${s.id}/query`:"/query",method:"POST",body:{model:i,operation:o,plan:t,params:r},batchRequestIdx:n,fetch:a})).data}async startTransaction(t){return{...await this.#i({path:"/transaction/start",method:"POST",body:t}),payload:void 0}}async commitTransaction(t){await this.#i({path:`/transaction/${t.id}/commit`,method:"POST"})}async rollbackTransaction(t){await this.#i({path:`/transaction/${t.id}/rollback`,method:"POST"})}disconnect(){return Promise.resolve()}async#i({path:t,method:r,body:n,fetch:i=globalThis.fetch,batchRequestIdx:o}){let s=await this.#r.request({method:r,path:t,headers:this.#t.build(),body:n,fetch:i});s.ok||await this.#s(s,o);let a=await s.json();return typeof a.extensions=="object"&&a.extensions!==null&&this.#a(a.extensions),a}async#s(t,r){let n=t.headers.get("Prisma-Error-Code"),i=await t.text(),o,s=i;try{o=JSON.parse(i)}catch{o={}}typeof o.code=="string"&&(n=o.code),typeof o.error=="string"?s=o.error:typeof o.message=="string"?s=o.message:typeof o.InvalidRequestError=="object"&&o.InvalidRequestError!==null&&typeof o.InvalidRequestError.reason=="string"&&(s=o.InvalidRequestError.reason),s=s||`HTTP ${t.status}: ${t.statusText}`;let a=typeof o.meta=="object"&&o.meta!==null?o.meta:o;throw new U(s,{clientVersion:this.#e,code:n??"P6000",batchRequestIdx:r,meta:a})}#a(t){if(t.logs)for(let r of t.logs)this.#l(r);t.traces&&this.#o.dispatchEngineSpans(t.traces)}#l(t){switch(t.level){case"debug":case"trace":Uc(t);break;case"error":case"warn":case"info":{this.#n.emit(t.level,{timestamp:Lt(t.timestamp),message:t.attributes.message??"",target:t.target});break}case"query":{this.#n.emit("query",{query:t.attributes.query??"",timestamp:Lt(t.timestamp),duration:t.attributes.duration_ms??0,params:t.attributes.params??"",target:t.target});break}default:throw new Error(`Unexpected log level: ${t.level}`)}}},No=class{#e;#t;#r;constructor(t){this.#e=t,this.#t=new Map}async request({method:t,path:r,headers:n,body:i,fetch:o}){let s=new URL(r,this.#e),a=this.#n(s);a&&(n.Cookie=a),this.#r&&(n["Accelerate-Query-Engine-Jwt"]=this.#r);let l=await o(s,{method:t,body:i!==void 0?JSON.stringify(i):void 0,headers:n});return Uc(t,s,l.status,l.statusText),this.#r=l.headers.get("Accelerate-Query-Engine-Jwt")??void 0,this.#o(s,l),l}#n(t){let r=[],n=new Date;for(let[i,o]of this.#t){if(o.expires&&o.expires<n){this.#t.delete(i);continue}let s=o.domain??t.hostname,a=o.path??"/";t.hostname.endsWith(s)&&t.pathname.startsWith(a)&&r.push($c(o.name,o.value))}return r.length>0?r.join("; "):void 0}#o(t,r){let n=r.headers.getSetCookie?.()||[];if(n.length===0){let i=r.headers.get("Set-Cookie");i&&n.push(i)}for(let i of n){let o=Vc(i),s=o.domain??t.hostname,a=o.path??"/",l=`${s}:${a}:${o.name}`;this.#t.set(l,{name:o.name,value:o.value,domain:s,path:a,expires:o.expires})}}};var Mo,jc={async loadQueryCompiler(e){let{clientVersion:t,compilerWasm:r}=e;if(r===void 0)throw new O("WASM query compiler was unexpectedly `undefined`",t);return Mo===void 0&&(Mo=(async()=>{let n=await r.getRuntime(),i=await r.getQueryCompilerWasmModule();if(i==null)throw new O("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let o={"./query_compiler_bg.js":n},s=new WebAssembly.Instance(i,o),a=s.exports.__wbindgen_start;return n.__wbg_set_wasm(s.exports),a(),n.QueryCompiler})()),await Mo}};var Bc="P2038",Nr=V("prisma:client:clientEngine"),Hc=globalThis;Hc.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new ne(e,Zn)}};var $t=class{name="ClientEngine";#e;#t={type:"disconnected"};#r;#n;config;datamodel;logEmitter;logQueries;logLevel;tracingHelper;#o;constructor(t,r,n){if(!t.previewFeatures?.includes("driverAdapters")&&!r)throw new O("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,Bc);if(r)this.#n={remote:!0};else if(t.adapter)this.#n={remote:!1,driverAdapterFactory:t.adapter},Nr("Using driver adapter: %O",t.adapter);else throw new O("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,Bc);this.#r=n??jc,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#o=i=>{this.logEmitter.emit("query",{...i,params:xr(i.params),target:"ClientEngine"})})}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async#i(){switch(this.#t.type){case"disconnected":{let t=this.tracingHelper.runInChildSpan("connect",async()=>{let r,n;try{r=await this.#s(),n=await this.#a(r)}catch(o){throw this.#t={type:"disconnected"},n?.free(),await r?.disconnect(),o}let i={executor:r,queryCompiler:n};return this.#t={type:"connected",engine:i},i});return this.#t={type:"connecting",promise:t},await t}case"connecting":return await this.#t.promise;case"connected":return this.#t.engine;case"disconnecting":return await this.#t.promise,await this.#i()}}async#s(){return this.#n.remote?new ni({clientVersion:this.config.clientVersion,env:this.config.env,inlineDatasources:this.config.inlineDatasources,logEmitter:this.logEmitter,logLevel:this.logLevel,logQueries:this.logQueries,overrideDatasources:this.config.overrideDatasources,tracingHelper:this.tracingHelper}):await Xn.connect({driverAdapterFactory:this.#n.driverAdapterFactory,tracingHelper:this.tracingHelper,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#d(this.config.transactionOptions.isolationLevel)},onQuery:this.#o,provider:this.config.activeProvider})}async#a(t){let r=this.#e;r===void 0&&(r=await this.#r.loadQueryCompiler(this.config),this.#e=r);let{provider:n,connectionInfo:i}=await t.getConnectionInfo();try{return this.#p(()=>new r({datamodel:this.datamodel,provider:n,connectionInfo:i}),void 0,!1)}catch(o){throw this.#l(o)}}#l(t){if(t instanceof ne)return t;try{let r=JSON.parse(t.message);return new O(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#c(t,r){if(t instanceof O)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new ne(Qc(this,t.message,r),this.config.clientVersion);if(t instanceof Ae)return new U(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let n=JSON.parse(t);return new X(`${n.message}
${n.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#u(t){return t instanceof ne?t:typeof t.message=="string"&&typeof t.code=="string"?new U(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#p(t,r,n=!0){let i=Hc.PRISMA_WASM_PANIC_REGISTRY.set_message,o;global.PRISMA_WASM_PANIC_REGISTRY.set_message=s=>{o=s};try{return t()}finally{if(global.PRISMA_WASM_PANIC_REGISTRY.set_message=i,o)throw this.#e=void 0,n&&this.stop().catch(s=>Nr("failed to disconnect:",s)),new ne(Qc(this,o,r),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.#i()}async stop(){switch(this.#t.type){case"disconnected":return;case"connecting":return await this.#t.promise,await this.stop();case"connected":{let t=this.#t.engine,r=this.tracingHelper.runInChildSpan("disconnect",async()=>{try{await t.executor.disconnect(),t.queryCompiler.free()}finally{this.#t={type:"disconnected"}}});return this.#t={type:"disconnecting",promise:r},await r}case"disconnecting":return await this.#t.promise}}version(){return"unknown"}async transaction(t,r,n){let i,{executor:o}=await this.#i();try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#d(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s)}else fe(t,"Invalid transaction action.")}catch(s){throw this.#c(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{interactiveTransaction:r,customDataProxyFetch:n}){Nr("sending request");let i=JSON.stringify(t),{executor:o,queryCompiler:s}=await this.#i().catch(l=>{throw this.#c(l,i)}),a;try{a=this.#p(()=>s.compile(i),i)}catch(l){throw this.#u(l)}try{Nr("query plan created",a);let l={},c=await o.execute({plan:a,model:t.modelName,operation:t.action,placeholderValues:l,transaction:r,batchIndex:void 0,customFetch:n?.(globalThis.fetch)});return Nr("query plan executed"),{data:{[t.action]:c}}}catch(l){throw this.#c(l,i)}}async requestBatch(t,{transaction:r,customDataProxyFetch:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(Rt(t,r)),{executor:s,queryCompiler:a}=await this.#i().catch(c=>{throw this.#c(c,o)}),l;try{l=a.compileBatch(o)}catch(c){throw this.#u(c)}try{let c;if(r?.kind==="itx")c=r.options;else{let d=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;c=await this.transaction("start",{},d)}let u={},p=[];switch(l.type){case"multi":{p=await Promise.all(l.plans.map((d,m)=>s.execute({plan:d,placeholderValues:u,model:t[m].modelName,operation:t[m].action,batchIndex:m,transaction:c,customFetch:n?.(globalThis.fetch)}).then(g=>({data:{[t[m].action]:g}}),g=>g)));break}case"compacted":{if(!t.every(m=>m.action===i))throw new Error("All queries in a batch must have the same action");let d=await s.execute({plan:l.plan,placeholderValues:u,model:t[0].modelName,operation:i,batchIndex:void 0,transaction:c,customFetch:n?.(globalThis.fetch)});p=this.#m(d,l,i);break}}return r?.kind!=="itx"&&await this.transaction("commit",{},c),p}catch(c){throw this.#c(c,o)}}metrics(t){throw new Error("Method not implemented.")}#m(t,r,n){let i=t.map(s=>r.keys.reduce((a,l)=>(a[l]=qe(s[l]),a),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let a=i.findIndex(l=>Er(l,s));if(a===-1)return r.expectNonEmpty?new U("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let l=Object.entries(t[a]).filter(([c])=>o.has(c));return{data:{[n]:Object.fromEntries(l)}}}})}#d(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new U(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function Qc(e,t,r){return ml({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:r})}var Vt=class extends ce{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",R(t,!0))}};b(Vt,"ForcedRetryError");var ot=class extends ce{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,R(r,!1))}};b(ot,"NotImplementedYetError");var F=class extends ce{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var st=class extends F{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",R(t,!0))}};b(st,"SchemaMissingError");var Fo="This request could not be understood by the server",Mr=class extends F{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||Fo,R(t,!1)),n&&(this.code=n)}};b(Mr,"BadRequestError");var Fr=class extends F{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",R(t,!0)),this.logs=r}};b(Fr,"HealthcheckTimeoutError");var Lr=class extends F{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,R(t,!0)),this.logs=n}};b(Lr,"EngineStartupError");var $r=class extends F{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",R(t,!1))}};b($r,"EngineVersionNotSupportedError");var Lo="Request timed out",Vr=class extends F{name="GatewayTimeoutError";code="P5009";constructor(t,r=Lo){super(r,R(t,!1))}};b(Vr,"GatewayTimeoutError");var Vg="Interactive transaction error",qr=class extends F{name="InteractiveTransactionError";code="P5015";constructor(t,r=Vg){super(r,R(t,!1))}};b(qr,"InteractiveTransactionError");var qg="Request parameters are invalid",Ur=class extends F{name="InvalidRequestError";code="P5011";constructor(t,r=qg){super(r,R(t,!1))}};b(Ur,"InvalidRequestError");var $o="Requested resource does not exist",jr=class extends F{name="NotFoundError";code="P5003";constructor(t,r=$o){super(r,R(t,!1))}};b(jr,"NotFoundError");var Vo="Unknown server error",qt=class extends F{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||Vo,R(t,!0)),this.logs=n}};b(qt,"ServerError");var qo="Unauthorized, check your connection string",Br=class extends F{name="UnauthorizedError";code="P5007";constructor(t,r=qo){super(r,R(t,!1))}};b(Br,"UnauthorizedError");var Uo="Usage exceeded, retry again later",Qr=class extends F{name="UsageExceededError";code="P5008";constructor(t,r=Uo){super(r,R(t,!0))}};b(Qr,"UsageExceededError");async function Ug(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Hr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await Ug(e);if(n.type==="QueryEngineError")throw new U(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new qt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new st(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new $r(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Lr(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new O(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Fr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new qr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Ur(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Br(r,Ut(qo,n));if(e.status===404)return new jr(r,Ut($o,n));if(e.status===429)throw new Qr(r,Ut(Uo,n));if(e.status===504)throw new Vr(r,Ut(Lo,n));if(e.status>=500)throw new qt(r,Ut(Vo,n));if(e.status>=400)throw new Mr(r,Ut(Fo,n))}function Ut(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function Gc(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var Me="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Wc(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,l,c,u;for(let p=0;p<o;p=p+3)u=t[p]<<16|t[p+1]<<8|t[p+2],s=(u&16515072)>>18,a=(u&258048)>>12,l=(u&4032)>>6,c=u&63,r+=Me[s]+Me[a]+Me[l]+Me[c];return i==1?(u=t[o],s=(u&252)>>2,a=(u&3)<<4,r+=Me[s]+Me[a]+"=="):i==2&&(u=t[o]<<8|t[o+1],s=(u&64512)>>10,a=(u&1008)>>4,l=(u&15)<<2,r+=Me[s]+Me[a]+Me[l]+"="),r}function Jc(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new O("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}var Kc={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.12.0-15.8047c96bbd92db98a2abc7c9323ce77c02c89dbc","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var Gr=class extends ce{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,R(r,!0))}};b(Gr,"RequestError");async function at(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new Gr(a,{clientVersion:n,cause:s})}}var Bg=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,zc=V("prisma:client:dataproxyEngine");async function Qg(e,t){let r=Kc["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&Bg.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,l,c]=s.split("."),u=Hg(`<=${a}.${l}.${c}`),p=await at(u,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let d=await p.text();zc("length of body fetched from unpkg.com",d.length);let m;try{m=JSON.parse(d)}catch(g){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),g}return m.version}throw new ot("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function Yc(e,t){let r=await Qg(e,t);return zc("version",r),r}function Hg(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var Zc=3,Wr=V("prisma:client:dataproxyEngine"),Jr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){Jc(t),this.config=t,this.env=t.env,this.inlineSchema=Wc(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.protocol=r.protocol,this.headerBuilder=new Ft({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel??"error",logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await Yc(this.host,this.config),Wr("host",this.host),Wr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Wr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Lt(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Lt(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await at(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Wr("schema response status",r.status);let n=await Hr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Rt(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(l=>(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l?this.convertProtocolErrorsToClientError(l.errors):l))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await at(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,transactionId:i?.id}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||Wr("graphql response status",a.status),await this.handleError(await Hr(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await at(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Hr(l,this.clientVersion));let c=await l.json(),{extensions:u}=c;u&&this.propagateResponseExtensions(u);let p=c.id,d=c["data-proxy"].endpoint;return{id:p,payload:{endpoint:d}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await at(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Hr(a,this.clientVersion));let l=await a.json(),{extensions:c}=l;c&&this.propagateResponseExtensions(c);return}}})}getURLAndAPIKey(){return ri({clientVersion:this.clientVersion,env:this.env,inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources})}metrics(){throw new ot("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof ce)||!i.isRetryable)throw i;if(r>=Zc)throw i instanceof Vt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${Zc} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Gc(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof st)throw await this.uploadSchema(),new Vt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Nn(t[0],this.config.clientVersion,this.config.activeProvider):new X(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function Xc({url:e,adapter:t,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=h=>{i.push({_tag:"warning",value:h})},a=h=>{let C=h.join(`
`);o.push({_tag:"error",value:C})},l=!!e?.startsWith("prisma://"),c=en(e),u=!!t,p=l||c;!u&&r&&p&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let d=p||!r;u&&(d||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):r?l&&a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]):a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let m={accelerate:d,ppg:c,driverAdapters:u};function g(h){return h.length>0}return g(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:m}:{ok:!0,diagnostics:{warnings:i},isUsing:m}}function eu({copyEngine:e=!0},t){let r;try{r=Mt({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=Xc({url:r,adapter:t.adapter,copyEngine:e,targetBuildType:"client"});for(let p of o.warnings)er(...p.value);if(!n){let p=o.errors[0];throw new ee(p.value,{clientVersion:t.clientVersion})}let s=pt(t.generator),a=s==="library",l=s==="binary",c=s==="client",u=(i.accelerate||i.ppg)&&!i.driverAdapters;return c?new $t(t,u):i.accelerate?new Jr(t):(i.driverAdapters,i.accelerate,new $t(t,u))}function ii({generator:e}){return e?.previewFeatures??[]}var tu=e=>({command:e});var ru=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function jt(e){try{return nu(e,"fast")}catch{return nu(e,"slow")}}function nu(e,t){return JSON.stringify(e.map(r=>ou(r,t)))}function ou(e,t){if(Array.isArray(e))return e.map(r=>ou(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(ht(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(K.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(Buffer.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(Gg(e))return{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:Buffer.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?su(e):e}function Gg(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function su(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(iu);let t={};for(let r of Object.keys(e))t[r]=iu(e[r]);return t}function iu(e){return typeof e=="bigint"?e.toString():su(e)}var Wg=/^(\s*alter\s)/i,au=V("prisma:client");function jo(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&Wg.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var Bo=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(On(r))n=r.sql,i={values:jt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:jt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:jt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:jt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=ru(r),i={values:jt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?au(`prisma.${e}(${n}, ${i.values})`):au(`prisma.${e}(${n})`),{query:n,parameters:i}},lu={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new le(t,r)}},cu={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function Qo(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=uu(r(s)):uu(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function uu(e){return typeof e.then=="function"?e:Promise.resolve(e)}var Jg=hi.split(".")[0],Kg={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Ho=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${Jg}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??Kg}};function pu(){return new Ho}function du(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function mu(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var oi=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};var gu=j(bi());function si(e){return typeof e.batchRequestIdx=="number"}function fu(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(Go(e.query.arguments)),t.push(Go(e.query.selection)),t.join("")}function Go(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${Go(n)})`:r}).join(" ")})`}var zg={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Wo(e){return zg[e]}var ai=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function lt(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new K(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>lt("bigint",r));case"bytes-array":return t.map(r=>lt("bytes",r));case"decimal-array":return t.map(r=>lt("decimal",r));case"datetime-array":return t.map(r=>lt("datetime",r));case"date-array":return t.map(r=>lt("date",r));case"time-array":return t.map(r=>lt("time",r));default:return t}}function li(e){let t=[],r=Yg(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=lt(e.types[s],i[s]);t.push(o)}return t}function Yg(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var Zg=V("prisma:client:request_handler"),ci=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new ai({batchLoader:tl(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),c=n.some(p=>Wo(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:Xg(o),containsWrite:c,customDataProxyFetch:i})).map((p,d)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[d],p)}catch(m){return m}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?hu(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Wo(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:fu(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return process.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(Zg(t),eh(t,i))throw t;if(t instanceof U&&th(t)){let c=yu(t.meta);An({args:o,errors:[c],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let l=t.message;if(n&&(l=gn({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:l})),l=this.sanitizeMessage(l),t.code){let c=s?{modelName:s,...t.meta}:t.meta;throw new U(l,{code:t.code,clientVersion:this.client._clientVersion,meta:c,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new ne(l,this.client._clientVersion);if(t instanceof X)throw new X(l,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof O)throw new O(l,this.client._clientVersion);if(t instanceof ne)throw new ne(l,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,gu.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(c=>c!=="select"&&c!=="include"),a=Ji(o,s),l=i==="queryRaw"?li(a):qe(a);return n?n(l):l}get[Symbol.toStringTag](){return"RequestHandler"}};function Xg(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:hu(e)};fe(e,"Unknown transaction kind")}}function hu(e){return{id:e.id,payload:e.payload}}function eh(e,t){return si(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function th(e){return e.code==="P2009"||e.code==="P2012"}function yu(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(yu)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}var wu=Zn;var vu=j(Mi());var D=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};b(D,"PrismaClientConstructorValidationError");var Eu=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],xu=["pretty","colorless","minimal"],bu=["info","query","warn","error"],rh={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new D(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Bt(r,t)||` Available datasources: ${t.join(", ")}`;throw new D(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new D(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new D(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new D(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&pt(t.generator)==="client")throw new D('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new D('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!ii(t).includes("driverAdapters"))throw new D('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(pt(t.generator)==="binary")throw new D('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new D(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new D(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!xu.includes(e)){let t=Bt(e,xu);throw new D(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new D(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!bu.includes(r)){let n=Bt(r,bu);throw new D(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Bt(i,o);throw new D(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new D(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new D(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new D(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new D('"omit" option is expected to be an object.');if(e===null)throw new D('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=ih(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let l=o.fields.find(c=>c.name===s);if(!l){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(l.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new D(oh(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new D(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Bt(r,t);throw new D(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Tu(e,t){for(let[r,n]of Object.entries(e)){if(!Eu.includes(r)){let i=Bt(r,Eu);throw new D(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}rh[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new D('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Bt(e,t){if(t.length===0||typeof e!="string")return"";let r=nh(e,t);return r?` Did you mean "${r}"?`:""}function nh(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,vu.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function ih(e,t){return Pu(t.models,e)??Pu(t.types,e)}function Pu(e,t){let r=Object.keys(e).find(n=>Ue(n)===t);if(r)return e[r]}function oh(e,t){let r=Tt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=Tn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}function Au(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=c=>{o||(o=!0,r(c))};for(let c=0;c<e.length;c++)e[c].then(u=>{n[c]=u,a()},u=>{if(!si(u)){l(u);return}u.batchRequestIdx===c?l(u):(i||(i=u),a())})})}var We=V("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var sh={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},ah=Symbol.for("prisma.client.transaction.id"),lh={id:0,nextId(){return++this.id}};function Ou(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new oi;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=Qo();constructor(n){e=n?.__internal?.configOverride?.(e)??e,sl(e),n&&Tu(n,e);let i=new Iu.EventEmitter().on("error",()=>{});this._extensions=At.empty(),this._previewFeatures=ii(e),this._clientVersion=e.clientVersion??wu,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=pu();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&ui.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&ui.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let l=e.activeProvider==="postgresql"||e.activeProvider==="cockroachdb"?"postgres":e.activeProvider;if(s.provider!==l)throw new O(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new O("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=!s&&o&&Xt(o,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},c=l.__internal??{},u=c.debug===!0;u&&V.enable("prisma:client");let p=ui.default.resolve(e.dirname,e.relativePath);ku.default.existsSync(p)||(p=e.dirname),We("dirname",e.dirname),We("relativePath",e.relativePath),We("cwd",p);let d=c.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:d.allowTriggerPanic,prismaPath:d.binaryPath??void 0,engineEndpoint:d.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&mu(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(m=>typeof m=="string"?m==="query":m.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:al(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Mt,getBatchRequestPayload:Rt,prismaGraphQLToJSError:Nn,PrismaClientUnknownRequestError:X,PrismaClientInitializationError:O,PrismaClientKnownRequestError:U,debug:V("prisma:client:accelerateEngine"),engineVersion:Su.version,clientVersion:e.clientVersion}},We("clientVersion",e.clientVersion),this._engine=eu(e,this._engineConfig),this._requestHandler=new ci(this,i),l.log)for(let m of l.log){let g=typeof m=="string"?m:m.emit==="stdout"?m.level:null;g&&this.$on(g,h=>{zt.log(`${zt.tags[g]??""}`,h.message||h.query)})}}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=yr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{as()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:Bo({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Cu(n,i);return jo(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new ee("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(jo(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new ee(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:tu,callsite:Be(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:Bo({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Cu(n,i));throw new ee("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new ee("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=lh.nextId(),s=du(n.length),a=n.map((l,c)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let u=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:c,isolationLevel:u,lock:s};return l.requestTransaction?.(p)??l});return Au(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let c={kind:"itx",...a};l=await n(this._createItxClient(c)),await this._engine.transaction("commit",o,a)}catch(c){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),c}return l}_createItxClient(n){return ye(yr(ye(Ha(this),[ie("_appliedParent",()=>this._appliedParent._createItxClient(n)),ie("_createPrismaPromise",()=>Qo(n)),ie(ah,()=>n.id)])),[St(za)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??sh,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,l=async c=>{let u=this._middlewares.get(++a);if(u)return this._tracingHelper.runInChildSpan(s.middleware,C=>u(c,A=>(C?.end(),l(A))));let{runInTransaction:p,args:d,...m}=c,g={...n,...m};d&&(g.args=i.middlewareArgsToRequestArgs(d)),n.transaction!==void 0&&p===!1&&delete g.transaction;let h=await el(this,g);return g.model?Ka({result:h,modelName:g.model,args:g.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):h};return this._tracingHelper.runInChildSpan(s.operation,()=>new Ru.AsyncResource("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:c,transaction:u,unpacker:p,otelParentCtx:d,customDataProxyFetch:m}){try{n=c?c(n):n;let g={name:"serialize"},h=this._tracingHelper.runInChildSpan(g,()=>In({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return V.enabled("prisma:client")&&(We("Prisma Client call:"),We(`prisma.${i}(${Na(n)})`),We("Generated request:"),We(JSON.stringify(h,null,2)+`
`)),u?.kind==="batch"&&await u.lock,this._requestHandler.request({protocolQuery:h,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:u,unpacker:p,otelParentCtx:d,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:m})}catch(g){throw g.clientVersion=this._clientVersion,g}}$metrics=new Ct(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=Ga}return t}function Cu(e,t){return ch(e)?[new le(e,t),lu]:[e,cu]}function ch(e){return Array.isArray(e)&&Array.isArray(e.raw)}var uh=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Du(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!uh.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}function _u(e){Xt(e,{conflictCheck:"warn"})}0&&(module.exports={DMMF,Debug,Decimal,Extensions,MetricsClient,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,createParam,defineDmmfProperty,deserializeJsonResponse,deserializeRawResult,dmmfToRuntimeDataModel,empty,getPrismaClient,getRuntime,join,makeStrictEnum,makeTypedQueryFactory,objectEnumValues,raw,serializeJsonQuery,skip,sqltag,warnEnvConflicts,warnOnce});
/*! Bundled license information:

@noble/hashes/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=client.js.map
